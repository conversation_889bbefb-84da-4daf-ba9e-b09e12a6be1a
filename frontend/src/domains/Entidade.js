import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class Entidade extends DomainBase {
  @observable id;
  @observable anoInicio;
  @observable anoReferencia;
  @observable codigo;
  @observable dataInativo;
  @observable entidadeCjur;
  @observable entidadeExecutivo;
  @observable idFms;
  @observable idFundeb;
  @observable idRgf;
  @observable inativo;
  @observable mesInicio;
  @observable mesReferencia;
  @observable nome;
  @observable planejamento;
  @observable rgf;
  @observable rreo;
  @observable classificacaoAdministrativa;
  @observable ente;
  @observable poder;
  @observable esfera;

  static getDomainAttributes() {
    return [
      'id',
      'anoInicio',
      'anoReferencia',
      'codigo',
      'dataInativo',
      'entidadeCjur',
      'entidadeExecutivo',
      'idFms',
      'idFundeb',
      'idRgf',
      'inativo',
      'mesInicio',
      'mesReferencia',
      'nome',
      'planejamento',
      'rgf',
      'rreo',
      'classificacaoAdministrativa',
      'ente',
      'poder',
      'esfera',
    ];
  }
}

export default Entidade;
