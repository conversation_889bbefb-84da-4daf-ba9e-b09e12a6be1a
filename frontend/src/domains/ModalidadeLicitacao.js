import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class ModalidadeLicitacao extends DomainBase {
  @observable id;
  @observable nome;
  @observable vigenciaDe;
  @observable vigenciaAte;
  @observable permiteConsorcio;
  @observable legislacao;

  static getDomainAttributes() {
    return ['id', 'nome', 'vigenciaDe', 'vigenciaAte', 'permiteConsorcio', 'legislacao'];
  }
}

export default ModalidadeLicitacao;
