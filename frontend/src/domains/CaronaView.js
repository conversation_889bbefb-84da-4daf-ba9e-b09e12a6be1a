import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class CaronaView extends DomainBase {
  @observable id;
  @observable entidade;
  @observable entidadeOrigem;
  @observable numeroProcessoAdministrativo;
  @observable dataAdesao;
  @observable dataCadastro;
  @observable anoCarona;
  @observable justificativa;
  @observable idResponsavelAdesaoCarona;
  @observable objeto;
  @observable naturezasDoObjeto;
  @observable valor;
  @observable fontesDeRecurso;
  @observable detentores;
  @observable observacoes;
  @observable dataValidadeAta;
  @observable status;
  @observable idRequisicaoModificacao;
  @observable gerenciadorAta;
  @observable entidadeOrigemExterna;
  @observable processoEntidadeOrigem;
  @observable updatedAt;

  static getDomainAttributes() {
    return [
      'id',
      'entidade',
      'entidadeOrigem',
      'numeroProcessoAdministrativo',
      'dataAdesao',
      'dataCadastro',
      'idResponsavelAdesaoCarona',
      'objeto',
      'naturezasDoObjeto',
      'valor',
      'fontesDeRecurso',
      'detentores',
      'observacoes',
      'dataValidadeAta',
      'status',
      'anoCarona',
      'justificativa',
      'idRequisicaoModificacao',
      'updatedAt',
    ];
  }
}

export default CaronaView;
