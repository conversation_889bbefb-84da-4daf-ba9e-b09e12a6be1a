import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class AditivoContrato extends DomainBase {
  @observable id;
  @observable tipo;
  @observable contrato;
  @observable numero;
  @observable dataVigenciaInicial;
  @observable dataVigenciaFinal;
  @observable dataCadastro;
  @observable dataPublicacao;
  @observable valor;
  @observable gestorTitular;
  @observable gestorSuplente;
  @observable fiscalAditivo;
  @observable fiscalSuplente;
  @observable justificativa;
  @observable tipoAlteracao;
  @observable status;

  static getDomainAttributes() {
    return [
      'id',
      'tipo',
      'contrato',
      'numero',
      'dataVigenciaInicial',
      'dataVigenciaFinal',
      'dataCadastro',
      'dataPublicacao',
      'valor',
      'gestorTitular',
      'gestorSuplente',
      'fiscalAditivo',
      'fiscalSuplente',
      'justificativa',
      'tipoAlteracao',
      'status',
    ];
  }
}

export default AditivoContrato;
