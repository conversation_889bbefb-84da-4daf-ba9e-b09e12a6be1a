import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class AlertaAnalise extends DomainBase {
  @observable id;
  @observable data;
  @observable mensagem;
  @observable prazoResposta;
  @observable usuarioAtual;
  @observable usuarioResponsavel;
  @observable diretorDafo;
  @observable jurisdicionadoResposta;
  @observable usuarioRejeicao;
  @observable status;
  @observable destinatarios;
  @observable resposta;
  @observable respostaRejeicao;
  @observable dataRejeicao;
  @observable dataResposta;
  @observable passouPorAuditor;
  @observable passouPorDafo;
  @observable dataArquivamento;
  @observable usuarioArquivamento;
  @observable jurisdicionado;
  @observable dataVisualizacao;
  @observable tdaCarona;
  @observable tdaDispensa;
  @observable tdaInexigibilidade;
  @observable tdaLicitacao;
  @observable tipo;
  @observable mensagens;
  @observable tdaCredenciamento;

  static getDomainAttributes() {
    return [
      'id',
      'data',
      'mensagem',
      'prazoResposta',
      'usuarioAtual',
      'usuarioResponsavel',
      'diretorDafo',
      'conselheiro',
      'jurisdicionadoResposta',
      'usuarioRejeicao',
      'status',
      'destinatarios',
      'resposta',
      'respostaRejeicao',
      'dataRejeicao',
      'dataResposta',
      'passouPorAuditor',
      'passouPorDafo',
      'passouPorConselheiro',
      'dataArquivamento',
      'tipo',
      'usuarioArquivamento',
      'jurisdicionado',
      'dataVisualizacao',
      'tdaCarona',
      'tdaDispensa',
      'tdaInexigibilidade',
      'tdaLicitacao',
      'mensagens',
      'tdaCredenciamento',
    ];
  }
}

export default AlertaAnalise;
