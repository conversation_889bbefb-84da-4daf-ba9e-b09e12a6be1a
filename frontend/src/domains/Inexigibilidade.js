import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class Inexigibilidade extends DomainBase {
  @observable id;
  @observable dataPedido;
  @observable dataCadastro;
  @observable numeroProcesso;
  @observable numeroSei;
  @observable objeto;
  @observable naturezasDoObjeto;
  @observable fontesDeRecurso;
  @observable entidade;
  @observable usuario;
  @observable status;
  @observable justificativa;
  @observable programaTrabalho;
  @observable elementoDespesa;
  @observable fundamentacao;
  @observable fundamentacaoLegalEntidade;
  @observable observacoes;
  @observable fornecedores;
  @observable valor;
  @observable responsavelHomologacao;
  @observable responsavelRatificacao;
  @observable responsavelInexigibilidade;
  @observable statusAnalise;
  @observable emAnalise;
  @observable numeroDownloads;
  @observable usuarioSeparouAnalise;
  @observable houveModificacao;
  @observable camposModificados;
  @observable modificacaoAlerta;
  @observable entidadesSecundárias;
  @observable arquivos;
  @observable checklists;
  @observable statusProcessoArquivado;
  @observable dataAnalise;
  @observable processoMigrado;
  @observable lei;
  @observable legislacaoOutros;
  @observable tipoAdjudicacao;
  @observable anoInexigibilidade;
  @observable numeroOld;
  @observable orgaosParticipantes;
  @observable obra;
  @observable updatedAt;
  @observable updatedAtAudit;
  @observable participacaoExclusiva;

  static getDomainAttributes() {
    return [
      'id',
      'dataPedido',
      'numeroProcesso',
      'anoInexigibilidade',
      'numeroSei',
      'objeto',
      'naturezasDoObjeto',
      'fontesDeRecurso',
      'entidade',
      'usuario',
      'status',
      'justificativa',
      'programaTrabalho',
      'elementoDespesa',
      'observacoes',
      'fundamentacao',
      'fundamentacaoLegalEntidade',
      'fornecedores',
      'valor',
      'responsavelHomologacao',
      'responsavelRatificacao',
      'responsavelInexigibilidade',
      'statusAnalise',
      'emAnalise',
      'numeroDownloads',
      'usuarioSeparouAnalise',
      'houveModificacao',
      'camposModificados',
      'modificacaoAlerta',
      'entidadesSecundárias',
      'arquivos',
      'checklists',
      'statusProcessoArquivado',
      'dataAnalise',
      'processoMigrado',
      'lei',
      'legislacaoOutros',
      'tipoAdjudicacao',
      'orgaosParticipantes',
      'obra',
      'updatedAt',
      'updatedAtAudit',
      'participacaoExclusiva',
    ];
  }
}

export default Inexigibilidade;
