import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class Contrato extends DomainBase {
  @observable id;
  @observable idRequisicaoModificacao;
  @observable numero;
  @observable tipo;
  @observable valorGlobal;
  @observable dataPublicacao;
  @observable entidade;
  @observable usuario;
  @observable contratoLicitante;
  @observable dataVigenciaInicial;
  @observable dataVigenciaFinal;
  @observable programaTrabalho;
  @observable programaTrabalhoDesc;
  @observable objeto;
  @observable formaContrato;
  @observable numeroDoe;
  @observable prazoExecucao;
  @observable gestor;
  @observable gestorSubstituto;
  @observable fiscal;
  @observable fiscalSubstituto;
  @observable dataCadastro;
  @observable observacoes;
  @observable houveModificacao;
  @observable camposModificados;
  @observable idContabil;
  @observable anoContrato;
  @observable permiteAditivo;
  @observable status;
  @observable motivoRescisao;
  @observable outroMotivoRescisao;
  @observable formaExtincao;
  @observable dataAvisoRescisao;
  @observable descricaoRescisao;
  @observable contratoAntigo;
  @observable sancaoLicitante;
  @observable idLicitacao;
  @observable idDispensa;
  @observable idCarona;
  @observable idInexigibilidade;
  @observable numeroInexigibilidade;
  @observable numeroDispensa;
  @observable numeroCarona;
  @observable numeroLicitacao;
  @observable anoLicitacao;
  @observable fontesDeRecurso;
  @observable elementosDeDespesa;
  @observable arquivos;
  @observable anoCadastro;
  @observable origem;
  @observable numeroContratoSei;
  @observable valorProcessoEntidadeExterna;
  @observable numeroProcessoAdmSei;
  @observable siteDivugacaoProcesso;
  @observable entidadeExterna;
  @observable processoExterno;
  @observable garantias;
  @observable empenhos;
  @observable processoMigrado;
  @observable tipoEmpenho;
  @observable dataEmpenho;
  @observable autoridadeContratante;
  @observable fundamentacao;
  @observable fundamentacaoLegalEntidade;
  @observable anoProcesso;
  @observable lei;
  @observable legislacaoOutros;
  @observable aditivado;
  @observable suprimido;
  @observable dataFinalVigente;
  @observable idAutoridadeContratante;
  @observable nomeAutoridadeContratante;

  static getDomainAttributes() {
    return [
      'id',
      'garantias',
      'processoExterno',
      'valorProcessoEntidadeExterna',
      'numeroProcessoAdmSei',
      'siteDivugacaoProcesso',
      'entidadeExterna',
      'idRequisicaoModificacao',
      'numero',
      'tipo',
      'valorGlobal',
      'dataPublicacao',
      'entidade',
      'usuario',
      'contratoLicitante',
      'dataVigenciaInicial',
      'dataVigenciaFinal',
      'programaTrabalhoDesc',
      'objeto',
      'formaContrato',
      'numeroDoe',
      'prazoExecucao',
      'gestor',
      'gestorSubstituto',
      'fiscal',
      'fiscalSubstituto',
      'dataCadastro',
      'observacoes',
      'houveModificacao',
      'camposModificados',
      'idContabil',
      'permiteAditivo',
      'status',
      'motivoRescisao',
      'outroMotivoRescisao',
      'formaExtincao',
      'dataAvisoRescisao',
      'descricaoRescisao',
      'contratoAntigo',
      'sancaoLicitante',
      'idLicitacao',
      'idDispensa',
      'idCarona',
      'idInexigibilidade',
      'numeroInexigibilidade',
      'numeroDispensa',
      'numeroCarona',
      'numeroLicitacao',
      'anoLicitacao',
      'fontesDeRecurso',
      'elementosDeDespesa',
      'arquivos',
      'anoCadastro',
      'origem',
      'numeroContratoSei',
      'empenhos',
      'processoMigrado',
      'tipoEmpenho',
      'dataEmpenho',
      'numeroContrato',
      'autoridadeContratante',
      'anoContrato',
      'fundamentacao',
      'fundamentacaoLegalEntidade',
      'anoProcesso',
      'lei',
      'legislacaoOutros',
      'aditivado',
      'suprimido',
      'dataFinalVigente',
      'idAutoridadeContratante',
      'nomeAutoridadeContratante',
    ];
  }
}

export default Contrato;
