import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class ContratoView extends DomainBase {
  @observable id;
  @observable idRequisicaoModificacao;
  @observable entidade;
  @observable tipo;
  @observable numero;
  @observable formaContrato;
  @observable valorGlobal;
  @observable objeto;
  @observable licitante;
  @observable entidadeExterna;
  @observable dataCadastro;
  @observable permiteAditivo;
  @observable processoExterno;
  @observable numeroContrato;
  @observable fontesDeRecurso;
  @observable dataVigenciaInicial;
  @observable dataVigenciaFinal;
  @observable cnpjContratado;
  @observable aditivado;
  @observable suprimido;
  @observable dataFinalVigente;

  static getDomainAttributes() {
    return [
      'id',
      'idRequisicaoModificacao',
      'entidade',
      'tipo',
      'numero',
      'formaContrato',
      'valorGlobal',
      'objeto',
      'licitante',
      'entidadeExterna',
      'dataCadastro',
      'permiteAditivo',
      'processoExterno',
      'numeroContrato',
      'fontesDeRecurso',
      'dataVigenciaInicial',
      'dataVigenciaFinal',
      'cnpjContratado',
      'aditivado',
      'suprimido',
      'dataFinalVigente',
    ];
  }
}

export default ContratoView;
