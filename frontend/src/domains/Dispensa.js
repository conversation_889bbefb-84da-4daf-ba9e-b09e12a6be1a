import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class Dispensa extends DomainBase {
  @observable id;
  @observable idRequisicaoModificacao;
  @observable anoDispensa;
  @observable dataPedido;
  @observable numeroProcesso;
  @observable objeto;
  @observable termoReferencia;
  @observable naturezasDoObjeto;
  @observable fontesDeRecurso;
  @observable entidade;
  @observable usuario;
  @observable status;
  @observable dataCadastro;
  @observable justificativa;
  @observable fundamentacao;
  @observable fundamentacaoLegalEntidade;
  @observable observacoes;
  @observable fornecedores;
  @observable valor;
  @observable gestor;
  @observable responsavelInexigibilidade;
  @observable statusAnalise;
  @observable emAnalise;
  @observable numeroDownloads;
  @observable usuarioSeparouAnalise;
  @observable houveModificacao;
  @observable camposModificados;
  @observable modificacaoAlerta;
  @observable entidadesSecundárias;
  @observable arquivos;
  @observable numeroProcessoSEI;
  @observable checklists;
  @observable statusProcessoArquivado;
  @observable dataAnalise;
  @observable processoMigrado;
  @observable lei;
  @observable legislacaoOutros;
  @observable tipoAdjudicacao;
  @observable lotesFracassados;
  @observable itensDesertos;
  @observable orgaosParticipantes;
  @observable obra;
  @observable updatedAt;
  @observable updatedAtAudit;
  @observable participacaoExclusiva;

  static getDomainAttributes() {
    return [
      'id',
      'idRequisicaoModificacao',
      'anoDispensa',
      'dataPedido',
      'numeroProcesso',
      'objeto',
      'termoReferencia',
      'naturezasDoObjeto',
      'fontesDeRecurso',
      'entidade',
      'usuario',
      'status',
      'dataCadastro',
      'justificativa',
      'observacoes',
      'fundamentacao',
      'fundamentacaoLegalEntidade',
      'fornecedores',
      'valor',
      'gestor',
      'responsavelInexigibilidade',
      'statusAnalise',
      'emAnalise',
      'numeroDownloads',
      'usuarioSeparouAnalise',
      'houveModificacao',
      'camposModificados',
      'modificacaoAlerta',
      'entidadesSecundárias',
      'arquivos',
      'numeroProcessoSEI',
      'checklists',
      'statusProcessoArquivado',
      'dataAnalise',
      'processoMigrado',
      'lei',
      'legislacaoOutros',
      'tipoAdjudicacao',
      'lotesFracassados',
      'itensDesertos',
      'orgaosParticipantes',
      'obra',
      'updatedAt',
      'updatedAtAudit',
      'participacaoExclusiva',
    ];
  }
}

export default Dispensa;
