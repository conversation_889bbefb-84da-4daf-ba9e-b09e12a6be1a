import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class Credenciamento extends DomainBase {
  @observable id;
  @observable entidade;
  @observable usuario;
  @observable lei;
  @observable legislacaoOutros;
  @observable numeroProcesso;
  @observable numero;
  @observable ano;
  @observable valor;
  @observable inicioVigencia;
  @observable fimVigencia;
  @observable termoReferencia;
  @observable responsavel;
  @observable tipoContratacao;
  @observable sitioDivulgacao;
  @observable comissaoContratacao;
  @observable presidenteComissao;
  @observable naturezasDoObjeto;
  @observable fontesDeRecurso;
  @observable orgaosParticipantes;
  @observable objeto;
  @observable observacoes;
  @observable dataCadastro;
  @observable status;
  @observable obra;
  @observable statusProcessoArquivado;
  @observable updatedAt;
  @observable updatedAtAudit;
  @observable participacaoExclusiva;

  static getDomainAttributes() {
    return [
      'id',
      'entidade',
      'usuario',
      'lei',
      'legislacaoOutros',
      'numeroProcesso',
      'numero',
      'ano',
      'valor',
      'inicioVigencia',
      'fimVigencia',
      'termoReferencia',
      'responsavel',
      'tipoContratacao',
      'sitioDivulgacao',
      'comissaoContratacao',
      'presidenteComissao',
      'naturezasDoObjeto',
      'fontesDeRecurso',
      'orgaosParticipantes',
      'objeto',
      'observacoes',
      'dataCadastro',
      'obra',
      'statusProcessoArquivado',
      'updatedAt',
      'updatedAtAudit',
      'participacaoExclusiva',
    ];
  }
}

export default Credenciamento;
