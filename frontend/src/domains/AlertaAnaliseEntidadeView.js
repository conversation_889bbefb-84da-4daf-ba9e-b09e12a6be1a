import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class AlertaAnaliseEntidadeView extends DomainBase {
  @observable id;
  @observable entidade;
  @observable data;
  @observable usuarioAtual;
  @observable usuarioResponsavel;
  @observable diretorDafo;
  @observable conselheiro;
  @observable status;
  @observable passouPorDafo;
  @observable passouPorConselheiro;
  @observable passouPorAuditor;
  @observable dataArquivamento;
  @observable usuarioArquivamento;
  @observable dataRejeicao;
  @observable tipo;
  @observable tipoProcesso;
  @observable idProcesso;
  @observable dataAberturaProcesso;
  @observable mensagem;
  @observable tdaCarona;
  @observable tdaDispensa;
  @observable tdaInexigibilidade;
  @observable tdaLicitacao;
  @observable tdaCredenciamento;

  static getDomainAttributes() {
    return [
      'id',
      'entidade',
      'data',
      'usuarioAtual',
      'usuarioResponsavel',
      'diretorDafo',
      'conselheiro',
      'status',
      'passouPorDafo',
      'passouPorConselheiro',
      'passouPorAuditor',
      'dataArquivamento',
      'usuarioArquivamento',
      'dataRejeicao',
      'tipo',
      'tipoProcesso',
      'idProcesso',
      'dataAberturaProcesso',
      'mensagem',
      'tdaCarona',
      'tdaDispensa',
      'tdaInexigibilidade',
      'tdaLicitacao',
      'tdaCredenciamento',
    ];
  }
}

export default AlertaAnaliseEntidadeView;
