import { computed, observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class Licitacao extends DomainBase {
  @observable id;
  @observable entidade;
  @observable usuario;
  @observable termoReferencia;
  @observable numero;
  @observable ano;
  @observable orgao;
  @observable dataAbertura;
  @observable dataCadastro;
  @observable dataCadastroObjeto;
  @observable dataCadastroPreparatoria;
  @observable dataCadastroFinalizacao;
  @observable objeto;
  @observable tipo;
  @observable tiposLicitacao;
  @observable forma;
  @observable pregoeiro;
  @observable numeroProcessoAdm;
  @observable numeroLicitacaoEntidadeExterna;
  @observable comissao;
  @observable regenciaLegal;
  @observable status;
  @observable justificativa;
  @observable dataCadastroVencedores;
  @observable srp;
  @observable valorEstimado;
  @observable valorDeRisco;
  @observable statusOcorrenciaAtual;
  @observable naturezaOcorrencia;
  @observable statusAnalise;
  @observable emAnalise;
  @observable consultorIndividual;
  @observable numeroDownloads;
  @observable usuarioSeparouAnalise;
  @observable houveModificacao;
  @observable camposModificados;
  @observable participacaoExterna;
  @observable orgaosParticipantes;
  @observable naturezasDoObjeto;
  @observable fontesDeRecurso;
  @observable parecerista;
  @observable acatadoJustificado;
  @observable licitantes;
  @observable fase;
  @observable vencedores;
  @observable publicacoes;
  @observable idRequisicaoModificacao;
  @observable obra;
  @observable lei;
  @observable legislacaoOutros;
  @observable checklists;
  @observable statusProcessoArquivado;
  @observable dataAnalise;
  @observable processoMigrado;
  @observable lotesFracassados;
  @observable tipoAdjudicacao;
  @observable itensDesertos;
  @observable tentativasMatch;
  @observable edital;
  @observable updatedAt;
  @observable userUpdated;
  @observable userCreatedPublicacao;
  @observable updatedAtAudit;
  @observable participacaoExclusiva;

  @computed
  get orgaoNome() {
    if (this.entidade != null) {
      let orgaoNome = this.entidade.nome;

      if (orgaoNome.includes(' - ')) {
        let partesOrgaoNome = orgaoNome.split(' - ');
        if (partesOrgaoNome.length == 3) {
          return partesOrgaoNome[1] + ' - ' + partesOrgaoNome[2] + ' - ' + partesOrgaoNome[0];
        }
        return partesOrgaoNome[1] + ' - ' + partesOrgaoNome[0];
      }

      if (orgaoNome.includes(' – ')) {
        let partesOrgaoNome = orgaoNome.split(' – ');

        if (partesOrgaoNome.length == 3) {
          return partesOrgaoNome[1] + ' – ' + partesOrgaoNome[2] + ' – ' + partesOrgaoNome[0];
        }
        return partesOrgaoNome[1] + ' – ' + partesOrgaoNome[0];
      }
      return orgaoNome;
    }
    return ' ';
  }

  static getDomainAttributes() {
    return [
      'id',
      'entidade',
      'usuario',
      'termoReferencia',
      'numero',
      'ano',
      'orgao',
      'dataAbertura',
      'dataCadastro',
      'dataCadastroObjeto',
      'dataCadastroPreparatoria',
      'dataCadastroFinalizacao',
      'objeto',
      'tipo',
      'tiposLicitacao',
      'forma',
      'pregoeiro',
      'numeroProcessoAdm',
      'numeroLicitacaoEntidadeExterna',
      'comissao',
      'regenciaLegal',
      'status',
      'justificativa',
      'dataCadastroVencedores',
      'srp',
      'valorEstimado',
      'valorDeRisco',
      'statusOcorrenciaAtual',
      'naturezaOcorrencia',
      'statusAnalise',
      'emAnalise',
      'consultorIndividual',
      'numeroDownloads',
      'usuarioSeparouAnalise',
      'houveModificacao',
      'camposModificados',
      'participacaoExterna',
      'orgaosParticipantes',
      'orgaoNome',
      'naturezasDoObjeto',
      'fontesDeRecurso',
      'parecerista',
      'acatadoJustificado',
      'licitantes',
      'fase',
      'vencedores',
      'publicacoes',
      'idRequisicaoModificacao',
      'lei',
      'legislacaoOutros',
      'checklists',
      'statusProcessoArquivado',
      'dataAnalise',
      'processoMigrado',
      'lotesFracassados',
      'tipoAdjudicacao',
      'itensDesertos',
      'edital',
      'obra',
      'tentativasMatch',
      'updatedAt',
      'userUpdated',
      'userCreatedPublicacao',
      'updatedAtAudit',
      'participacaoExclusiva',
    ];
  }
}

export default Licitacao;
