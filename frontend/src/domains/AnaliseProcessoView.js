import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class AnaliseProcessoView extends DomainBase {
  @observable id;
  @observable idProcesso;
  @observable numero;
  @observable entidade;
  @observable objeto;
  @observable dataCadastro;
  @observable tipoProcesso;
  @observable valor;
  @observable valorAdjudicado;
  @observable dataProcesso;
  @observable dataFinalAnalise;
  @observable responsavel;
  @observable analista;
  @observable valorRisco;
  @observable idAlertaAnalise;
  @observable passouDafo;
  @observable arquivado;
  @observable checklistFinalizado;
  @observable statusProcessoArquivado;
  @observable ano;
  @observable termoReferencia;
  @observable updatedAtAudit;

  static getDomainAttributes() {
    return [
      'id',
      'idProcesso',
      'numero',
      'entidade',
      'objeto',
      'dataCadastro',
      'tipoProcesso',
      'valor',
      'valorAdjudicado',
      'dataProcesso',
      'dataFinalAnalise',
      'responsavel',
      'analista',
      'valorRisco',
      'idAlertaAnalise',
      'passouDafo',
      'arquivado',
      'checklistFinalizado',
      'statusProcessoArquivado',
      'ano',
      'termoReferencia',
      'updatedAtAudit',
    ];
  }
}

export default AnaliseProcessoView;
