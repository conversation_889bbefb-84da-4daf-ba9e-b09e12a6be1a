import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class Carona extends DomainBase {
  @observable id;
  @observable numeroProcessoAdministrativo;
  @observable anoCarona;
  @observable dataAdesao;
  @observable naturezasDoObjeto;
  @observable gerenciadorAta;
  @observable numeroProcessoGerenciadorAta;
  @observable observacoes;
  @observable entidade;
  @observable usuario;
  @observable status;
  @observable dataCadastro;
  @observable dataValidadeAta;
  @observable justificativa;
  @observable valor;
  @observable responsavelAdesao;
  @observable responsavelAdesaoCarona;
  @observable objeto;
  @observable statusAnalise;
  @observable emAnalise;
  @observable numeroDownloads;
  @observable usuarioSeparouAnalise;
  @observable detentores;
  @observable fontesDeRecurso;
  @observable arquivosDTOs;
  @observable arquivos;
  @observable termoReferencia;
  @observable origemLicon2;
  @observable processoEntidadeOrigem;
  @observable entidadeOrigem;
  @observable entidadeOrigemExterna;
  @observable licitacao;
  @observable checklists;
  @observable idRequisicaoModificacao;
  @observable statusProcessoArquivado;
  @observable dataAnalise;
  @observable processoMigrado;
  @observable fundamentacaoLegal;
  @observable lei;
  @observable legislacaoOutros;
  @observable tipoAdjudicacao;
  @observable idResponsavelAdesaoCarona;
  @observable updatedAt;
  @observable updatedAtAudit;

  static getDomainAttributes() {
    return [
      'id',
      'numeroProcessoAdministrativo',
      'anoCarona',
      'dataAdesao',
      'naturezasDoObjeto',
      'gerenciadorAta',
      'numeroProcessoGerenciadorAta',
      'observacoes',
      'entidade',
      'usuario',
      'status',
      'dataCadastro',
      'justificativa',
      'modalidadeCarona',
      'valor',
      'responsavelAdesao',
      'responsavelAdesaoCarona',
      'objeto',
      'statusAnalise',
      'emAnalise',
      'numeroDownloads',
      'usuarioSeparouAnalise',
      'detentores',
      'fontesDeRecurso',
      'arquivosDTOs',
      'arquivos',
      'termoReferencia',
      'origemLicon2',
      'processoEntidadeOrigem',
      'entidadeOrigem',
      'entidadeOrigemExterna',
      'licitacao',
      'checklists',
      'idRequisicaoModificacao',
      'statusProcessoArquivado',
      'dataAnalise',
      'processoMigrado',
      'fundamentacaoLegal',
      'lei',
      'legislacaoOutros',
      'tipoAdjudicacao',
      'idResponsavelAdesaoCarona',
      'updatedAt',
      'updatedAtAudit',
    ];
  }
}

export default Carona;
