import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class BoardLicitacaoView extends DomainBase {
  @observable id;
  @observable numero;
  @observable numeroAnoLicitacao;
  @observable ano;
  @observable objeto;
  @observable entidade;
  @observable statusOcorrenciaAtual;
  @observable naturezaOcorrencia;
  @observable fase;
  @observable orgao;
  @observable dataCadastro;
  @observable valorEstimado;
  @observable valorAdjudicado;
  @observable dataAbertura;
  @observable status;
  @observable ultimaOcorrencia;
  @observable dataUltimaOcorrencia;
  @observable dataCadastroPreparatoria;
  @observable dataCadastroVencedores;
  @observable idRequisicaoModificacao;
  @observable termoReferencia;
  @observable qtdContratosAssociados;
  @observable updatedAt;
  @observable userCreatedPublicacao;

  static getDomainAttributes() {
    return [
      'id',
      'numero',
      'numeroAnoLicitacao',
      'ano',
      'objeto',
      'entidade',
      'statusOcorrenciaAtual',
      'naturezaOcorrencia',
      'fase',
      'orgao',
      'dataCadastro',
      'valorEstimado',
      'valorAdjudicado',
      'dataAbertura',
      'status',
      'ultimaOcorrencia',
      'dataUltimaOcorrencia',
      'dataCadastroPreparatoria',
      'dataCadastroVencedores',
      'idRequisicaoModificacao',
      'termoReferencia',
      'qtdContratosAssociados',
      'updatedAt',
      'userCreatedPublicacao',
    ];
  }
}

export default BoardLicitacaoView;
