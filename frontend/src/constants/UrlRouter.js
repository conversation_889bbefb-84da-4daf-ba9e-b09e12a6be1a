const UrlRouter = {
  home: '/',
  login: '/login',
  development: '/development',
  administracao: {
    default: '/administracao',
    fonteRecurso: {
      index: '/administracao/fonte-recurso',
      novo: '/administracao/fonte-recurso/novo',
      editar: '/administracao/fonte-recurso/editar/:id',
    },
    tipoLicitacao: {
      index: '/administracao/tipo-licitacao',
      novo: '/administracao/tipo-licitacao/novo',
      editar: '/administracao/tipo-licitacao/editar/:id',
    },
    elementoDespesa: {
      index: '/administracao/elemento-despesa',
      novo: '/administracao/elemento-despesa/novo',
      editar: '/administracao/elemento-despesa/editar/:id',
    },
    formaLicitacao: {
      index: '/administracao/forma-licitacao',
      novo: '/administracao/forma-licitacao/novo',
      editar: '/administracao/forma-licitacao/editar/:id',
    },
    modalidadeLicitacao: {
      index: '/administracao/modalidade-licitacao',
      novo: '/administracao/modalidade-licitacao/novo',
      editar: '/administracao/modalidade-licitacao/editar/:id',
    },
    formaPublicacao: {
      index: '/administracao/forma-publicacao',
      novo: '/administracao/forma-publicacao/novo',
      editar: '/administracao/forma-publicacao/editar/:id',
    },
    linkConsulta: {
      index: '/administracao/gerador-links',
      novo: '/administracao/gerador-links/novo',
      editar: '/administracao/gerador-links/editar/:id',
    },
    variavelControle: {
      index: '/administracao/variavel-controle',
      editar: '/administracao/variavel-controle/editar/:id',
    },
    obrigatoriedadeArquivo: {
      index: '/administracao/obrigatoriedade-arquivo',
      novo: '/administracao/obrigatoriedade-arquivo/novo',
      editar: '/administracao/obrigatoriedade-arquivo/editar/:id',
    },
    licitante: {
      index: '/administracao/licitante',
      novo: '/administracao/licitante/novo',
      editar: '/administracao/licitante/editar/:id',
    },
    falha: {
      index: '/administracao/falha',
      novo: '/administracao/falha/novo',
      editar: '/administracao/falha/editar/:id',
    },
    feriado: {
      index: '/administracao/feriado',
      novo: '/administracao/feriado/novo',
      editar: '/administracao/feriado/editar/:id',
    },
    entidadeExterna: {
      index: '/administracao/entidade-externa',
      novo: '/administracao/entidade-externa/novo',
      editar: '/administracao/entidade-externa/editar/:id',
    },
    fundamentacaoLegal: {
      index: '/administracao/fundamentacao-legal',
      novo: '/administracao/fundamentacao-legal/novo',
      editar: '/administracao/fundamentacao-legal/editar/:id',
    },
    responsavelEnte: {
      index: '/administracao/responsavel-ente',
      novo: '/administracao/responsavel-ente/novo',
      editar: '/administracao/responsavel-ente/editar/:id',
    },
    configuracoesEmail: {
      editar: '/administracao/configuracoes-email/editar',
    },
    comissao: {
      index: '/administracao/comissao',
      novo: '/administracao/comissao/novo',
      editar: '/administracao/comissao/editar/:id',
    },
    requisicaoModificacao: {
      index: '/administracao/requisicao-modificacao',
      indexDetail: '/administracao/requisicao-modificacao/:id',
      julgamento: '/administracao/requisicao-modificacao/julgamento/:id',
      licitacao: {
        requisitar: '/cadastros-consulta/requisicao-modificacao/licitacao/:id',
      },
      inexigibilidade: {
        requisitar: '/cadastros-consulta/inexigibilidade/requisicao-modificacao/:id',
      },
      carona: {
        requisitar: '/cadastros-consulta/carona/requisicao-modificacao/:id',
      },
      contrato: {
        requisitar: '/cadastros-consulta/contrato/requisicao-modificacao/:id',
      },
      aditivo: {
        requisitar: '/cadastros-consulta/contrato/aditivos/requisicao-modificacao/:idContrato/:id',
      },
      dispensa: {
        requisitar: '/cadastros-consulta/dispensa/requisicao-modificacao/:id',
      },
      termoReferencia: {
        requisitar: '/termo-referencia/gerenciamento-termos/requisicao-modificacao/:id',
      },
      credenciamento: {
        requisitar: '/cadastros-consulta/credenciamento/requisicao-modificacao/:id',
      },
    },
    responsavelContrato: {
      index: '/administracao/responsavel-contrato',
      novo: '/administracao/responsavel-contrato/novo',
      editar: '/administracao/responsavel-contrato/editar/:id',
    },
  },
  apoio: {
    default: '/apoio',
  },
  auditoria: {
    default: '/auditoria',
    secaoChecklist: {
      index: '/auditoria/secao-checklist',
      novo: '/auditoria/secao-checklist/novo',
      editar: '/auditoria/secao-checklist/editar/:id',
    },
    itemChecklist: {
      index: '/auditoria/item-checklist',
      novo: '/auditoria/item-checklist/novo',
      editar: '/auditoria/item-checklist/editar/:id',
    },
    analiseProcesso: {
      index: '/auditoria/analise-processo',
    },
    tda: {
      default: '/auditoria/tda',
      novo: '/auditoria/tda/:tipoProcesso/:idProcesso/novo',
      editar: '/auditoria/tda/:tipoProcesso/:idProcesso/editar/:id',
    },
    aplicarChecklist: {
      default: '/auditoria/aplicar-checklist',
      editar: '/auditoria/aplicar-checklist/:tipoProcesso/:idProcesso/editar',
    },
    emitirAlerta: {
      default: '/auditoria/emitir-alerta',
      novo: '/auditoria/emitir-alerta/:tipoProcesso/:idProcesso/novo',
      editar: '/auditoria/emitir-alerta/:tipoProcesso/:idProcesso/editar/:idAlerta',
    },
    analisarAlertas: {
      index: '/auditoria/analisar-alertas',
    },
    historicoProcessos: {
      index: '/auditoria/historico-processos',
    },
    requisicaoModificacao: {
      index: '/auditoria/requisicao-modificacao',
      indexDetail: '/auditoria/requisicao-modificacao/:id',
      julgamento: '/auditoria/requisicao-modificacao/julgamento/:id',
    },
    analiseAutomatica: {
      index: '/auditoria/analise-automatica',
      novo: '/auditoria/analise-automatica/novo',
      editar: '/auditoria/analise-automatica/editar/:id',
    },
    analisarEditais: {
      index: '/auditoria/analisar-editais',
    },
    monitoramentoAtosDiarioOficial: {
      index: '/auditoria/monitoramento-atos-diario-oficial',
    },
    mapeamentoAtosLicitacoes: {
      index: '/auditoria/mapeamento-atos-licitacoes',
    },
    painelAtosLicitacoes: {
      index: '/auditoria/painel-atos-licitacoes',
    },
    alertasInspetor: {
      index: '/auditoria/alertas-inspetor',
    },
  },
  alerta: {
    index: '/alertas',
    editar: '/alertas/editar/:id',
  },
  cadastrosConsulta: {
    default: '/cadastros-consulta',
    carona: {
      index: '/cadastros-consulta/carona',
      detalhe: '/cadastros-consulta/carona/detail/:id',
      novo: '/cadastros-consulta/carona/novo',
      editar: '/cadastros-consulta/carona/editar/:id',
    },
    licitacao: {
      index: '/cadastros-consulta/licitacao',
      detalhe: '/cadastros-consulta/licitacao/detail/:id',
      novo: '/cadastros-consulta/licitacao/novo',
      editar: '/cadastros-consulta/licitacao/editar/:fase/:id',
      proximaFase: '/cadastros-consulta/licitacao/proxima-fase/:id',
    },
    parecerista: {
      index: '/cadastros-consulta/parecerista',
      novo: '/cadastros-consulta/parecerista/novo',
      editar: '/cadastros-consulta/parecerista/editar/:id',
    },
    consultaProcesso: {
      index: '/cadastros-consulta/processo',
    },
    dispensa: {
      index: '/cadastros-consulta/dispensa',
      detalhe: '/cadastros-consulta/dispensa/detail/:id',
      novo: '/cadastros-consulta/dispensa/novo',
      editar: '/cadastros-consulta/dispensa/editar/:id',
    },
    inexigibilidade: {
      index: '/cadastros-consulta/inexigibilidade',
      detalhe: '/cadastros-consulta/inexigibilidade/detail/:id',
      novo: '/cadastros-consulta/inexigibilidade/novo',
      editar: '/cadastros-consulta/inexigibilidade/editar/:id',
    },
    credenciamento: {
      index: '/cadastros-consulta/credenciamento',
      detalhe: '/cadastros-consulta/credenciamento/detail/:id',
      novo: '/cadastros-consulta/credenciamento/novo',
      editar: '/cadastros-consulta/credenciamento/editar/:id',
      credenciado: {
        index: '/cadastros-consulta/credenciamento/:idCredenciamento/credenciados',
        novo: '/cadastros-consulta/credenciamento/:idCredenciamento/credenciados/novo',
        editar: '/cadastros-consulta/credenciamento/:idCredenciamento/credenciados/editar/:id',
      },
    },
    contrato: {
      index: '/cadastros-consulta/contrato',
      novo: '/cadastros-consulta/contrato/novo',
      detalhe: '/cadastros-consulta/contrato/detail/:id',
      editar: '/cadastros-consulta/contrato/editar/:id',
      aditivo: {
        index: '/cadastros-consulta/contrato/:idContrato/aditivos',
        novo: '/cadastros-consulta/contrato/:idContrato/aditivos/novo',
        editar: '/cadastros-consulta/contrato/:idContrato/aditivos/editar/:id',
      },
      empenho: {
        index: '/cadastros-consulta/contrato/:idContrato/empenho',
        novo: '/cadastros-consulta/contrato/:idContrato/empenho/novo',
        editar: '/cadastros-consulta/contrato/:idContrato/empenho/editar/:id',
      },
      defaultLicitacao: '/cadastros-consulta/contrato/novo/:idLicitacao',
    },
  },
  seguranca: {
    default: '/seguranca',
    usuario: {
      index: '/seguranca/usuario',
      novo: '/seguranca/usuario/novo',
      editar: '/seguranca/usuario/editar/:id',
    },
    grupoUsuario: {
      index: '/seguranca/grupo-usuario',
      novo: '/seguranca/grupo-usuario/novo',
      editar: '/seguranca/grupo-usuario/editar/:id',
    },
    configuracoes: {
      editar: '/seguranca/configuracoes/editar',
    },
    funcaoRisco: {
      index: '/seguranca/funcao-risco',
      novo: '/seguranca/funcao-risco/novo',
      editar: '/seguranca/funcao-risco/editar/:id',
    },
  },
  termoReferencia: {
    default: '/termo-referencia',
    secao: {
      index: '/termo-referencia/secao',
      novo: '/termo-referencia/secao/novo',
      editar: '/termo-referencia/secao/editar/:id',
    },
    gerenciamentoTermos: {
      index: '/termo-referencia/gerenciamento-termos',
      novo: '/termo-referencia/gerenciamento-termos/novo',
      editar: '/termo-referencia/gerenciamento-termos/editar/:id',
      detalhe: '/termo-referencia/gerenciamento-termos/detail/:idTermo',
    },
  },
  catalogo: {
    default: '/catalogo',
    consultaMateriais: {
      index: '/catalogo/consulta-materiais',
      detalhar: '/catalogo/consulta-materiais/detalhar/:id',
    },
  },
  obra: {
    default: '/obras',
    tipoObra: {
      index: '/obras/tipo-obra',
      novo: '/obras/tipo-obra/novo',
      editar: '/obras/tipo-obra/editar/:id',
    },
    cadastro: {
      index: '/obras/cadastro',
      novo: '/obras/cadastro/novo',
      editar: '/obras/cadastro/editar/:id',
    },
    acompanhamento: {
      index: '/obras/acompanhamentos',
    },
    mapa: {
      index: '/obras/mapa',
    },
    medicao: {
      index: '/obras/medicao',
    },
    relatorioObra: {
      index: '/obras/relatorio-obra',
      novo: '/obras/relatorio-obra/novo',
      editar: '/obras/relatorio-obra/editar/:id',
    },
  },
};

export default UrlRouter;
