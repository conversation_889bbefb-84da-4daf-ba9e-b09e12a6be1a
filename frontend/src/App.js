import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import 'primeflex/primeflex.css';
import './App.scss';

import React, { useEffect } from 'react';

import Template from './pages/Template';
import { Switch, Route, matchPath } from 'react-router';
import { createBrowserHistory } from 'history';
import PrimeReact from 'primereact/api';
import UrlRouter from './constants/UrlRouter';
import { BrowserRouter, HashRouter } from 'react-router-dom';
import Development from 'fc/pages/Development';
import NotFound from 'fc/pages/NotFound';
import { getRoutesObjects } from 'fc/utils/utils';
import Login from './pages/login/Login';
import RoutesAdministracao from './pages/routes/administracao/RoutesAdministracao';
import RoutesApoio from './pages/routes/apoio/RoutesApoio';
import RoutesAuditoria from './pages/routes/auditoria/RoutesAuditoria';
import RoutesAlertasAuditorJurisdicionado from './pages/routes/avisos/RoutesAlertasAuditorJurisdicionado';
import RoutesCadastrosConsulta from './pages/routes/cadastrosConsulta/RoutesCadastrosConsulta';
import RoutesSeguranca from './pages/routes/seguranca/RoutesSeguranca';
import RoutesTermoReferencia from './pages/routes/termoReferencia/RoutesTermoReferencia';
import Homepage from './pages/paginaInicial/Home';
import RoutesCatalogo from './pages/routes/catalogo/RoutesCatalogo';
import AppErrorBoundary from './components/AppErrorBoundary';
import RoutesObras from './pages/routes/obras/RoutesObras';
import { AppContextsProvider } from 'fc/contexts';

PrimeReact.ripple = true;
export const RTLContext = React.createContext();

require('dotenv').config();
const history = createBrowserHistory();

const App = () => {
  useEffect(() => {
    window.location.getCurrentPath = () =>
      getRoutesObjects(UrlRouter).find((route) => matchPath(window.location.hash?.replace('#', ''), route))?.path;
  }, []);

  return (
    <AppErrorBoundary>
      <BrowserRouter history={history}>
        <HashRouter>
          <Switch>
            <AppContextsProvider history={history}>
              <Template>
                <Switch>
                  <Route path={UrlRouter.login} exact component={Login} />
                  <Route path={UrlRouter.home} exact component={Homepage} />
                  <Route path={UrlRouter.administracao.default} component={RoutesAdministracao} />
                  <Route path={UrlRouter.apoio.default} component={RoutesApoio} />
                  <Route path={UrlRouter.auditoria.default} component={RoutesAuditoria} />
                  <Route path={UrlRouter.alerta.index} component={RoutesAlertasAuditorJurisdicionado} />
                  <Route path={UrlRouter.cadastrosConsulta.default} component={RoutesCadastrosConsulta} />
                  <Route path={UrlRouter.seguranca.default} component={RoutesSeguranca} />
                  <Route path={UrlRouter.termoReferencia.default} component={RoutesTermoReferencia} />
                  <Route path={UrlRouter.catalogo.default} component={RoutesCatalogo} />
                  <Route path={UrlRouter.obra.default} component={RoutesObras} />
                  <Route path={UrlRouter.development} component={Development} />
                  <Route component={NotFound} />
                </Switch>
              </Template>
            </AppContextsProvider>
          </Switch>
        </HashRouter>
      </BrowserRouter>
    </AppErrorBoundary>
  );
};

export default App;
