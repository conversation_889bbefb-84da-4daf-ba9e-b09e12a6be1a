import { PropTypes, observer } from 'mobx-react';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import AccessPermission from '~/constants/AccessPermission';
import FcButton from 'fc/components/FcButton';
import PermissionProxy from 'fc/components/PermissionProxy';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import { checkUserGroup, getValue, getValueByKey, getValueDate } from 'fc/utils/utils';
import AlertasViewIndexStore from '~/stores/paginaInicial/alertas/AlertasViewIndexStore';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import UrlRouter from '~/constants/UrlRouter';

@observer
class AlertasResumo extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.alerta);
    this.store = new AlertasViewIndexStore();
    this.state = {
      visibleWarningsDialog: false,
      openWarnings: false,
      onRowSelect: null,
    };
  }

  componentDidMount() {
    this.store.load();
  }

  openWarningsDialog = () => {
    this.setState({ visibleWarningsDialog: true });
  };

  closeWarningsDialog = () => {
    this.setState({ visibleWarningsDialog: false });
  };

  renderWarningsDialog() {
    const { listKey } = this.store;

    return (
      <Dialog
        header="Alertas em Aberto"
        style={{ width: '80vw' }}
        draggable={false}
        visible={this.state.visibleWarningsDialog}
        closable={false}
        onHide={this.closeWarningsDialog}
      >
        <div className="p-fluid p-formgrid p-grid">
          <div className="p-field p-col-12">
            <DataTable
              rowHover
              value={listKey}
              className="p-datatable-sm"
              paginator
              rows={5}
              emptyMessage={'Nenhum alerta em aberto'}
            >
              <Column
                header="Data de Emissão"
                key="col-data"
                field="data"
                body={({ data }) => getValueDate(data, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)}
              />
              {checkUserGroup('Auditor') && (
                <Column
                  header="Auditor Responsável"
                  key="col-usuarioResponsavel"
                  field="usuarioResponsavel"
                  body={({ usuarioResponsavel }) => getValue(usuarioResponsavel?.nome)}
                />
              )}
              <Column
                header="Entidade"
                key="col-entidade"
                field="entidade"
                body={({ entidade }) => getValue(entidade?.nome)}
              />
              <Column
                header="Processo"
                key="col-tipoProcesso"
                field="tipoProcesso"
                body={({ tipoProcesso }) => getValue(tipoProcesso)}
              />
              <Column
                header="Data de Abertura"
                key="col-dataAberturaProcesso"
                field="dataAberturaProcesso"
                body={({ dataAberturaProcesso }) =>
                  getValueDate(dataAberturaProcesso, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)
                }
              />
              <Column
                header="Status"
                key="col-status"
                field="status"
                body={({ status }) => getValueByKey(status, DadosEstaticosService.getStatusAlertaAnalise())}
              />
              <Column
                style={{ width: '150px' }}
                header=""
                key="col-acoes"
                field=""
                body={(rowData) => {
                  return (
                    <PermissionProxy resourcePermissions={this.getWritePermission()}>
                      <FcButton
                        icon="pi pi-eye"
                        label="Visualizar Alerta"
                        className="p-button-sm p-button-success p-mr-2"
                        onClick={() => {
                          rowData?.id && this.pushUrlToHistory(UrlRouter.alerta.editar.replace(':id', rowData.id));
                        }}
                      />
                    </PermissionProxy>
                  );
                }}
              />
            </DataTable>
          </div>
          <div className="p-field p-col-12">
            <span style={{ float: 'right' }} className="p-d-flex">
              <FcButton
                label="Fechar"
                type="button"
                className="p-ml-auto p-button-secondary"
                onClick={() => {
                  this.setState({ visibleWarningsDialog: false });
                }}
              />
            </span>
          </div>
        </div>
      </Dialog>
    );
  }

  getColumns() {
    const columns = [
      {
        style: { width: '50%' },
        field: 'data',
        header: 'Data de Emissão',
        body: ({ data }) => getValueDate(data, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
      },

      {
        style: { width: '70%' },
        field: 'tipoProcesso',
        header: 'Processo',
        body: ({ tipoProcesso }) => getValue(tipoProcesso),
        headerClassName: 'text-end',
      },
    ];

    const { listKey } = this.store;
    const { getDefaultTableProps } = this;
    return (
      <DataTable
        rowHover
        selectionMode="single"
        value={listKey?.slice(0, 2)}
        {...getDefaultTableProps()}
        emptyMessage={'Não existem novos alertas'}
        paginator={false}
        onSelectionChange={this.openWarningsDialog}
        className="no-border datatable mt-2 text-lg"
      >
        {this.renderColumns(columns)}
      </DataTable>
    );
  }

  render() {
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()}>
        {this.getColumns()}
        {this.state.visibleWarningsDialog && this.renderWarningsDialog()}
        <div className="flex justify-content-end ">
          <FcButton
            style={{
              textDecoration: 'underline',
              width: '180px',
              height: '10px',
              marginTop: '13px',
            }}
            label="Exibir todos os alertas..."
            type="button"
            className="p-button-link"
            onClick={this.openWarningsDialog}
          />
        </div>
      </PermissionProxy>
    );
  }
}

AlertasResumo.propTypes = {
  history: PropTypes.any,
};

export default AlertasResumo;
