import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import AccessPermission from '~/constants/AccessPermission';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { DataTable } from 'primereact/datatable';
import { getValue, getValueDate } from 'fc/utils/utils';

@observer
class TabAtributoRequisicaoModal extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.requisicaoModificacao);
  }

  render() {
    const { objetoModificado, requisicaoModificacao } = this.props;

    if (objetoModificado) {
      const defaultAttributes = DadosEstaticosService.getAtributosTipoProcesso(
        requisicaoModificacao.tipoProcesso.toLowerCase()
      );

      if (
        requisicaoModificacao.tipoProcesso === 'ANULACAO_REVOGACAO' &&
        objetoModificado.tipoProcesso === 'CREDENCIAMENTO'
      ) {
        defaultAttributes.push({
          label: 'Data de Aviso',
          field: 'dataAviso',
          valueFunc: (value) => getValueDate(value),
        });
      }
      let atributosList = defaultAttributes
        .filter((attr) => !objetoModificado['new' + attr.field[0]?.toUpperCase() + attr.field?.slice(1)])
        .map((attr) => ({
          label: attr.label,
          valorOriginal: getValue(attr.valueFunc(objetoModificado[attr.field])),
          valorModificado: getValue(objetoModificado['new' + attr.field[0]?.toUpperCase() + attr.field?.slice(1)]),
        }));

      atributosList = atributosList.concat(
        Object.keys(objetoModificado)
          .filter((attr) => attr.slice(0, 3) === 'new')
          .map((attr) => {
            const originalAttr = attr[3].toLowerCase() + attr.slice(4);
            return {
              label:
                DadosEstaticosService.getLabelByAttribute(originalAttr, requisicaoModificacao.tipoProcesso) ??
                originalAttr,
              valorOriginal: getValue(objetoModificado[originalAttr]),
              valorModificado: objetoModificado[originalAttr] !== objetoModificado[attr] && objetoModificado[attr],
            };
          })
      );

      if (objetoModificado?.obra?.modificado === 'EDICAO') {
        const { obra } = objetoModificado;
        obra.newCategoria &&
          atributosList.push({
            label: 'Obra/Categoria',
            valorOriginal: getValue(obra.categoria),
            valorModificado: obra.categoria !== obra.newCategoria && getValue(obra.newCategoria),
          });
        obra.newTipo &&
          atributosList.push({
            label: 'Obra/Tipo',
            valorOriginal: getValue(obra.tipo),
            valorModificado: obra.tipo !== obra.newTipo && getValue(obra.newTipo),
          });
        obra.edificacao?.modificado === 'EDICAO' &&
          atributosList.push({
            label: 'Obra/Geometria',
            valorOriginal: getValue(obra.edificacao.localizacao),
            valorModificado:
              obra.edificacao.localizacao !== obra.edificacao.newLocalizacao &&
              getValue(obra.edificacao.newLocalizacao),
          });
      }

      const styleDefaultModificados = { backgroundColor: 'yellow', textAlign: 'justify' };
      const valoresColumns = [
        { header: '#', field: 'label', style: { width: '15%', fontWeight: 'bold' } },
        {
          header: 'Valores do Processo Original',
          field: 'valorOriginal',
          style: { width: '40%', textAlign: 'justify' },
        },
        {
          header: 'Valores Modificados',
          field: 'valorModificado',
          style: { width: '40%', textAlign: 'justify' },
          body: ({ valorModificado, valorOriginal }) => {
            const isValueModified = valorModificado && valorModificado !== '-';
            return isValueModified ? (
              <b style={isValueModified ? styleDefaultModificados : {}}>{valorModificado}</b>
            ) : valorModificado === '' ? (
              <b style={styleDefaultModificados}>-</b>
            ) : (
              valorOriginal
            );
          },
        },
      ];

      return (
        <DataTable rowHover value={atributosList} className="p-datatable" emptyMessage="Nenhum registro encontrado">
          {this.renderColumns(valoresColumns)}
        </DataTable>
      );
    } else {
      return (
        <div className="card page">
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }
  }
}

TabAtributoRequisicaoModal.propTypes = {
  history: PropTypes.any,
  requisicaoModificacao: PropTypes.object,
  objetoModificado: PropTypes.object,
};

export default TabAtributoRequisicaoModal;
