import { showNotification } from 'fc/utils/utils';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import FormInicioFim from './form';

const FormInicio = observer((props) => {
  const { store, onSuccess } = props;

  const [submitted, setSubmitted] = useState(false);

  useEffect(() => {
    if (submitted) execution();
  }, [submitted]);

  const execution = () => {
    if (!store.rules.hasError) {
      store.validaArquivos(() => store.iniciarObra(onSuccess));
    } else {
      showNotification('error', null, 'Verifique os campos do formulário!');
    }
  };

  const submitFormData = (e) => {
    e?.preventDefault && e.preventDefault();
    if (submitted) {
      execution();
    } else {
      setSubmitted(true);
    }
  };

  return <FormInicioFim {...props} onSubmit={submitFormData} labelOk="Iniciar" attribute="dataInicio" fase="INICIAL" />;
});

FormInicio.propTypes = {
  store: PropTypes.any,
  onCancel: PropTypes.func,
  onSuccess: PropTypes.func,
};

export default FormInicio;
