import FcButton from 'fc/components/FcButton';
import FcCalendar from 'fc/components/FcCalendar';
import FormField from 'fc/components/FormField';
import { DATE_FORMAT } from 'fc/utils/date';
import { observer } from 'mobx-react';
import moment from 'moment/moment';
import { Message } from 'primereact/message';
import PropTypes from 'prop-types';
import { useState } from 'react';
import ObraUpload from '~/pages/geoObras/acompanhamento/obraUpload';

const FormInicioFim = observer((props) => {
  const { store, onCancel, onSubmit, submitted, attribute, fase } = props;

  const [activeIndexForm, setActiveIndexForm] = useState(0);

  const minDate = store.obra?.status === 'EM_ANDAMENTO' ? store.obra?.ultimaMedicao : null;
  const renderDadosBasicos = () => {
    return (
      <div key="dadosBasicosInicioFim" className="p-fluid p-formgrid p-grid">
        <FormField
          columns={6}
          attribute={attribute}
          label="Data da Informacao"
          rule={store.getRule(attribute)}
          submitted={submitted}
        >
          <>
            <FcCalendar
              value={store.object[attribute] ? moment(store.object[attribute])._d : null}
              placeholder="Selecione a data"
              onChange={(e) => store.updateAttributeDate(attribute, e)}
              minDate={minDate ? moment(minDate)._d : null}
            />
            {minDate && (
              <small>{`A data final da última medição foi ${moment(store.obra?.ultimaMedicao)?.format(
                DATE_FORMAT
              )}.`}</small>
            )}
          </>
        </FormField>

        <div className="p-grid p-col-12 p-p-0 mt-3">
          <ObraUpload
            chooseLabel="Adicionar Georreferenciamento"
            accept=".kml"
            max={1}
            tipos={store?.fileStore.tipoArquivoEnum?.filter(
              (tipo) => tipo.fase === fase && tipo.value === 'GEORREFERENCIAMENTO'
            )}
            onChangeFiles={(arquivos) => store.setArquivos(arquivos)}
            arquivos={store?.arquivos?.filter(
              (arquivo) => arquivo.fase === fase && arquivo.tipo === 'GEORREFERENCIAMENTO'
            )}
            store={store?.fileStore}
            updateAttributeFile={(key, attribute, value) => store.updateAttributeFile(key, attribute, value)}
            emptyMenssage="Georreferenciamento não adicionado!"
            fase={store.fase}
          />
        </div>
      </div>
    );
  };

  const renderFotos = () => {
    return (
      <div key="fotos" className="p-fluid p-formgrid p-grid mt-3">
        <div className="p-grid p-col-12 ">
          <ObraUpload
            chooseLabel="Adicionar Fotos"
            accept="image/*"
            tipos={store?.fileStore.tipoArquivoEnum?.filter((tipo) => tipo.fase === fase && tipo.value === 'FOTO')}
            onChangeFiles={(arquivos) => store.setArquivos(arquivos)}
            arquivos={store?.arquivos?.filter((arquivo) => arquivo.fase === fase && arquivo.tipo === 'FOTO')}
            store={store?.fileStore}
            updateAttributeFile={(key, attribute, value) => store.updateAttributeFile(key, attribute, value)}
            emptyMenssage="Nenhuma imagem adicionada!"
            fase={store.fase}
          />
        </div>
      </div>
    );
  };

  const onBack = () => {
    setActiveIndexForm(activeIndexForm - 1);
  };

  const onNext = () => {
    setActiveIndexForm(activeIndexForm + 1);
  };

  const isDisabledAvancar = () => {
    const errorsMessages = {
      message: '',
      disabled: true,
    };
    if (activeIndexForm === 0 && !store.validaDadosBasicos(attribute)) {
      errorsMessages.message =
        'É necessário preencher todos os campos obrigatórios antes de avançar para a próxima etapa.';
    } else if (activeIndexForm === 1 && !store.validaFotos()) {
      errorsMessages.message = 'É necessário adicionar fotos antes de avançar para a próxima etapa.';
    } else {
      errorsMessages.disabled = false;
    }
    return errorsMessages;
  };

  const renderActionButtons = () => {
    const disabledAvancar = isDisabledAvancar();
    return (
      <div className="flex">
        <span className="p-ml-auto">
          {disabledAvancar.disabled && (
            <Message className="p-ml-auto mr-2 mt-2" severity="warn" text={disabledAvancar.message} />
          )}
          {activeIndexForm === 0 && (
            <FcButton
              type="button"
              label="Cancelar"
              className="p-button-secondary mr-2 mt-2"
              loading={store.loading}
              onClick={() => onCancel && onCancel()}
            />
          )}
          {activeIndexForm > 0 && (
            <FcButton className="p-button-outlined mt-2 mr-2" type="button" label="Voltar" onClick={() => onBack()} />
          )}
          {activeIndexForm === 0 ? (
            <FcButton
              className="mt-2"
              type="button"
              label="Avancar"
              disabled={disabledAvancar.disabled}
              onClick={() => onNext()}
            />
          ) : (
            <FcButton
              className="mt-2"
              type="button"
              disabled={!store.validaForm(attribute)}
              label={props.labelOk}
              loading={store.loading}
              onClick={() => onSubmit()}
            />
          )}
        </span>
      </div>
    );
  };

  const forms = [
    {
      label: 'Dados Básicos',
      step: 0,
      body: renderDadosBasicos(),
    },
    {
      label: 'Fotos',
      step: 1,
      body: renderFotos(),
    },
  ];

  return (
    <form onSubmit={onSubmit}>
      {forms.find((item) => item.step === activeIndexForm).body}
      {renderActionButtons()}
    </form>
  );
});

FormInicioFim.propTypes = {
  labelOk: PropTypes.string,
  store: PropTypes.any,
  onCancel: PropTypes.func,
  onSuccess: PropTypes.func,
  onSubmit: PropTypes.func,
  submitted: PropTypes.bool,
};

export default FormInicioFim;
