import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import { Dialog } from 'primereact/dialog';
import { ProgressBar } from 'primereact/progressbar';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { InputText } from 'primereact/inputtext';
import { confirmDialog, ConfirmDialog } from 'primereact/confirmdialog';
import { FilterMatchMode } from 'primereact/api';
import { Chip } from 'primereact/chip';
import { Toast } from 'primereact/toast';
import { OverlayPanel } from 'primereact/overlaypanel';
import FcButton from 'fc/components/FcButton';
import AbaListagem from '../../abaListagem';
import DialogAddOrEditItem from '../dialogAddOrEditItem';
import SicroSinapiFormStore from '~/stores/geoObras/sicroSinapi/formStore';
import DialogItemDetails from '../dialogItemDetails';
import TextDiff from './textDiff';
import Legend from './legend';
import './style.scss';
import { getValueMoney } from 'fc/utils/utils';
import { Tag } from 'primereact/tag';
import Tooltip from 'fc/components/Tooltip';

@observer
class DialogItemsMatching extends React.Component {
  constructor(props) {
    super(props);

    this.store = new SicroSinapiFormStore();

    this.state = {
      filters: {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
      },
      dialogSelectItemVisible: false,
      dialogAddOrEditItemVisible: false,
      dialogItemDetailVisible: false,
      selectedItem: null,
      expandedRows: null,
    };

    this.onGlobalFilterChange = this.onGlobalFilterChange.bind(this);
    this.baseBodyTemplate = this.baseBodyTemplate.bind(this);
    this.scoreBodyTemplate = this.scoreBodyTemplate.bind(this);
    this.actionBodyTemplate = this.actionBodyTemplate.bind(this);
    this.rowExpansionTemplate = this.rowExpansionTemplate.bind(this);
  }

  componentDidMount() {
    this.store.initialize(this.props.reportId);
  }

  onGlobalFilterChange(event) {
    const value = event.target.value;
    const filters = { ...this.state.filters };
    filters.global.value = value;

    this.setState({ filters });
  }

  toggleDialogSelectItem(item) {
    this.setState({ selectedItem: item ?? null, dialogSelectItemVisible: !this.state.dialogSelectItemVisible });
  }

  toggleDialogAddOrEditItem(item) {
    this.setState({ selectedItem: item ?? null, dialogAddOrEditItemVisible: !this.state.dialogAddOrEditItemVisible });
  }

  toggleDialogItemDetail(item) {
    this.setState({ selectedItem: item ?? null, dialogItemDetailVisible: !this.state.dialogItemDetailVisible });
  }

  baseBodyTemplate(rowData) {
    return <span className={`base-badge base-${rowData.base}`}>{rowData.base}</span>;
  }

  scoreBodyTemplate(rowData) {
    return rowData.score >= 0 ? (
      <ProgressBar
        value={rowData.score}
        showValue={true}
        color={rowData.score < 75 ? '#f87171' : rowData.score >= 75 && rowData.score < 90 ? '#facc15' : '#4ade80'}
      />
    ) : rowData.itemObraNonMatching ? (
      <Chip
        label="Item Substituto"
        icon="pi pi-arrow-right-arrow-left"
        style={{ backgroundColor: '#2196f3', color: '#fff' }}
      />
    ) : (
      '-'
    );
  }

  actionBodyTemplate(rowData) {
    return (
      <div className="actions p-d-flex p-jc-end">
        <FcButton
          icon="pi pi-eye"
          type="button"
          className="p-button-sm p-button-primary p-mr-2"
          onClick={() => {
            this.toggleDialogItemDetail(rowData);
          }}
        />
        {!rowData.itemObraNonMatching ? (
          <FcButton
            icon="pi pi-arrow-right-arrow-left"
            type="button"
            className="p-button-sm p-button-info p-mr-2"
            onClick={() => this.toggleDialogSelectItem(rowData)}
          />
        ) : (
          <FcButton
            icon="pi pi-pencil"
            type="button"
            className="p-button-sm p-button-success p-mr-2"
            onClick={() => this.toggleDialogAddOrEditItem(rowData)}
          />
        )}
        <FcButton
          icon="pi pi-trash"
          className="p-button-sm p-button-danger"
          onClick={() => {
            confirmDialog({
              message: 'Você tem certeza, que deseja desconsiderar o item?',
              header: 'Desconsiderar Item',
              icon: 'pi pi-exclamation-triangle',
              acceptClassName: 'p-button-danger',
              accept: () => this.store.deleteItem(rowData.codigo),
            });
          }}
        />
      </div>
    );
  }

  getColumns(selectedColumns = []) {
    const columns = [
      {
        name: 'expander',
        expander: (rowData) => !!rowData.itemObraNonMatching,
      },
      {
        name: 'codigo',
        style: { minWidth: '8rem' },
        field: 'codigo',
        header: 'Código',
        sortable: true,
      },
      {
        name: 'base',
        style: { minWidth: '8rem' },
        field: 'base',
        header: 'Base',
        sortable: true,
        body: this.baseBodyTemplate,
      },
      {
        name: 'descricaoPlanilha',
        style: { minWidth: '12rem' },
        field: 'descricaoPlanilha',
        header: 'Descrição da Planilha',
        sortable: true,
        body: (rowData) =>
          rowData.descricaoPlanilha ? (
            <TextDiff
              originalText={rowData.descricaoPlanilha}
              modifiedText={rowData.referencia.descricao}
              highlightAdditions={false}
            />
          ) : (
            '-'
          ),
      },
      {
        name: 'referencia.descricao',
        style: { minWidth: '12rem' },
        field: 'referencia.descricao',
        header: 'Descrição do Item Correspondente',
        sortable: true,
        body: (rowData) =>
          rowData.referencia.descricao ? (
            <TextDiff
              originalText={rowData.descricaoPlanilha}
              modifiedText={rowData.referencia.descricao}
              highlightDeletions={false}
            />
          ) : (
            '-'
          ),
      },
      {
        field: 'sobrePreco',
        name: 'sobrePreco',
        style: { minWidth: '12' },
        header: 'SobrePreço/Subpreço',
        sortable: true,
        body: (rowData) => this.renderTag(rowData),
      },
      {
        name: 'score',
        style: { minWidth: '12rem' },
        field: 'score',
        header: 'Correspondência',
        sortable: true,
        body: this.scoreBodyTemplate,
      },
      {
        name: 'actions',
        style: { minWidth: '4rem' },
        body: this.actionBodyTemplate,
      },
    ];

    return columns
      .filter((col) => selectedColumns.includes(col.name) || selectedColumns.length === 0)
      .map((col) => {
        return <Column {...col} key={`col-${col.field}`} />;
      });
  }

  rowExpansionTemplate(data) {
    const columns = this.getColumns([
      'codigo',
      'base',
      'descricaoPlanilha',
      'sobrePreco',
      'referencia.descricao',
      'score',
    ]);
    return (
      data.itemObraNonMatching && (
        <div style={{ paddingLeft: '5rem' }}>
          <i className="pi pi-reply" style={{ fontSize: '2rem', transform: 'rotateX(180deg)', color: '#f87171' }}></i>
          <div style={{ paddingLeft: '3rem', bottom: '2.2rem', position: 'relative' }}>
            <DataTable className="card" value={[data.itemObraNonMatching]} responsiveLayout="scroll">
              {columns}
              <Column
                body={(rowData) => (
                  <div className="actions p-d-flex p-jc-end">
                    <FcButton
                      icon="pi pi-eye"
                      type="button"
                      className="p-button-sm p-button-primary p-mr-2"
                      onClick={() => this.toggleDialogItemDetail(rowData)}
                    />
                    <FcButton
                      icon="pi pi-times"
                      type="button"
                      className="p-button-sm p-button-danger p-mr-2"
                      onClick={() => {
                        this.collapseRow(data);
                        this.store.excluirItemObraNonMatching(data.codigo);
                      }}
                    />
                  </div>
                )}
              />
            </DataTable>
          </div>
        </div>
      )
    );
  }

  renderDialogSelectItem() {
    const itemSelectedDescription = this.state?.selectedItem?.descricaoPlanilha || '';
    return (
      <Dialog
        blockScroll
        header="Selecione o Item para substituição"
        visible={this.state.dialogSelectItemVisible}
        onHide={() => this.toggleDialogSelectItem()}
        style={{ width: '80vw' }}
        draggable={false}
        resizable={false}
      >
        <AbaListagem
          actionButtonLabel="Selecionar"
          DialogAddOrEditItemHeaderLabel="Selecionar Item"
          DialogAddOrEditItemSubHeaderLabel="Detalhe o item para selecioná-lo"
          storeForm={this.store}
          showAddItems={false}
          advancedSearchWithAlwaysDrawer={true}
          onSelectedItem={(item) => {
            this.store.calculaSobrePreco(item);
            this.store.saveItemObraNonMatching(this.state.selectedItem.codigo, item);
            this.toggleDialogSelectItem();
          }}
          itemDescricao={itemSelectedDescription}
        />
      </Dialog>
    );
  }

  renderDialogAddOrEditItem() {
    return (
      this.state.dialogAddOrEditItemVisible && (
        <DialogAddOrEditItem
          headerLabel="Selecionar Item"
          subHeaderLabel="Detalhe o item para selecioná-lo"
          item={this.state.selectedItem}
          visible={this.state.dialogAddOrEditItemVisible}
          onCancel={() => this.toggleDialogAddOrEditItem()}
          onConfirm={(item) => {
            this.store.saveItem(item, this.state.selectedItem.codigo);
            this.toggleDialogAddOrEditItem();
          }}
        />
      )
    );
  }

  renderTag({ sobrePreco }) {
    return (
      <Tag
        style={{
          color: sobrePreco >= 0 ? '#22c55e' : '#ef4444',
          backgroundColor: '#F5F9FF',
          border: `1px solid ${sobrePreco >= 0 ? '#22c55e' : '#ef4444'}`,
        }}
      >
        <Tooltip value={sobrePreco >= 0 ? 'Subpreço' : 'Sobrepreço'}>
          <span className="flex gap-1 align-items-center">
            <span>
              {sobrePreco >= 0 ? 'Subpreço' : 'Sobrepreço'} {getValueMoney(sobrePreco)}
            </span>
          </span>
        </Tooltip>
      </Tag>
    );
  }

  processItemsForListing() {
    return this.store.itensObra
      .filter((item) => item.importado || item.itemObraNonMatching)
      .map((item) => (item.score ? item : { ...item, score: -1 }));
  }

  collapseRow(rowData) {
    const expandedRows = { ...this.state.expandedRows };

    delete expandedRows[rowData.id];

    this.setState({ expandedRows });
  }

  renderLegend() {
    return (
      <div>
        <Toast ref={(el) => (this.toast = el)} />
        <div className="card">
          <OverlayPanel ref={(el) => (this.op = el)} id="overlay_panel" style={{ width: '334px' }}>
            <Legend
              title="Descrições"
              itens={[
                { name: 'Textos Removidos', color: '#dc2626' },
                { name: 'Textos Adicionados', color: '#16a34a' },
              ]}
            />
            <Legend
              title="Correspondência"
              itens={[
                { name: 'Menor que 75%', color: '#f87171' },
                { name: 'Maior Igual a 75% e Menor que 90%', color: '#facc15' },
                { name: 'Maior Igual a 90%', color: '#4ade80' },
              ]}
            />
          </OverlayPanel>
        </div>
      </div>
    );
  }

  renderHeader() {
    return (
      <div className="table-header">
        <span className="p-input-icon-left">
          <i className="pi pi-search" />
          <InputText
            type="search"
            value={this.state.filters.global.value || ''}
            onChange={(e) => this.onGlobalFilterChange(e)}
            placeholder="Buscar item"
          />
        </span>
        <FcButton icon="pi pi-info" className="p-button-rounded p-button-help" onClick={(e) => this.op.toggle(e)} />
      </div>
    );
  }

  renderFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => {
            this.props.onCancel();
          }}
          className="p-button-outlined p-button-brand-light"
        />
        <FcButton
          label="Confirmar"
          icon="pi pi-check"
          onClick={() => {
            confirmDialog({
              message:
                'Tem certeza que deseja confirmar a correspondência dos itens?. Saiba que esse processo só pode ser realizado uma única vez.',
              header: 'Confirmar Correspondência',
              icon: 'pi pi-exclamation-triangle',
              acceptClassName: 'p-button-success',
              accept: () => {
                this.store.updateAttribute('correspondenciaRealizada', true);
                this.store.save(this.props.onSuccessfulSave);
              },
            });
          }}
        />
      </div>
    );
  }

  render() {
    const columns = this.getColumns();

    return (
      <Dialog
        header="Correspondência dos Itens"
        style={{ width: '95vw', height: '100vh' }}
        visible={this.props.visible}
        maximizable
        footer={this.renderFooter()}
        onHide={() => {
          this.props.onCancel();
        }}
      >
        <div className="datatable-items-matching">
          <DataTable
            rows={10}
            className="p-datatable-items"
            rowClassName={(rowData) => (rowData.itemObraNonMatching ? 'p-highlight' : '')}
            dataKey="id"
            filters={this.state.filters}
            onFilter={(e) => this.setState({ filters: e.filters })}
            value={this.processItemsForListing()}
            expandedRows={this.state.expandedRows}
            onRowToggle={(e) => this.setState({ expandedRows: e.data })}
            rowExpansionTemplate={this.rowExpansionTemplate}
            loading={this.store.loading}
            responsiveLayout="scroll"
            globalFilterFields={['codigo', 'base', 'descricaoPlanilha', 'referencia.descricao', 'score']}
            header={this.renderHeader()}
            emptyMessage="Sem itens encontrados."
            paginator
            removableSort
            sortField="score"
            sortOrder={1}
          >
            {columns}
          </DataTable>
          <DialogItemDetails
            item={this.state.selectedItem}
            visible={this.state.dialogItemDetailVisible}
            onHide={() => this.toggleDialogItemDetail()}
          />
          <ConfirmDialog />
          {this.state.selectedItem && this.renderDialogSelectItem()}
          {this.renderDialogAddOrEditItem()}
          {this.renderLegend()}
        </div>
      </Dialog>
    );
  }
}

DialogItemsMatching.propTypes = {
  reportId: PropTypes.number,
  visible: PropTypes.bool,
  onCancel: PropTypes.func,
  onSuccessfulSave: PropTypes.func,
};

DialogItemsMatching.defaultProps = {
  reportId: null,
  visible: false,
  onCancel: () => {},
  onSuccessfulSave: () => {},
};

export default DialogItemsMatching;
