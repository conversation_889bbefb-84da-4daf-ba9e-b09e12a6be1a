import React from 'react';
import AccessPermission from '~/constants/AccessPermission';
import './style.scss';
import * as echarts from 'echarts';
import PainelAtosLicitacaoIndexStore from '~/stores/painelAtosLicitacoes/indexStore';
import ChartSelectDate from './chartSelectDate';
import { PropTypes } from 'mobx-react';

class PieChart extends React.Component {
  constructor(props) {
    super(props, AccessPermission.painelAtosLicitacoes);
    this.store = new PainelAtosLicitacaoIndexStore();
    this.chartRef = React.createRef();
  }

  renderChart(dataChart) {
    const chartDom = this.chartRef.current;
    if (chartDom) {
      const myChart = echarts.init(chartDom);
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            const valorLicitacao = new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(
              params.data.dataChart || 0
            );
            const quantidadeLicitacao = params.data.value || 0;
            return `${params.name}<br/>Quantidade de licitações: ${quantidadeLicitacao}<br/>Valor total: ${valorLicitacao}`;
          },
        },
        series: [
          {
            type: 'pie',
            radius: '70%',
            label: {
              fontSize: 14,
              fontWeight: 'bold',
            },
            data: [
              {
                value: dataChart?.[this.props.values[0]],
                valor: dataChart?.[this.props.prices[0]],
                name: this.props.labels[0],
              },
              {
                value: dataChart?.[this.props.values[1]],
                valor: dataChart?.[this.props.prices[1]],
                name: this.props.labels[1],
              },
            ],
            itemStyle: {
              color: (params) => {
                const colorList = [this.props.colors[0], this.props.colors[1]];
                return colorList[params.dataIndex];
              },
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      };

      myChart.setOption(option);
      this.chartInitialized = true;
      return myChart;
    }
  }

  render() {
    return (
      <ChartSelectDate
        chartRef={this.chartRef}
        typeGraph={'pie'}
        values={this.props.values}
        renderChart={this.renderChart.bind(this)}
      />
    );
  }
}

PieChart.propTypes = {
  values: PropTypes.array,
  prices: PropTypes.array,
  labels: PropTypes.array,
  colors: PropTypes.array,
};

export default PieChart;
