import React from 'react';
import { observer } from 'mobx-react';
import FcButton from 'fc/components/FcButton';
import { Column } from 'react-virtualized';
import { DataTable } from 'primereact/datatable';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import ConsultaDetalhamentoIndexStore from '../../stores/consultaMateriais/indexStore';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import { getValueByKey } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import './Style.scss';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import MaterialIndexStore from '~/stores/consultaMateriais/MaterialStore';
import { Divider } from 'primereact/divider';
import { Tag } from 'primereact/tag';
import ListUnidadeMedida from '~/components/listUnidadeMedida';

@observer
class FormMaterial extends GenericIndexPage {
  store;
  constructor(props) {
    super(props);
    this.store = new ConsultaDetalhamentoIndexStore();
    this.materialStore = new MaterialIndexStore();
    this.state = {
      activeTabIndex: 0,
      previousTabIndex: 0,
    };
  }

  _renderDescricaoMaterial(row) {
    const unidadesMedida = row?.pdm?.unidadesMedida ?? row?.unidadesMedida ?? [];

    return (
      <div className="flex align-items-center">
        <div style={{ minWidth: '30%' }}>
          <div style={{ fontWeight: 'bold', fontSize: '14px', whiteSpace: 'nowrap' }}>
            {row.nomeMaterial ?? row.pdm?.nome ?? ''}
          </div>
          <div
            className="flex flex-wrap align-items-center mt-2 gap-2"
            style={{ color: '#9E9E9E', whiteSpace: 'nowrap' }}
          >
            <div style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
              Classe:{' '}
              {(row?.descricaoClasse || row.pdm?.classe?.descricao) &&
                (row.descricaoClasse ?? row.pdm?.classe?.descricao ?? '').charAt(0).toUpperCase() +
                  (row.descricaoClasse ?? row.pdm?.classe?.descricao ?? '').slice(1).toLowerCase()}
            </div>
            <div>Código: {row?.codigo}</div>
            <div>Código PDM: {row?.codigoPdm}</div>
          </div>
        </div>
        {row.tipo !== 'S' && (
          <>
            <Divider layout="vertical" />
            <div style={{ overflowY: 'hidden' }} className="flex flex-wrap gap-1 w-full h-auto">
              {row?.caracteristicas?.map((caracteristica) => (
                <div>
                  <Tag style={{ color: '#3F51B5', backgroundColor: 'rgba(63, 81, 181, 0.2)' }}>
                    <span>{caracteristica?.textCaracteristica ?? caracteristica.caracteristica}</span>
                  </Tag>
                </div>
              ))}
              <ListUnidadeMedida uniqueKey={row.id} unidadesMedida={unidadesMedida} />
            </div>
          </>
        )}
      </div>
    );
  }

  onPage(event, filter) {
    const page = (event.page ?? 0) + 1;
    let pagination = {};
    if (filter === 'servicos') {
      pagination = JSON.parse(JSON.stringify(this.store.paginationServicos));
    } else if (filter === 'materiais') {
      pagination = JSON.parse(JSON.stringify(this.store.paginationMateriais));
    } else {
      pagination = JSON.parse(JSON.stringify(this.store.pagination));
    }
    pagination.page.index = page;
    this.store.setPagination(pagination, filter);
    this.store.load(pagination, filter);
  }

  getFirstFromPagination(filter) {
    let page = {};
    if (filter === 'servicos') {
      page = this.store.paginationServicos.page;
    } else if (filter === 'materiais') {
      page = this.store.paginationMateriais.page;
    } else {
      page = this.store.pagination.page;
    }
    const { index, size } = page;
    return size * (index - 1);
  }

  getDefaultTableProps() {
    return {
      dataKey: 'id',
      className: 'datatable-responsive',
      paginator: true,
      lazy: true,
      rows: 10,
      emptyMessage: 'Nenhum dado encontrado.',
      resizableColumns: true,
      columnResizeMode: 'fit',
      paginatorTemplate: 'FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport',
      currentPageReportTemplate: '{first} - {last} de {totalRecords} registros',
      onPage: this.onPage,
      first: this.getFirstFromPagination(),
      totalRecords: this.store.pagination.total ?? 0,
    };
  }

  renderTabs() {
    const { onSetMaterial, moveToSelectedItens } = this.props;
    const { loading, listKey } = this.store;

    const columns = [
      {
        body: (row) => this._renderDescricaoMaterial(row),
      },
      {
        style: { width: '110px', textAlign: 'right' },
        body: (row) => (
          <FcButton
            className=" p-button-outlined p-button-rounded"
            label={
              row.itemSuspenso === 'S'
                ? 'Suspenso'
                : row.status === 'I'
                ? 'Inativo'
                : row.status === 'D'
                ? 'Depreciado'
                : 'Adicionar'
            }
            disabled={row.itemSuspenso === 'S' || row.status === 'I' || row.status === 'D'}
            type="button"
            onClick={() => {
              this.materialStore.fetchMaterialById(row.id, (materialData) => {
                onSetMaterial(materialData);
                moveToSelectedItens && moveToSelectedItens();
              });
            }}
          />
        ),
      },
    ];

    const tabs = [];
    tabs.push({
      id: 0,
      header: 'Todos',
      content: (
        <DataTable
          rowHover
          responsiveLayout="scroll"
          tabIndex={'key'}
          value={listKey}
          {...this.getDefaultTableProps()}
          className="p-datatable-sm materiais-table p-2 min-w-0"
          loading={loading}
          onSort={(e) => this.onSort(e, 'todos')}
          onPage={(e) => this.onPage(e, 'todos')}
          first={this.getFirstFromPagination('todos')}
          style={{ border: '1px #E0E0E0 solid', borderRadius: '10px' }}
          emptyMessage="Nenhum material/serviço encontrado"
        >
          <Column
            style={{ width: '25px' }}
            key="col-servico"
            body={({ tipo }) => (
              <span
                className={`tipo-item-badge ${getValueByKey(
                  tipo,
                  DadosEstaticosService.getTipoMaterial(),
                  'value',
                  'name'
                )}`}
              >
                {getValueByKey(tipo, DadosEstaticosService.getTipoMaterial(), 'value', 'singularText')}
              </span>
            )}
          />
          {this.renderColumns(columns)}
        </DataTable>
      ),
    });
    if (this.store.listMateriais?.length > 0) {
      tabs.push({
        id: tabs.length,
        header: 'Materiais',
        content: (
          <DataTable
            rowHover
            responsiveLayout="scroll"
            tabIndex={'key'}
            value={this.store.listMateriais}
            {...this.getDefaultTableProps()}
            className="p-datatable-sm materiais-table p-2"
            totalRecords={this.store.paginationMateriais.total ?? 0}
            loading={loading}
            onSort={(e) => this.onSort(e, 'materiais')}
            onPage={(e) => this.onPage(e, 'materiais')}
            first={this.getFirstFromPagination('materiais')}
            style={{ border: '1px #E0E0E0 solid', borderRadius: '10px' }}
            emptyMessage="Nenhum material encontrado"
          >
            {this.renderColumns(columns)}
          </DataTable>
        ),
      });
    }
    if (this.store.listServicos?.length > 0) {
      tabs.push({
        id: tabs.length,
        header: 'Serviços',
        content: (
          <DataTable
            rowHover
            responsiveLayout="scroll"
            tabIndex={'key'}
            value={this.store.listServicos}
            {...this.getDefaultTableProps()}
            className="p-datatable-sm materiais-table p-2"
            totalRecords={this.store.paginationServicos.total ?? 0}
            loading={loading}
            onSort={(e) => this.onSort(e, 'servicos')}
            onPage={(e) => this.onPage(e, 'servicos')}
            first={this.getFirstFromPagination('servicos')}
            style={{ border: '1px #E0E0E0 solid', borderRadius: '10px' }}
            emptyMessage="Nenhum serviço encontrado"
          >
            {this.renderColumns(columns)}
          </DataTable>
        ),
      });
    }

    if (tabs.length === 1 && this.state.activeTabIndex > 0) {
      this.setState({ activeTabIndex: 0, previousTabIndex: 0 });
    } else if (tabs.length === 2 && this.state.activeTabIndex === 2) {
      this.setState({ activeTabIndex: 1 });
    } else if (tabs.length === 3 && this.state.activeTabIndex === 1 && this.state.previousTabIndex === 2) {
      this.setState({ activeTabIndex: 2, previousTabIndex: 0 });
    }

    let content = <></>;
    content = (
      <div className="p-col-12 relative">
        <div className="absolute" style={{ top: 0, right: 0, zIndex: 10 }}>
          <div className="relative">
            <span className="absolute badge-qtd-itens" style={{ backgroundColor: '#FF3D32', color: '#f1f5f9' }}>
              {this.props.qtdItens}
            </span>
            <FcButton
              icon="pi pi-shopping-cart"
              onClick={this.props.onClickCarrinho}
              className="mr-2 p-button-rounded p-button-outlined ml-1 "
              type="button"
            ></FcButton>
          </div>
        </div>
        <FcCloseableTabView
          tabs={tabs}
          activeTabIndex={this.state.activeTabIndex}
          onChangeTab={(tab) => this.setState({ activeTabIndex: tab.id })}
          disableRedirect
        />
      </div>
    );
    return content;
  }

  render() {
    let content = <></>;
    if (this.store) {
      content = (
        <div className="grid">
          <div className="flex col-12 mt-2 flex-column align-items-end">
            <AdvancedSearch
              searchParams={this.store.getAdvancedSearchParams()}
              store={this.store}
              searchFields={['query']}
              className="w-full"
              onSearch={(searchParams) => {
                if (searchParams.andParameters.length > 0) {
                  this.setState({ previousTabIndex: this.state.activeTabIndex });
                }
              }}
              elasticsearch
            />
          </div>
          {this.renderTabs()}
        </div>
      );
    }
    return content;
  }
}

export default FormMaterial;
