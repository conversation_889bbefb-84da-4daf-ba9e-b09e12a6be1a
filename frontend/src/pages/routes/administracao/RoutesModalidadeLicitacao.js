import { Switch, Route } from 'react-router';
import UrlRouter from '~/constants/UrlRouter';
import ModalidadeLicitacaoIndexPage from '~/pages/modalidadeLicitacao';
import EditModalidadeLicitacao from '~/pages/modalidadeLicitacao/edit';
import NewModalidadeLicitacao from '~/pages/modalidadeLicitacao/new';
import NotFound from 'fc/pages/NotFound';

const RoutesModalidadeLicitacao = () => {
  return (
    <Switch>
      <Route path={UrlRouter.administracao.modalidadeLicitacao.index} exact component={ModalidadeLicitacaoIndexPage} />
      <Route path={UrlRouter.administracao.modalidadeLicitacao.editar} exact component={EditModalidadeLicitacao} />
      <Route path={UrlRouter.administracao.modalidadeLicitacao.novo} exact component={NewModalidadeLicitacao} />
      <Route component={NotFound} />
    </Switch>
  );
};

export default RoutesModalidadeLicitacao;
