import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import ModalidadeLicitacao from '~/domains/ModalidadeLicitacao';
import ModalidadeLicitacaoService from '../../services/ModalidadeLicitacaoService';
import IndexBase from 'fc/stores/IndexBase';

class ModalidadeLicitacaoIndexStore extends IndexBase {
  constructor() {
    super(ModalidadeLicitacaoService, ModalidadeLicitacao, 'nome');
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'nome',
        label: 'Nome',
        type: SearchTypes.TEXT,
      },
      {
        field: 'permiteConsorcio',
        label: 'Permite Consórcio',
        type: SearchTypes.BOOLEAN,
      },
    ];
  }
}

export default ModalidadeLicitacaoIndexStore;
