import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import ModalidadeLicitacao from '~/domains/ModalidadeLicitacao';
import ModalidadeLicitacaoService from '../../services/ModalidadeLicitacaoService';
import DadosEstaticosService from '../../services/DadosEstaticosService';
import IndexBase from 'fc/stores/IndexBase';

class ModalidadeLicitacaoIndexStore extends IndexBase {
  constructor() {
    super(ModalidadeLicitacaoService, ModalidadeLicitacao, 'nome');
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'nome',
        label: 'Nome',
        type: SearchTypes.TEXT,
      },
      {
        field: 'vigenciaDe',
        label: 'Vigência De',
        type: SearchTypes.DATE_TIME,
      },
      {
        field: 'vigenciaAte',
        label: 'Vigência Até',
        type: SearchTypes.DATE_TIME,
      },
      {
        field: 'legislacao',
        label: 'Legislação',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTipoLicitacaoLegislacao(),
      },
      {
        field: 'permiteConsorcio',
        label: 'Permite Consórcio',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getSimNao(),
      },
    ];
  }
}

export default ModalidadeLicitacaoIndexStore;
