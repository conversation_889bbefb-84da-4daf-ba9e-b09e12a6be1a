import ModalidadeLicitacao from '~/domains/ModalidadeLicitacao';
import ModalidadeLicitacaoService from '../../services/ModalidadeLicitacaoService';
import FormBase from 'fc/stores/FormBase';

class ModalidadeLicitacaoFormStore extends FormBase {
  constructor() {
    super(ModalidadeLicitacaoService, ModalidadeLicitacao);
  }

  rulesDefinition() {
    let rules = {
      nome: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
      ],
      vigenciaDe: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      vigenciaAte: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      legislacao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      permiteConsorcio: [
        {
          rule: (value) => {
            const normalizedValue = !value ? false : value;
            return typeof normalizedValue === 'boolean';
          },
          message: 'O campo Permite Consórcio deve ser verdadeiro ou falso',
        },
      ],
    };

    if (this.object?.vigenciaAte) {
      rules = this.mergeRules(rules, {
        vigenciaDe: [
          {
            rule: (value) => {
              if (!value || !this.object.vigenciaAte) return true;
              const vigenciaDe = new Date(value);
              const vigenciaAte = new Date(this.object.vigenciaAte);
              return vigenciaDe <= vigenciaAte;
            },
            message: 'A data de vigência inicial deve ser anterior ou igual à data de vigência final',
          },
        ],
      });
    }

    if (this.object?.vigenciaDe) {
      rules = this.mergeRules(rules, {
        vigenciaAte: [
          {
            rule: (value) => {
              if (!value || !this.object.vigenciaDe) return true;
              const vigenciaDe = new Date(this.object.vigenciaDe);
              const vigenciaAte = new Date(value);
              return vigenciaAte >= vigenciaDe;
            },
            message: 'A data de vigência final deve ser posterior ou igual à data de vigência inicial',
          },
        ],
      });
    }

    return rules;
  }

  getObjectToSave(actionType) {
    const objectToSave = super.getObjectToSave(actionType);

    if (objectToSave.hasOwnProperty('permiteConsorcio')) {
      objectToSave.permiteConsorcio =
        objectToSave.permiteConsorcio === undefined || objectToSave.permiteConsorcio === null
          ? false
          : Boolean(objectToSave.permiteConsorcio);
    }

    if (objectToSave.hasOwnProperty('legislacao')) {
      if (Array.isArray(objectToSave.legislacao)) {
        objectToSave.legislacao = objectToSave.legislacao.length > 0 ? objectToSave.legislacao[0] : null;
      }
    }

    return objectToSave;
  }
}

export default ModalidadeLicitacaoFormStore;
