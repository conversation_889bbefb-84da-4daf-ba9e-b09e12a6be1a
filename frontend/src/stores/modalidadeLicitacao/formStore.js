import ModalidadeLicitacao from '~/domains/ModalidadeLicitacao';
import ModalidadeLicitacaoService from '../../services/ModalidadeLicitacaoService';
import FormBase from 'fc/stores/FormBase';

class ModalidadeLicitacaoFormStore extends FormBase {
  constructor() {
    super(ModalidadeLicitacaoService, ModalidadeLicitacao);
  }

  rulesDefinition() {
    return {
      nome: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
      ],
      vigenciaDe: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      vigenciaAte: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      legislacao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      permiteConsorcio: [
        {
          rule: (value) => {
            // Normaliza o valor para boolean se for undefined ou null
            const normalizedValue = value === undefined || value === null ? false : value;
            return typeof normalizedValue === 'boolean';
          },
          message: 'O campo permiteConsorcio deve ser verdadeiro ou falso',
        },
      ],
    };
  }

  getObjectToSave(actionType) {
    const objectToSave = super.getObjectToSave(actionType);

    // Normaliza o campo permiteConsorcio para garantir que seja sempre boolean
    if (objectToSave.hasOwnProperty('permiteConsorcio')) {
      objectToSave.permiteConsorcio =
        objectToSave.permiteConsorcio === undefined || objectToSave.permiteConsorcio === null
          ? false
          : Boolean(objectToSave.permiteConsorcio);
    }

    return objectToSave;
  }
}

export default ModalidadeLicitacaoFormStore;
