import ModalidadeLicitacao from '~/domains/ModalidadeLicitacao';
import ModalidadeLicitacaoService from '../../services/ModalidadeLicitacaoService';
import FormBase from 'fc/stores/FormBase';

class ModalidadeLicitacaoFormStore extends FormBase {
  constructor() {
    super(ModalidadeLicitacaoService, ModalidadeLicitacao);
  }

  rulesDefinition() {
    return {
      nome: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
      ],
      vigenciaDe: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      vigenciaAte: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      legislacao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      permiteConsorcio: [
        {
          rule: (value) => typeof value === 'boolean',
          message: 'O campo permiteConsorcio deve ser verdadeiro ou falso',
        },
      ],
    };
  }
}

export default ModalidadeLicitacaoFormStore;
