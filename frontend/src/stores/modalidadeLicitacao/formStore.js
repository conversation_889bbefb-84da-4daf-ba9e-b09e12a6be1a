import ModalidadeLicitacao from '~/domains/ModalidadeLicitacao';
import ModalidadeLicitacaoService from '../../services/ModalidadeLicitacaoService';
import FormBase from 'fc/stores/FormBase';
//import { override } from 'mobx';

class ModalidadeLicitacaoFormStore extends FormBase {
  constructor() {
    super(ModalidadeLicitacaoService, ModalidadeLicitacao);
  }

  rulesDefinition() {
    return {
      nome: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
      ],
      vigenciaDe: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      vigenciaAte: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      legislacao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };
  }
}

export default ModalidadeLicitacaoFormStore;

/*
  @override
  save(callback, type = 'edit') {
    // Garantir que legislacao seja um array válido antes de salvar
    if (this.object.legislacao && !Array.isArray(this.object.legislacao)) {
      this.object.legislacao = [];
    }

    // Garantir que permiteConsorcio seja um boolean
    if (this.object.permiteConsorcio === undefined || this.object.permiteConsorcio === null) {
      this.object.permiteConsorcio = false;
    }

    super.save(callback, type);
  }*/
