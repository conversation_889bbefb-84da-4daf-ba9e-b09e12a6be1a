import FormBase from 'fc/stores/FormBase';
import RequisicaoModificacao from '~/domains/RequisicaoModificacao';
import RequisicaoModificacaoService from '~/services/RequisicaoModificacaoService';
import { action, observable, runInAction } from 'mobx';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import AppStore from 'fc/stores/AppStore';

class RequisicaoRemocaoFormStore extends FormBase {
  @observable requisicoesRemocao;

  constructor() {
    super(RequisicaoModificacaoService, RequisicaoModificacao);
  }

  rulesDefinition() {
    return {
      justificativaJurisdicionado: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };
  }

  clearObject() {
    this.object.justificativaJurisdicionado = undefined;
  }

  getRequisicaoRemocaoDTO(idProcesso) {
    return {
      idProcesso,
      idEntidade: AppStore.getContextEntity()?.id,
      justificativaJurisdicionado: this.object.justificativaJurisdicionado,
    };
  }

  @action
  requisicaoRemocaoContrato(idContrato, callback) {
    if (!this.object.justificativaJurisdicionado) {
      showErrorNotification('Justificativa é de preenchimento obrigatório');
    } else {
      const requisicaoRemocaoDTO = this.getRequisicaoRemocaoDTO(idContrato);
      this.loading = true;
      this.service
        .requisicaoRemocaoContrato(requisicaoRemocaoDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Requisição salva com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  requisicaoRemocaoAditivoContrato(idAditivo, callback) {
    if (!this.object.justificativaJurisdicionado) {
      showErrorNotification('Justificativa é de preenchimento obrigatório');
    } else {
      const requisicaoRemocaoDTO = this.getRequisicaoRemocaoDTO(idAditivo);
      this.loading = true;
      this.service
        .requisicaoRemocaoAditivoContrato(requisicaoRemocaoDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Requisição salva com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  requisicaoRemocaoCarona(idCarona, callback) {
    if (!this.object.justificativaJurisdicionado) {
      showErrorNotification('Justificativa é de preenchimento obrigatório');
    } else {
      const requisicaoRemocaoDTO = this.getRequisicaoRemocaoDTO(idCarona);
      this.loading = true;
      this.service
        .requisicaoRemocaoCarona(requisicaoRemocaoDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Requisição salva com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  requisicaoRemocaoDispensa(idDispensa, callback) {
    if (!this.object.justificativaJurisdicionado) {
      showErrorNotification('Justificativa é de preenchimento obrigatório');
    } else {
      const requisicaoRemocaoDTO = this.getRequisicaoRemocaoDTO(idDispensa);
      this.loading = true;
      this.service
        .requisicaoRemocaoDispensa(requisicaoRemocaoDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Requisição salva com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  requisicaoRemocaoInexigibilidade(idInexigibilidade, callback) {
    if (!this.object.justificativaJurisdicionado) {
      showErrorNotification('Justificativa é de preenchimento obrigatório');
    } else {
      const requisicaoRemocaoDTO = this.getRequisicaoRemocaoDTO(idInexigibilidade);
      this.loading = true;
      this.service
        .requisicaoRemocaoInexigibilidade(requisicaoRemocaoDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Requisição salva com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  requisicaoRemocaoLicitacao(idLicitacao, callback) {
    if (!this.object.justificativaJurisdicionado) {
      showErrorNotification('Justificativa é de preenchimento obrigatório');
    } else {
      const requisicaoRemocaoDTO = this.getRequisicaoRemocaoDTO(idLicitacao);
      this.loading = true;
      this.service
        .requisicaoRemocaoLicitacao(requisicaoRemocaoDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Requisição salva com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  requisicaoRemocaoCredenciamento(idCredenciamento, callback) {
    if (!this.object.justificativaJurisdicionado) {
      showErrorNotification('Justificativa é de preenchimento obrigatório');
    } else {
      const requisicaoRemocaoDTO = this.getRequisicaoRemocaoDTO(idCredenciamento);
      this.loading = true;
      this.service
        .requisicaoRemocaoCredenciamento(requisicaoRemocaoDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Requisição salva com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  requisicaoRemocaoCredenciado(idCredenciado, callback) {
    if (!this.object.justificativaJurisdicionado) {
      showErrorNotification('Justificativa é de preenchimento obrigatório');
    } else {
      const requisicaoRemocaoDTO = this.getRequisicaoRemocaoDTO(idCredenciado);
      this.loading = true;
      this.service
        .requisicaoRemocaoCredenciado(requisicaoRemocaoDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Requisição salva com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }
}

export default RequisicaoRemocaoFormStore;
