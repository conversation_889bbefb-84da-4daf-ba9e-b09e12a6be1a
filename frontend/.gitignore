# See https://help.github.com/ignore-files/ for more about ignoring files.

.sass-cache
.vscode

# dependencies
/node_modules
package-lock.json

# testing
/coverage

# production
/build
/idea
/.idea

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

layout.css
pages.css

.env
homolog.env
prod.env
staging.env

./public/env-config.js
env-config.js
parsed-env