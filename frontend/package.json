{"name": "licon", "version": "1.0.0-SNAPSHOT", "homepage": ".", "private": false, "license": "MIT", "dependencies": {"@ckeditor/ckeditor5-alignment": "^34.1.0", "@ckeditor/ckeditor5-build-classic": "^34.1.0", "@ckeditor/ckeditor5-clipboard": "^34.1.0", "@ckeditor/ckeditor5-dev-utils": "^30.1.5", "@ckeditor/ckeditor5-font": "^34.1.0", "@ckeditor/ckeditor5-paste-from-office": "^34.1.0", "@ckeditor/ckeditor5-react": "^5.0.2", "@ckeditor/ckeditor5-source-editing": "^34.1.0", "@ckeditor/ckeditor5-table": "^34.1.0", "@ckeditor/ckeditor5-theme-lark": "^34.1.0", "@ckeditor/ckeditor5-upload": "^34.1.0", "@fullcalendar/core": "6.1.8", "@fullcalendar/daygrid": "6.1.8", "@fullcalendar/interaction": "6.1.8", "@fullcalendar/react": "6.1.8", "@fullcalendar/timegrid": "6.1.8", "@radix-ui/react-tooltip": "^1.0.5", "@turf/turf": "^6.5.0", "axios": "^0.19.0", "babel-plugin-istanbul": "^6.1.1", "chart.js": "^3.5.0", "classnames": "^2.2.6", "cpf-cnpj-validator": "^1.0.3", "crypto-js": "^4.1.1", "diff": "^5.1.0", "diff-match-patch": "^1.0.5", "echarts": "^5.5.0", "echarts-for-react": "^3.0.2", "email-validator": "^2.0.4", "eslint-plugin-unused-imports": "^2.0.0", "file-saver": "^2.0.5", "history": "^5.0.0", "ionicons": "^6.0.3", "jspdf": "^2.5.1", "jspdf-autotable": "^3.5.20", "leaflet": "^1.9.4", "leaflet-boundary-canvas": "^1.0.0", "leaflet-draw": "^1.0.4", "leaflet-draw-locales": "^1.2.3", "leaflet.awesome-markers": "^2.0.5", "leaflet.markercluster": "^1.5.3", "mobx": "^6.3.2", "mobx-react": "^7.2.0", "mobx-react-devtools": "^6.1.1", "mobx-react-router": "^4.1.0", "moment": "^2.29.1", "node-sass": "^6.0.1", "postcss": "8.4", "postcss-loader": "4.0.3", "primeflex": "^3.1.3", "primeicons": "6.0.1", "primereact": "8.7.2", "prismjs": "1.9.0", "prop-types": "^15.7.2", "raw-loader": "4", "react": "^17.0.1", "react-app-polyfill": "^2.0.0", "react-dev-utils": "^11.0.4", "react-dom": "^17.0.2", "react-highlight-words": "^0.17.0", "react-leaflet": "^4.2.1", "react-leaflet-canvas-markers": "1.0.1", "react-leaflet-draft": "^2.0.0", "react-leaflet-draw": "^0.20.4", "react-refresh": "^0.8.3", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.0", "react-transition-group": "^4.4.1", "react-virtualized": "^9.22.3", "xlsx": "^0.17.1", "react-leaflet-markercluster": "3.0.0-rc1", "@changey/react-leaflet-markercluster": "^4.0.0-rc1"}, "devDependencies": {"@babel/core": "7.13.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.14.5", "@babel/plugin-proposal-export-default-from": "^7.14.5", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-function-bind": "^7.14.5", "@babel/plugin-proposal-function-sent": "^7.14.5", "@babel/plugin-proposal-json-strings": "^7.18.6", "@babel/plugin-proposal-logical-assignment-operators": "^7.20.7", "@babel/plugin-proposal-numeric-separator": "^7.14.5", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-throw-expressions": "^7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-react-display-name": "^7.14.5", "@babel/plugin-transform-react-jsx-source": "^7.19.6", "@babel/preset-env": "^7.14.7", "@babel/preset-react": "^7.14.5", "@pmmmwh/react-refresh-webpack-plugin": "0.4.3", "@svgr/webpack": "5.5.0", "@typescript-eslint/eslint-plugin": "^4.5.0", "@typescript-eslint/parser": "^4.5.0", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.0", "babel-loader": "8.1.0", "babel-plugin-named-asset-import": "^0.3.7", "babel-plugin-root-import": "^6.6.0", "babel-preset-react-app": "^10.0.0", "bfj": "^7.0.2", "camelcase": "^6.1.0", "case-sensitive-paths-webpack-plugin": "2.3.0", "css-loader": "4.3.0", "customize-cra": "^1.0.0", "dotenv": "8.2.0", "dotenv-expand": "5.1.0", "eslint": "^7.11.0", "eslint-config-prettier": "^8.3.0", "eslint-config-react-app": "^6.0.0", "eslint-import-resolver-babel-plugin-root-import": "^1.1.1", "eslint-plugin-flowtype": "^5.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-testing-library": "^3.9.2", "eslint-webpack-plugin": "^2.5.2", "file-loader": "6.1.1", "fs-extra": "^9.0.1", "html-webpack-plugin": "4.5.0", "identity-obj-proxy": "3.0.0", "jest": "26.6.0", "jest-circus": "26.6.0", "jest-resolve": "26.6.0", "jest-watch-typeahead": "0.6.1", "mini-css-extract-plugin": "0.11.3", "optimize-css-assets-webpack-plugin": "5.0.4", "pnp-webpack-plugin": "1.6.4", "postcss-flexbugs-fixes": "4.2.1", "postcss-normalize": "8.0.1", "postcss-preset-env": "6.7.0", "postcss-safe-parser": "5.0.2", "prettier": "^2.3.2", "prompts": "2.4.0", "react-app-rewired": "^2.1.8", "react-error-overlay": "6.0.9", "resolve": "1.18.1", "resolve-url-loader": "^3.1.2", "sass-loader": "^10.0.5", "semver": "7.3.2", "style-loader": "1.3.0", "terser-webpack-plugin": "4.2.3", "ts-pnp": "1.2.0", "url-loader": "4.1.1", "webpack": "4.44.2", "webpack-dev-server": "3.11.1", "webpack-manifest-plugin": "2.2.0", "workbox-webpack-plugin": "5.1.4"}, "scripts": {"start": "node scripts/start.js --openssl-legacy-provider start", "env": "chmod +x ./env.sh && ./env.sh && cp env-config.js ./public/", "build": "node scripts/build.js", "test": "node scripts/test.js", "lint": "eslint ./src --max-warnings=0"}, "eslintConfig": {"extends": "react-app"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": [], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "testRunner": "/home/<USER>/workspace/tce/projeto-base/frontend/node_modules/jest-circus/runner.js", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/config/jest/babelTransform.js", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true}}