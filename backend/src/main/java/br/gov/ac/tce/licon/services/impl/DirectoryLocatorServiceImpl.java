package br.gov.ac.tce.licon.services.impl;

import java.nio.file.Path;
import java.nio.file.Paths;

import br.gov.ac.tce.licon.entities.enums.TipoEntidade;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import br.gov.ac.tce.licon.services.DirectoryLocatorService;

@Component
public class DirectoryLocatorServiceImpl implements DirectoryLocatorService {

    @Value("${app.files.repository-full-path}")
    private String repositoryFullPath;

    @Value("${app.files.temp-folder-path}")
    private String tempFolderPath;

    @Value("${app.files.definitive-folder-path}")
    private String definitiveFolderPath;

    @Value("${app.files.caronas-folder-path}")
    private String caronaFolderPath;

    @Value("${app.files.contrato-folder-path}")
    private String contratoFolderPath;

    @Value("${app.files.dispensas-folder-path}")
    private String dispensasFolderPath;

    @Value("${app.files.comissoes-folder-path}")
    private String comissoesFolderPath;

    @Value("${app.files.termo-referencia-folder-path}")
    private String termoReferenciaFolderPath;

    @Value("${app.files.licitacoes-folder-path}")
    private String licitacoesFolderPath;
    
    @Value("${app.files.aditivo-contrato-folder-path}")
    private String aditivoContratoFolderPath;

    @Value("${app.files.inexigibilidades-folder-path}")
    private String inexigibilidadesFolderPath;

    @Value("${app.files.tda-licitacao-folder-path}")
    private String tdaLicitacaoFolderPath;

    @Value("${app.files.tda-dispensa-folder-path}")
    private String tdaDispensaFolderPath;

    @Value("${app.files.tda-carona-folder-path}")
    private String tdaCaronaFolderPath;

    @Value("${app.files.tda-inexigibilidade-folder-path}")
    private String tdaInexigibilidadeFolderPath;

    @Value("${app.files.alerta-analise-folder-path}")
    private String alertaAnaliseFolderPath;

    @Value("alerta-mensagem")
    private String alertaMensagemFolderPath;

    @Value("requisicao-modificacao")
    private String requisicaoModificacaoFolderPath;
    
    @Value("edital")
    private String editalFolderPath;

    @Value("${app.files.obra-medicao-folder-path}")
    private String obraMedicaoFolderPath;

    @Value("${app.files.diario-obra-folder-path}")
    private String diarioObraFolderPath;

    @Value("${app.files.boletim-obra-folder-path}")
    private String boletimObraFolderPath;

    @Value("${app.files.empenho-contrato-folder-path}")
    private String empenhoContratoFolderPath;

    @Value("${app.files.geoobra-obra-folder-path}")
    private String geoobraObraFolderPath;

    @Value("tda_credenciamento")
    private String tdaCredenciamento;

    public Path getFullPathDestinoDefinitivo() {
        return Paths.get(repositoryFullPath, definitiveFolderPath);
    }

    public Path getFullPathDestinoTemporario() {
        return Paths.get(repositoryFullPath, tempFolderPath);
    }

    public Path getFullPathDestinoTemporario(String nomeDestino) {
        return getFullPathDestinoTemporario().resolve(nomeDestino);
    }

    public Path getFullPathEntidade(TipoEntidade entidade) {
        switch (entidade) {
            case ADITIVO:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(aditivoContratoFolderPath));
            case CARONA:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(caronaFolderPath));
            case CONTRATO:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(contratoFolderPath));
            case DISPENSA:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(dispensasFolderPath));
            case LICITACAO:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(licitacoesFolderPath));
            case COMISSAO:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(comissoesFolderPath));
            case TERMO_REFERENCIA:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(termoReferenciaFolderPath));
            case INEXIGIBILIDADE:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(inexigibilidadesFolderPath));
            case TDA_LICITACAO:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(tdaLicitacaoFolderPath));
            case TDA_DISPENSA:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(tdaDispensaFolderPath));
            case TDA_CARONA:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(tdaCaronaFolderPath));
            case TDA_INEXIGIBILIDADE:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(tdaInexigibilidadeFolderPath));
            case ALERTA_ANALISE:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(alertaAnaliseFolderPath));
            case ALERTA_MENSAGEM:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(alertaMensagemFolderPath));
            case REQUISICAO_MODIFICACAO:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(requisicaoModificacaoFolderPath));
            case EDITAL:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(editalFolderPath));
            case OBRA_MEDICAO:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(obraMedicaoFolderPath));
            case EMPENHO_CONTRATO:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(empenhoContratoFolderPath));
            case GEOOBRA_OBRA:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(geoobraObraFolderPath));
            case TDA_CREDENCIAMENTO:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(tdaCredenciamento));
            case DIARIO_OBRA:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(diarioObraFolderPath));
            case BOLETIM_OBRA:
                return getFullPathDestinoDefinitivo().resolve(Paths.get(boletimObraFolderPath));
            default:
                return getFullPathDestinoDefinitivo();
        }
    }

}
