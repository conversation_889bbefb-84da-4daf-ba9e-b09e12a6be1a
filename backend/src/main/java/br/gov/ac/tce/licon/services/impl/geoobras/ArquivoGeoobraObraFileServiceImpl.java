package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.entities.Arquivo;
import br.gov.ac.tce.licon.entities.enums.TipoEntidade;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.services.geoobras.ArquivoGeoobraObraFileService;
import br.gov.ac.tce.licon.services.geoobras.ArquivoGeoobraObraService;
import br.gov.ac.tce.licon.services.impl.AbstractFileServiceImpl;
import com.j256.simplemagic.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ArquivoGeoobraObraFileServiceImpl extends AbstractFileServiceImpl implements ArquivoGeoobraObraFileService {

    private static final ContentType[] TIPOS_VALIDOS = new ContentType[]{ContentType.PDF, ContentType.MICROSOFT_EXCEL, ContentType.MICROSOFT_EXCEL_XML, ContentType.PNG, ContentType.JPEG, ContentType.MICROSOFT_WORD, ContentType.MICROSOFT_WORD_XML, ContentType.KML, ContentType.XML};

    @Autowired
    private ArquivoGeoobraObraService arquivoGeoobraObraService;

    @Override
    protected Arquivo lookupArquivoParaDownload(Long idArquivo) throws AppException {
        return arquivoGeoobraObraService.getById(idArquivo);
    }

    @Override
    protected TipoEntidade getTipoEntidade() {
        return TipoEntidade.GEOOBRA_OBRA;
    }

    protected ContentType[] tiposValidos() {
        return TIPOS_VALIDOS;
    }
}
