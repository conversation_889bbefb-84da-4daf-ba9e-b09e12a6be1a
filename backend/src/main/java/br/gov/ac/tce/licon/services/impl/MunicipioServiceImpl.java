package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.requests.MunicipioFiltroRequest;
import br.gov.ac.tce.licon.entities.Municipio;
import br.gov.ac.tce.licon.repositories.MunicipioRepository;
import br.gov.ac.tce.licon.services.MunicipioService;
import br.gov.ac.tce.licon.services.specs.MunicipioSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class MunicipioServiceImpl extends AbstractService<Municipio, MunicipioFiltroRequest, MunicipioRepository> implements MunicipioService {

    @Autowired
    private MunicipioRepository repository;

    @Override
    public MunicipioRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<Municipio> getSpecification(MunicipioFiltroRequest filtro) {
        return new MunicipioSpecification(filtro);
    }
}
