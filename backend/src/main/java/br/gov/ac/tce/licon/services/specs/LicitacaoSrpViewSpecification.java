package br.gov.ac.tce.licon.services.specs;

import br.gov.ac.tce.licon.dtos.requests.LicitacaoSrpViewFiltroRequest;
import br.gov.ac.tce.licon.entities.LicitacaoSrpView;
import br.gov.ac.tce.licon.entities.LicitacaoSrpView_;
import lombok.AllArgsConstructor;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public class LicitacaoSrpViewSpecification implements ISpecification<LicitacaoSrpView> {

    private static final long serialVersionUID = 783351234654331L;

    private final LicitacaoSrpViewFiltroRequest filtro;

    @Override
    public Predicate toPredicate(Root<LicitacaoSrpView> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
        List<Predicate> predicados = new ArrayList<>();
        addIfExists(getPredicate(root.get(LicitacaoSrpView_.numero), filtro.getNumero(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(LicitacaoSrpView_.termoReferencia), filtro.getTermoReferencia(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(LicitacaoSrpView_.entidade), filtro.getEntidade(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(LicitacaoSrpView_.lei), filtro.getLei(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(LicitacaoSrpView_.ano), filtro.getAno(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(LicitacaoSrpView_.valorEstimado), filtro.getValorEstimado(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(LicitacaoSrpView_.dataCadastroPreparatoria), filtro.getDataCadastroPreparatoria(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(LicitacaoSrpView_.dataAbertura), filtro.getDataAbertura(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(LicitacaoSrpView_.valorAdjudicado), filtro.getValorAdjudicado(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(LicitacaoSrpView_.objeto), filtro.getObjeto(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(LicitacaoSrpView_.naturezasDoObjeto), filtro.getNaturezasDoObjeto(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(LicitacaoSrpView_.vencedores), filtro.getVencedores(), builder, filtro.getFilterType()), predicados);
        return builder.and(predicados.toArray(new Predicate[0]));
    }

}
