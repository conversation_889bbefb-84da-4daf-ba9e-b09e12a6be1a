package br.gov.ac.tce.licon.services.specs;

import br.gov.ac.tce.licon.dtos.requests.AlertaMensagemFiltroRequest;
import br.gov.ac.tce.licon.entities.AlertaMensagem;
import br.gov.ac.tce.licon.entities.AlertaMensagem_;
import lombok.AllArgsConstructor;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public class AlertaMensagemSpecification implements ISpecification<AlertaMensagem> {

	private static final long serialVersionUID = -6136676263926937229L;

	private final AlertaMensagemFiltroRequest filtro;

	@Override
	public Predicate toPredicate(Root<AlertaMensagem> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
		List<Predicate> predicados = new ArrayList<>();

		addIfExists(getPredicate(root.get(AlertaMensagem_.prazoResposta), filtro.getPrazoResposta(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaMensagem_.mensagemAuditor), filtro.getMensagemAuditor(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaMensagem_.respostaJurisdicionado), filtro.getRespostaJurisdicionado(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaMensagem_.dataMensagem), filtro.getDataMensagem(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaMensagem_.dataResposta), filtro.getDataResposta(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaMensagem_.usuarioMensagem), filtro.getUsuarioMensagem(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaMensagem_.usuarioResposta), filtro.getUsuarioResposta(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaMensagem_.alertaAnalise), filtro.getAlertaAnalise(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaMensagem_.mensagemInspetor), filtro.getMensagemInspetor(), builder, filtro.getFilterType()), predicados);
		return builder.and(predicados.toArray(new Predicate[0]));
	}

}
