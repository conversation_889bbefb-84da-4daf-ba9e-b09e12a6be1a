package br.gov.ac.tce.licon.services.specs;

import br.gov.ac.tce.licon.dtos.requests.LicitacaoFiltroRequest;
import br.gov.ac.tce.licon.entities.*;
import lombok.AllArgsConstructor;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public class LicitacaoSpecification implements ISpecification<Licitacao> {

	private static final long serialVersionUID = 7833506484372449851L;

	private final LicitacaoFiltroRequest filtro;

	@Override
	public Predicate toPredicate(Root<Licitacao> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
		List<Predicate> predicados = new ArrayList<>();

		addIfExists(getPredicate(root.get(Licitacao_.entidade), filtro.getEntidade(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.usuario), filtro.getUsuario(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.numero), filtro.getNumero(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.ano), filtro.getAno(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.orgao), filtro.getOrgao(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.objeto), filtro.getObjeto(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.tipo), filtro.getTipo(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.tiposLicitacao), filtro.getTiposLicitacao(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.forma), filtro.getForma(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.pregoeiro), filtro.getPregoeiro(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.numeroProcessoAdm), filtro.getNumeroProcessoAdm(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.comissao), filtro.getComissao(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.regenciaLegal), filtro.getRegenciaLegal(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.status), filtro.getStatus(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.justificativa), filtro.getJustificativa(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.srp), filtro.getSrp(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.valorEstimado), filtro.getValorEstimado(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.valorDeRisco), filtro.getValorDeRisco(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.tentativasMatch), filtro.getTentativasMatch(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.statusOcorrenciaAtual), filtro.getStatusOcorrenciaAtual(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.naturezaOcorrencia), filtro.getNaturezaOcorrencia(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.statusAnalise), filtro.getStatusAnalise(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.emAnalise), filtro.getEmAnalise(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.consultorIndividual), filtro.getConsultorIndividual(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.numeroDownloads), filtro.getNumeroDownloads(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.usuarioSeparouAnalise), filtro.getUsuarioSeparouAnalise(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.houveModificacao), filtro.getHouveModificacao(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.camposModificados), filtro.getCamposModificados(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.participacaoExterna), filtro.getParticipacaoExterna(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.parecerista), filtro.getParecerista(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.acatadoJustificado), filtro.getAcatadoJustificado(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.lei14333), filtro.getLei14333(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.fase), filtro.getFase(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.lei), filtro.getLei(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(Licitacao_.legislacaoOutros), filtro.getLegislacaoOutros(), builder, filtro.getFilterType()), predicados);

		return builder.and(predicados.toArray(new Predicate[0]));
	}
	
}
