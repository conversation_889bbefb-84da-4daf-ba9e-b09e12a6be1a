package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.mapper.geoobras.ArquivoGeoobraObraToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchParameter;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.SearchOperator;
import br.gov.ac.tce.licon.dtos.requests.geoobras.*;
import br.gov.ac.tce.licon.entities.ObrigatoriedadeArquivo;
import br.gov.ac.tce.licon.entities.enums.Objeto;
import br.gov.ac.tce.licon.entities.enums.geoobras.*;
import br.gov.ac.tce.licon.entities.geoobras.*;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.geoobras.GeoobraObraRepository;
import br.gov.ac.tce.licon.services.ObrigatoriedadeArquivoService;
import br.gov.ac.tce.licon.services.geoobras.*;
import br.gov.ac.tce.licon.services.impl.AbstractUploadTipoServiceImpl;
import br.gov.ac.tce.licon.services.specs.geoobras.GeoobraObraSpecification;
import br.gov.ac.tce.licon.utils.UtilsKML;
import com.bedatadriven.jackson.datatype.jts.JtsModule;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.j256.simplemagic.ContentType;
import com.vividsolutions.jts.geom.Geometry;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.errors.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
@EnableScheduling
public class GeoobraObraServiceImpl
        extends AbstractUploadTipoServiceImpl<GeoobraObra, GeoobraObraFiltroRequest, GeoobraObraRepository, ArquivoGeoobraObraFileService, ArquivoGeoobraObra, ArquivoGeoobraObraFiltroRequest, ArquivoGeoobraObraService, ArquivoGeoobraObraDTO, ArquivoGeoobraObraToDtoMapper, TipoArquivoGeoobraObra>
        implements GeoobraObraService {

    @Autowired
    private GeoobraObraRepository repository;

    @Autowired
    private ArquivoGeoobraObraFileService arquivoGeoobraObraFileService;

    @Autowired
    private ArquivoGeoobraObraService arquivoGeoobraObraService;

    @Autowired
    private ArquivoGeoobraObraToDtoMapper arquivoGeoobraObraToDtoMapper;

    @Inject
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Inject
    private MedicaoService medicaoService;

    @Inject
    private MapaObraViewService mapaObraViewService;

    @Inject
    private MinioClient minioClientGeoobras;

    @Value("${spring.geoobras.minio.bucket}")
    private String bucketGeoobras;

    @Value("${spring.geoobras.acompanhamento.path}")
    private String pathAcompanhamento;

    private static final Logger LOGGER = LoggerFactory.getLogger(GeoobraObraServiceImpl.class);

    private Boolean checkXlsMimeTypes(String mimeType) {
        List<ContentType> types = Arrays.asList(ContentType.MICROSOFT_EXCEL,
                ContentType.MICROSOFT_EXCEL_XML,
                ContentType.ZIP,
                ContentType.PDF);
        return types.stream().anyMatch(type -> type.getMimeType().equals(mimeType));
    }

    @Override
    public GeoobraObraRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<GeoobraObra> getSpecification(GeoobraObraFiltroRequest filtro) {
        return new GeoobraObraSpecification(filtro);
    }

    @Override
    public ArquivoGeoobraObraService getArquivoService() {
        return arquivoGeoobraObraService;
    }

    @Override
    public ArquivoGeoobraObraFileService getFileService() {
        return arquivoGeoobraObraFileService;
    }

    @Override
    public ArquivoGeoobraObraToDtoMapper getMapper() {
        return arquivoGeoobraObraToDtoMapper;
    }

    @Override
    protected ArquivoGeoobraObra getNewArquivo() {
        return new ArquivoGeoobraObra();
    }

    @Override
    protected void beforeSave(GeoobraObra entity) {
        if (entity.getId() == null) {
            entity.setFase(FaseGeoobraObra.CADASTRAL);
            entity.setStatus(GeoobraStatusObra.NAO_INICIADA);
            entity.setDataCadastro(LocalDate.now());
        }
    }

    public void saveObra(GeoobraObraDTO dto) throws AppException {
        GeoobraObra entity = dto.getObra();
        this.beforeSave(entity);
        List<ArquivoGeoobraObraDTO> arquivos = dto.getArquivos();
        validarArquivos(arquivos);
        GeoobraObra obra = save(entity);
        saveArquivos(arquivos, obra);
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() {
        AdvancedSearchRequest filtro = new AdvancedSearchRequest();
        this.inicializaFiltro(filtro);
        AdvancedSearchParameter arquivoObjetoParam = new AdvancedSearchParameter("objeto", SearchOperator.EQUAL_TO, Objeto.GEOOBRA_OBRA.name());
        AdvancedSearchParameter arquivoObrigatorioParam = new AdvancedSearchParameter("obrigatorio", SearchOperator.EQUAL_TO, true);
        filtro.getAndParameters().add(arquivoObjetoParam);
        filtro.getAndParameters().add(arquivoObrigatorioParam);

        return obrigatoriedadeArquivoService.buscarAdvanced(filtro).getItems();
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoGeoobraObra arquivoEntity, GeoobraObra entity, ArquivoGeoobraObraDTO arquivoUpload) {
        arquivoEntity.setObra(entity);
        if (TipoArquivoGeoobraObra.GEORREFERENCIAMENTO.equals(arquivoUpload.getTipo())) {
            ArquivoBinarioDTO arquivoBinarioDTO = this.download(arquivoUpload.getArquivo());
            InputStream inputStream = new ByteArrayInputStream(arquivoBinarioDTO.getBinario());
            Geometry geom = UtilsKML.extractGeomFromKML(inputStream);
            arquivoEntity.setGeom(geom);
            arquivoEntity.setCentroid(geom.getCentroid());
        }
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    @Override
    public void validarArquivos(ArquivosGeoobraObraDTO obraDTO) {
        List<ArquivoGeoobraObraDTO> arquivos = obraDTO.getArquivos();
        super.validarArquivos(arquivos);
        for (ArquivoGeoobraObraDTO arquivoDTO : arquivos) {
            if (TipoArquivoGeoobraObra.PLANILHA_CONTRATADA.equals(arquivoDTO.getTipo())) {
                if (!checkXlsMimeTypes(arquivoDTO.getArquivo().getTipoArquivo())) {
                    throw new AppException("O arquivo do tipo 'Planilha Contratada' deve ser uma planilha ou um PDF", HttpStatus.BAD_REQUEST);
                }
            } else if (TipoArquivoGeoobraObra.GEORREFERENCIAMENTO.equals(arquivoDTO.getTipo())) {
                getFileService().validaArquivoKML(arquivoDTO.getArquivo(), obraDTO.getEntidade(), null);
            }
        }
    }

    @Override
    public void iniciarObra(GeoobraObraDTO dto) {
        GeoobraObra entity = dto.getObra();
        entity.setFase(FaseGeoobraObra.INICIAL);
        entity.setStatus(GeoobraStatusObra.EM_ANDAMENTO);
        List<ArquivoGeoobraObraDTO> arquivos = dto.getArquivos();
        GeoobraObra obra = save(entity);
        validarArquivos(arquivos);
        List<ArquivoGeoobraObra> arquivosSalvos  = saveArquivos(arquivos, obra);
        this.atualizaTipoGeometria(arquivosSalvos, obra);
    }

    private void atualizaTipoGeometria(List<ArquivoGeoobraObra> arquivosSalvos, GeoobraObra obra) {
        ArquivoGeoobraObra arquivoFaseInicial = arquivosSalvos.stream().filter(f ->
                FaseGeoobraObra.INICIAL.equals(f.getFase()) && TipoArquivoGeoobraObra.GEORREFERENCIAMENTO.equals(f.getTipo())
        ).findFirst().orElse(null);

        if (arquivoFaseInicial != null) {
            String tipoGeometrisStr = arquivoFaseInicial.getGeom().getGeometryType();
            TipoGeometriaObra tipoGeometriaObra = TipoGeometriaObra.valueOf(tipoGeometrisStr.toUpperCase());
            obra.setTipoGeometriaObra(tipoGeometriaObra);
            save(obra);
        }
    }

    @Override
    public void finalizarObra(GeoobraObraDTO dto) {
        GeoobraObra entity = dto.getObra();
        entity.setFase(FaseGeoobraObra.FINALIZACAO);
        List<ArquivoGeoobraObraDTO> arquivos = dto.getArquivos();
        GeoobraObra obra = save(entity);
        validarArquivosFinalizacao(arquivos, obra);
        saveArquivos(arquivos, obra);
    }

    private void validarArquivosFinalizacao(List<ArquivoGeoobraObraDTO> arquivos, GeoobraObra obra) {
        this.validarArquivos(arquivos);
        for (ArquivoGeoobraObraDTO arquivoDTO : arquivos) {
            if (FaseGeoobraObra.FINALIZACAO.equals(arquivoDTO.getFase()) && TipoArquivoGeoobraObra.GEORREFERENCIAMENTO.equals(arquivoDTO.getTipo())) {
                ArquivoBinarioDTO arquivoBinarioDTO = this.download(arquivoDTO.getArquivo());
                InputStream inputStream = new ByteArrayInputStream(arquivoBinarioDTO.getBinario());
                Geometry geom = UtilsKML.extractGeomFromKML(inputStream);
                String tipoGeometrisStr = geom.getGeometryType();
                TipoGeometriaObra tipoGeometriaObra = TipoGeometriaObra.valueOf(tipoGeometrisStr.toUpperCase());
                if (!obra.getTipoGeometriaObra().equals(tipoGeometriaObra)) {
                    throw new AppException(String.format("O tipo de geometria do arquivo de georreferenciamento adicionado é (%s) e não corresponde ao tipo de geometria da obra (%s).", tipoGeometriaObra.getValor(), obra.getTipoGeometriaObra().getValor()), HttpStatus.BAD_REQUEST);
                }
            }
        }
    }

    @Override
    public void entregarObra(GeoobraObraDTO dto) {
        GeoobraObra entity = dto.getObra();
        entity.setFase(FaseGeoobraObra.ENTREGA);
        entity.setStatus(GeoobraStatusObra.FINALIZADA);
        List<ArquivoGeoobraObraDTO> arquivos = dto.getArquivos();
        validarArquivos(arquivos);
        GeoobraObra obra = save(entity);
        saveArquivos(arquivos, obra);
    }

    @Override
    public void interromperObra(GeoobraObraDTO dto) {
        GeoobraObra entity = dto.getObra();
        entity.setFase(FaseGeoobraObra.INTERRUPCAO);
        entity.setStatus(GeoobraStatusObra.INTERROMPIDA);
        List<ArquivoGeoobraObraDTO> arquivos = dto.getArquivos();
        validarArquivos(arquivos);
        GeoobraObra obra = save(entity);
        saveArquivos(arquivos, obra);
    }

    @Override
    public void continuarObra(GeoobraObraDTO dto) {
        GeoobraStatusObra newStatus = GeoobraStatusObra.NAO_INICIADA;
        GeoobraObra obra = getById(dto.getId());
        if (obra != null) {
            FaseGeoobraObra faseGeoobraObra = obra.getFase();
            if (FaseGeoobraObra.INICIAL.equals(faseGeoobraObra) || FaseGeoobraObra.MEDICAO.equals(faseGeoobraObra) || FaseGeoobraObra.FINALIZACAO.equals(faseGeoobraObra)) {
                newStatus = GeoobraStatusObra.EM_ANDAMENTO;
            } else if (FaseGeoobraObra.ENTREGA.equals(faseGeoobraObra)) {
                newStatus = GeoobraStatusObra.FINALIZADA;
            }
            obra.setStatus(newStatus);
        }
        save(obra);
    }

    @Override
    public void paralisarObra(GeoobraObraDTO dto) {
        GeoobraObra obra = getById(dto.getId());
        obra.setStatus(GeoobraStatusObra.PARALISADA);
        save(obra);
    }

    @Scheduled(cron = "0 0 1 * * *")
    protected void populaGeoobrasPublico() {
        try {

            List files = new ArrayList();
            List<Map> obras = makeObras(files);

            ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
            String json = ow.writeValueAsString(obras);

            InputStream inputStream = new ByteArrayInputStream(json.getBytes());

            putObject(Paths.get(pathAcompanhamento, "acompanhamento.json").toString(), inputStream);

            files.stream().forEach(arquivo -> {

                ArquivoBinarioDTO arquivoBinarioDTO = null;
                String diretorio = "";
                if (arquivo instanceof ArquivoGeoobraObra) {
                    ArquivoGeoobraObraDTO arquivoGeoobraObraDTO = this.getMapper().map((ArquivoGeoobraObra) arquivo);
                    arquivoBinarioDTO = this.getFileService().download(arquivoGeoobraObraDTO.getArquivo());
                    diretorio = ((ArquivoGeoobraObra) arquivo).getDiretorio();
                } else if (arquivo instanceof ArquivoMedicao) {
                    ArquivoMedicaoDTO arquivoMedicaoDTO = this.medicaoService.getMapper().map((ArquivoMedicao) arquivo);
                    arquivoBinarioDTO = this.medicaoService.getFileService().download(arquivoMedicaoDTO.getArquivo());
                    diretorio = ((ArquivoMedicao) arquivo).getDiretorio();
                }

                ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(arquivoBinarioDTO.getBinario());
                putObject(getPathFile(diretorio), byteArrayInputStream);

            });
            LOGGER.info("Dados do geoobras público atualizado com sucesso.");
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private void putObject(String objectName, InputStream inputStream) {
        try {
            minioClientGeoobras.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketGeoobras)
                            .object(objectName)
                            .stream(inputStream, inputStream.available(), -1)
                            .build());
        } catch (IOException | InvalidKeyException | InvalidResponseException | XmlParserException |
                 InsufficientDataException | ErrorResponseException | NoSuchAlgorithmException | InternalException |
                 ServerException e) {
            String msg = "Houve algum erro ao tentar armazenar dados no geoobras público.";
            LOGGER.error(msg, e);
        }
    }

    private String getPathFile(String diretorio) {
        Path path = Paths.get(diretorio);
        String filename = path.getFileName().toString();
        return Paths.get(pathAcompanhamento, "files", filename).toString();
    }

    private List<Map> makeObras(List files) {
        List<GeoobraObra> geoobraObras = repository.findObrasCompletas();
        return geoobraObras.stream().map(obra -> {
            Map<String, Object> obraMap = new HashMap<>();
            obraMap.put("id", obra.getId());
            obraMap.put("tipo", obra.getTipo() != null ? obra.getTipo().getNome() : null);
            obraMap.put("tipoDimensao", obra.getTipo() != null ? obra.getTipo().getTipoDimensao() : null);
            obraMap.put("contrato", obra.getContrato() != null ? obra.getContrato().getNumero() : null);
            obraMap.put("anoContrato", obra.getContrato() != null ? obra.getContrato().getAnoContrato() : null);
            obraMap.put("entidade", obra.getEntidade() != null ? obra.getEntidade().getNome() : null);
            obraMap.put("ente",  obra.getEntidade() != null ? obra.getEntidade().getEnte().getNome() : null);
            obraMap.put("status", obra.getStatus().name());
            obraMap.put("fase", obra.getFase().name());
            obraMap.put("numero", obra.getNumero());
            obraMap.put("endereco", obra.getEndereco());
            obraMap.put("cep", obra.getCep());
            obraMap.put("dimensoes", obra.getDimensoes());
            obraMap.put("valor", obra.getValor());
            obraMap.put("descricao", obra.getDescricao());
            obraMap.put("tipoObjeto", obra.getTipoObjeto().name());
            obraMap.put("subtipoObjeto", obra.getSubtipoObjeto() != null ? obra.getSubtipoObjeto().name() : null);
            obraMap.put("dataPrevistaInicio", formatDate(obra.getDataPrevistaInicio()));
            obraMap.put("dataPrevistaConclusao", formatDate(obra.getDataPrevistaConclusao()));
            obraMap.put("dataCadastro", formatDate(obra.getDataCadastro()));
            obraMap.put("dataInicio", obra.getDataInicio() != null ? formatDate(obra.getDataInicio()) : null);
            obraMap.put("dataFim", obra.getDataFim() != null ? formatDate(obra.getDataFim()) : null);
            obraMap.put("dataConclusao", obra.getDataConclusao() != null ? formatDate(obra.getDataConclusao()) : null);
            obraMap.put("dataRecebimento", obra.getDataRecebimento() != null ? formatDate(obra.getDataRecebimento()) : null);
            obraMap.put("tipoEncerramento", obra.getTipoEncerramento() != null ? obra.getTipoEncerramento() : null);
            obraMap.put("municipio", obra.getMunicipio().getNomeMun());

            obraMap.put("medicoes", makeMedicoes(files, obra.getId()));

            List<Map> acompanhamentos = makeAcompanhamentos(obra.getId());
            if (acompanhamentos != null && !acompanhamentos.isEmpty()) {
                Map primeiroAcompanhamento = acompanhamentos.get(0);
                obraMap.put("centroid", primeiroAcompanhamento.get("centroid"));
                obraMap.put("startPoint", primeiroAcompanhamento.get("startPoint"));
            }
            obraMap.put("acompanhamentos", acompanhamentos);

            List<ArquivoGeoobraObra> arquivos = this.getArquivoService().buscarPor(obra.getId()).stream().filter(arquivo -> TipoArquivoGeoobraObra.FOTO.equals(arquivo.getTipo())).collect(Collectors.toList());
            files.addAll(arquivos);

            obraMap.put("arquivosInicio", arquivos.stream().filter(arquivo -> FaseGeoobraObra.INICIAL.equals(arquivo.getFase())).map(arquivo -> getPathFile(arquivo.getDiretorio())).collect(Collectors.toList()));
            obraMap.put("arquivosFim", arquivos.stream().filter(arquivo -> FaseGeoobraObra.FINALIZACAO.equals(arquivo.getFase())).map(arquivo -> getPathFile(arquivo.getDiretorio())).collect(Collectors.toList()));

            return obraMap;
        }).collect(Collectors.toList());
    }

    private List<Map> makeAcompanhamentos(Long idObra) {
        List<MapaObraView> acompanhamentos = mapaObraViewService.getByObra(idObra);
        return acompanhamentos.stream().map(acompanhamento -> {
            Map<String, Object> medicaoMap = new HashMap<>();
            medicaoMap.put("fase", acompanhamento.getFase());
            medicaoMap.put("obraId", idObra);
            medicaoMap.put("geom", toJson(acompanhamento.getGeom()));
            medicaoMap.put("startPoint", toJson(acompanhamento.getStartPoint()));
            medicaoMap.put("centroid", toJson(acompanhamento.getCentroid()));
            return medicaoMap;
        }).collect(Collectors.toList());
    }

    private List<Map> makeMedicoes(List files, Long idObra) {
        List<Medicao> medicoes = medicaoService.getByObra(idObra);
        return medicoes.stream().map(medicao -> {
            Map<String, Object> medicaoMap = new HashMap<>();
            medicaoMap.put("id", medicao.getId());
            medicaoMap.put("numeroEmpenho", medicao.getEmpenho().getNumeroEmpenho().toString().replace(".", ""));
            medicaoMap.put("valor", medicao.getValor());
            medicaoMap.put("percentualConclusao", medicao.getPercentualConclusao());
            medicaoMap.put("dataInicio", formatDate(medicao.getDataInicio()));
            medicaoMap.put("dataFim", formatDate(medicao.getDataFim()));
            medicaoMap.put("dataCadastro", formatDate(medicao.getDataCadastro()));

            List<ArquivoMedicao> arquivos = this.medicaoService.getArquivoService().buscarPor(medicao.getId()).stream().filter(arquivo -> TipoArquivoMedicao.FOTO.equals(arquivo.getTipo())).collect(Collectors.toList());
            medicaoMap.put("arquivos", arquivos.stream().map(arquivo -> getPathFile(arquivo.getDiretorio())).collect(Collectors.toList()));
            files.addAll(arquivos);
            return medicaoMap;
        }).collect(Collectors.toList());
    }

    private Map toJson(Geometry geometry) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JtsModule());
        ObjectWriter writer = mapper.writer();

        Map result = null;
        try {
            String json = writer.writeValueAsString(geometry);
            result = mapper.readValue(json, Map.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Erro ao converter geometria {} para json.", geometry);
        }
        return result;
    }

    private String formatDate(LocalDate date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return date != null ? date.format(formatter) : "";
    }
}
