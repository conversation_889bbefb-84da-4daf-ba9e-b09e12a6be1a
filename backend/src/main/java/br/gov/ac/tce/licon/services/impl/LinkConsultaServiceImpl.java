package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.requests.LinkConsultaFiltroRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.responses.BuscaResponse;
import br.gov.ac.tce.licon.entities.Ente;
import br.gov.ac.tce.licon.entities.Entidade;
import br.gov.ac.tce.licon.entities.LinkConsulta;
import br.gov.ac.tce.licon.entities.VariavelControle;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.EnteRepository;
import br.gov.ac.tce.licon.repositories.EntidadeRepository;
import br.gov.ac.tce.licon.repositories.LinkConsultaRepository;
import br.gov.ac.tce.licon.repositories.VariavelControleRepository;
import br.gov.ac.tce.licon.services.LinkConsultaService;
import br.gov.ac.tce.licon.services.specs.LinkConsultaSpecification;
import br.gov.ac.tce.licon.utils.CriptografiaHash;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.List;

@Service
@Transactional
public class LinkConsultaServiceImpl extends AbstractService<LinkConsulta, LinkConsultaFiltroRequest, LinkConsultaRepository> implements LinkConsultaService {

    @Autowired
    private LinkConsultaRepository repository;

    @Autowired
    private VariavelControleRepository variavelControleRepository;

    @Autowired
    private EntidadeRepository entidadeRepository;

    @Autowired
    private EnteRepository enteRepository;

    @Override
    public LinkConsultaRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<LinkConsulta> getSpecification(LinkConsultaFiltroRequest filtro) {
        return new LinkConsultaSpecification(filtro);
    }

    @Override
    public BuscaResponse<LinkConsulta> buscarAdvanced(AdvancedSearchRequest filtro) {
        BuscaResponse<LinkConsulta> resultadoBusca = super.buscarAdvanced(filtro);
        postBuscarAdvanced(resultadoBusca.getItems());
        return resultadoBusca;
    }

    private void postBuscarAdvanced(List<LinkConsulta> linkConsultaList) {
        List<VariavelControle> variavelControleList = this.variavelControleRepository.findAll();
        for (LinkConsulta linkConsulta : linkConsultaList) {
            this.buildParametroLink(linkConsulta, variavelControleList);
        }
    }

    private void buildParametroLink(LinkConsulta linkConsulta, List<VariavelControle> variavelControleList) {
        StringBuilder parametroLink = new StringBuilder();
        for (VariavelControle variavelControle : variavelControleList) {
            parametroLink.append(variavelControle.getValor());
        }
        parametroLink.append("?link=" + CriptografiaHash.encode(linkConsulta.getId().toString()));
        linkConsulta.setParametroLink(parametroLink.toString());
    }

    @Override
    protected void resolverRelacionamentos(LinkConsulta entity) throws AppException {

        if (entity.getEntidade() != null) {
            Optional<Entidade> optional = entidadeRepository.findById(entity.getEntidade().getId());
            if (optional.isPresent()) {
                Entidade entidade = optional.get();
                Optional<Ente> opEnte = enteRepository.findById(entidade.getEnte().getId());
                if (opEnte.isPresent()) {
                    entidade.setEnte(opEnte.get());
                    entity.setEntidade(entidade);
                }
            }
        }

        super.resolverRelacionamentos(entity);
    }
}
