package br.gov.ac.tce.licon.services.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import br.gov.ac.tce.licon.dtos.requests.TipoLicitacaoFiltroRequest;
import br.gov.ac.tce.licon.entities.TipoLicitacao;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.TipoLicitacaoRepository;
import br.gov.ac.tce.licon.services.TipoLicitacaoService;
import br.gov.ac.tce.licon.services.specs.TipoLicitacaoSpecification;

@Service
@Transactional
public class TipoLicitacaoServiceImpl extends AbstractService<TipoLicitacao, TipoLicitacaoFiltroRequest, TipoLicitacaoRepository> implements TipoLicitacaoService {

	@Autowired
	private TipoLicitacaoRepository repository;

	@Override
	public TipoLicitacaoRepository getRepository() {
		return repository;
	}

	@Override
	protected Example<TipoLicitacao> obterExemploChecarSeJahExiste(TipoLicitacao entity) throws AppException {
		TipoLicitacao exemplo = TipoLicitacao.builder().nome(entity.getNome()).build();
		return Example.of(exemplo);
	}

	@Override
	protected void lancarErroEntidadeJahExistente(TipoLicitacao entity) throws AppException {
		throw new AppException(String.format("O tipo de Licitacão com dado nome já existe: %s", entity.getNome()),
				HttpStatus.UNPROCESSABLE_ENTITY);
	}

	@Override
	protected Specification<TipoLicitacao> getSpecification(TipoLicitacaoFiltroRequest filtro) {
		return new TipoLicitacaoSpecification(filtro);
	}

}
