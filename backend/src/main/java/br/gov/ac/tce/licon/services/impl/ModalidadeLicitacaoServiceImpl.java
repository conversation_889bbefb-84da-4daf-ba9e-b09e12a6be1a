package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.requests.ModalidadeLicitacaoFiltroRequest;
import br.gov.ac.tce.licon.entities.ModalidadeLicitacao;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.ModalidadeLicitacaoRepository;
import br.gov.ac.tce.licon.services.ModalidadeLicitacaoService;
import br.gov.ac.tce.licon.services.specs.ModalidadeLicitacaoSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class ModalidadeLicitacaoServiceImpl extends AbstractService<ModalidadeLicitacao, ModalidadeLicitacaoFiltroRequest, ModalidadeLicitacaoRepository> implements ModalidadeLicitacaoService {

    @Autowired
    private ModalidadeLicitacaoRepository repository;

    @Override
    public ModalidadeLicitacaoRepository getRepository() {
        return repository;
    }

    @Override
    protected Example<ModalidadeLicitacao> obterExemploChecarSeJahExiste(ModalidadeLicitacao entity) throws AppException {
        ModalidadeLicitacao exemplo = ModalidadeLicitacao.builder()
                .nome(entity.getNome())
                .build();
        return Example.of(exemplo);
    }

    @Override
    protected void lancarErroEntidadeJahExistente(ModalidadeLicitacao entity) throws AppException {
        throw new AppException(String.format("Modalidade de Licitação com dado nome já existe: %s", entity.getNome()),
                HttpStatus.UNPROCESSABLE_ENTITY);
    }

    @Override
    protected Specification<ModalidadeLicitacao> getSpecification(ModalidadeLicitacaoFiltroRequest filtro) {
        return new ModalidadeLicitacaoSpecification(filtro);
    }

    @Override
    public ModalidadeLicitacao save(ModalidadeLicitacao entity) throws AppException {
        try {
            if (entity.getVigenciaDe() != null && entity.getVigenciaAte() != null &&
                entity.getVigenciaDe().isAfter(entity.getVigenciaAte())) {
                throw new AppException("A data de vigência inicial não pode ser posterior à data de vigência final", HttpStatus.BAD_REQUEST);
            }

            return super.save(entity);

        } catch (AppException e) {
            throw e;
        } catch (Exception e) {
            throw new AppException("Erro inesperado ao salvar modalidade de licitação: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
