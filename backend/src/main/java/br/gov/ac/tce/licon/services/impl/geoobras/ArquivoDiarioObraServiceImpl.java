package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivoDiarioObraFiltroRequest;
import br.gov.ac.tce.licon.entities.geoobras.ArquivoDiarioObra;
import br.gov.ac.tce.licon.repositories.geoobras.ArquivoDiarioObraRepository;
import br.gov.ac.tce.licon.services.geoobras.ArquivoDiarioObraService;
import br.gov.ac.tce.licon.services.impl.AbstractService;
import br.gov.ac.tce.licon.services.specs.geoobras.ArquivoDiarioObraSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Transactional
public class ArquivoDiarioObraServiceImpl
        extends AbstractService<ArquivoDiarioObra, ArquivoDiarioObraFiltroRequest, ArquivoDiarioObraRepository>
        implements ArquivoDiarioObraService {

    @Autowired
    private ArquivoDiarioObraRepository repository;

    @Override
    public ArquivoDiarioObraRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<ArquivoDiarioObra> getSpecification(ArquivoDiarioObraFiltroRequest filtro) {
        return new ArquivoDiarioObraSpecification(filtro);
    }

    @Override
    protected void beforeSave(ArquivoDiarioObra entity) {
        if (entity.getId() == null) {
            entity.setDataEnvio(LocalDateTime.now());
        }
    }

    @Override
    public List<ArquivoDiarioObra> buscarPor(Long idDiario) {
        return repository.buscarPor(idDiario);
    }
}
