package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.entities.Arquivo;
import br.gov.ac.tce.licon.entities.enums.TipoEntidade;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.services.geoobras.ArquivoMedicaoFileService;
import br.gov.ac.tce.licon.services.geoobras.ArquivoMedicaoService;
import br.gov.ac.tce.licon.services.impl.AbstractFileServiceImpl;
import com.j256.simplemagic.ContentType;
import org.springframework.stereotype.Service;

import javax.inject.Inject;

@Service
public class ArquivoMedicaoFileServiceImpl extends AbstractFileServiceImpl implements ArquivoMedicaoFileService {

    private static final ContentType[] TIPOS_VALIDOS = new ContentType[]{ContentType.PDF, ContentType.MICROSOFT_EXCEL, ContentType.MICROSOFT_EXCEL_XML, ContentType.PNG, ContentType.JPEG, ContentType.MICROSOFT_WORD, ContentType.MICROSOFT_WORD_XML, ContentType.KML, ContentType.XML};

    @Inject
    private ArquivoMedicaoService arquivoMedicaoService;

    @Override
    protected Arquivo lookupArquivoParaDownload(Long idArquivo) throws AppException {
        // Realizar validação do usuário fazendo a requisição poder realizar o download deste arquivo específico!
        return arquivoMedicaoService.getById(idArquivo);
    }

    @Override
    protected TipoEntidade getTipoEntidade() {
        return TipoEntidade.OBRA_MEDICAO;
    }

    protected ContentType[] tiposValidos() {
        return TIPOS_VALIDOS;
    }

}
