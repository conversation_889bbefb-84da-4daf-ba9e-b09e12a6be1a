package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.ArquivoDTO;
import br.gov.ac.tce.licon.dtos.EmailDTO;
import br.gov.ac.tce.licon.dtos.mapper.ArquivoCaronaToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.*;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchParameter;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.SearchOperator;
import br.gov.ac.tce.licon.dtos.responses.BuscaResponse;
import br.gov.ac.tce.licon.dtos.responses.cjurApi.PessoaResponsavelEntidadeResponseDTO;
import br.gov.ac.tce.licon.entities.*;
import br.gov.ac.tce.licon.entities.enums.Objeto;
import br.gov.ac.tce.licon.entities.enums.StatusAuditoriaLicitacao;
import br.gov.ac.tce.licon.entities.enums.StatusLicitacao;
import br.gov.ac.tce.licon.entities.enums.TipoArquivoCarona;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.*;
import br.gov.ac.tce.licon.services.*;
import br.gov.ac.tce.licon.services.cjurApi.CjurApiService;
import br.gov.ac.tce.licon.services.specs.CaronaSpecification;
import br.gov.ac.tce.licon.utils.GeradorMensagemEmail;
import com.j256.simplemagic.ContentType;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Transactional
public class CaronaServiceImpl extends AbstractUploadTipoServiceImpl<Carona, CaronaFiltroRequest, CaronaRepository, ArquivoCaronaFileService, ArquivoCarona, ArquivoCaronaFiltroRequest, ArquivoCaronaService, ArquivoCaronaDTO, ArquivoCaronaToDtoMapper, TipoArquivoCarona> implements CaronaService {

    @Inject
    private ArquivoCaronaFileService arquivoCaronaFileService;

    @Inject
    private ArquivoCaronaService arquivoCaronaService;

    @Inject
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Autowired
    private CaronaRepository repository;

    @Autowired
    private TdaCaronaRepository tdaRepository;

    @Autowired
    private CaronaLicitanteRepository caronaLicitanterepository;

    @Inject
    private ArquivoCaronaToDtoMapper arquivoCaronaToDtoMapper;

    @Autowired
    private IEmailService emailService;

    @Autowired
    private TermoReferenciaService termoReferenciaService;

    @Autowired
    private TermoReferenciaRepository termoReferenciaRepository;

    @Autowired
    private LicitacaoRepository licitacaoRepository;

    @Autowired
    private AnaliseProcessoViewServiceImpl analiseProcessoViewService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private CjurApiService cjurApiService;

    @Autowired
    private AnulacaoRevogacaoService anulacaoRevogacaoService;

    @Override
    public CaronaRepository getRepository() {
        return repository;
    }

    @Override
    protected void lancarErroEntidadeJahExistente(Carona entity) throws AppException {
        throw new AppException(String.format("Carona com número de processo %s já existe para a entidade %s.", entity.getNumeroProcessoGerenciadorAta(), entity.getEntidade().getNome()), HttpStatus.UNPROCESSABLE_ENTITY);
    }

    @Override
    protected Example<Carona> obterExemploChecarSeJahExiste(Carona entity) throws AppException {
        Carona exemplo = Carona.builder().numeroProcessoGerenciadorAta(entity.getNumeroProcessoGerenciadorAta()).entidade(entity.getEntidade()).build();
        return Example.of(exemplo);
    }

    @Override
    protected Specification<Carona> getSpecification(CaronaFiltroRequest filtro) {
        return new CaronaSpecification(filtro);
    }

    @Override
    protected void beforeSave(Carona entity) {
        if (entity.getId() == null) {
            entity.setDataCadastro(LocalDateTime.now());
            entity.setUsuario(getCurrentUser());
            entity.setStatus(StatusLicitacao.NAO_PUBLICADA);
            entity.setOrigemLicon2(true);
        }
    }

    @Override
    protected void validateCollectionsRemoval(Carona object, Carona persistedObject) {
        validateDetentoresRemoval(object, persistedObject);
    }

    private void validateDetentoresRemoval(Carona object, Carona persistedObject) {
        if (!persistedObject.getDetentores().equals(object.getDetentores())) {
            List<Long> newIdList = object.getDetentores().stream().map(AbstractIdentificavel::getId).collect(Collectors.toList());
            for (Long persistedId : persistedObject.getDetentores().stream().map(AbstractIdentificavel::getId).collect(Collectors.toList())) {
                if (!newIdList.contains(persistedId)) {
                    caronaLicitanterepository.deleteById(persistedId);
                }
            }
        }
    }

    @Override
    public Carona save(Carona entity) throws AppException {
        beforeSave(entity);
        resolverRelacionamentos(entity);
        checarSeJahExiste(entity);
        validar(entity);
        if (entity.getId() != null) {
            beforeUpdate(entity);
        }
        boolean isNew = entity.getId() == null;
        Carona e = getRepository().saveAndFlush(entity);
        afterSave(isNew, e);
        return entity;
    }

    public void saveCarona(CaronaDTO dto) throws AppException {
        Carona carona = dto.getCarona();
        List<ArquivoCaronaDTO> arquivos = dto.getArquivosCarona();
        validarArquivos(arquivos, dto.getFiltros());

        if (carona.getLicitacao() != null) {
            Licitacao licitacao = licitacaoRepository.getById(carona.getLicitacao().getId());
            carona.setLicitacao(licitacao);
        }

        TermoReferencia termoReferencia = carona.getTermoReferencia();
        if (carona.getId() == null && termoReferencia != null && termoReferenciaRepository.checkAssociatedTerm(termoReferencia.getId())) {
            throw new AppException("A operação não pôde ser concluída, pois o Termo de Referência escolhido já está associado a algum processo.", HttpStatus.UNPROCESSABLE_ENTITY);
        }

        if (!carona.getProcessoEntidadeOrigem() && !carona.getLei().equals("LEI_N_8666")) {
            if (termoReferencia == null || termoReferencia.getIdentificadorProcesso() == null) {
                throw new AppException("A operação não pôde ser concluída, pois o Termo de Referência escolhido deve possuir um identificador.", HttpStatus.UNPROCESSABLE_ENTITY);
            }

            if (termoReferencia != null && !termoReferencia.getIsFinalizado()) {
                termoReferencia = termoReferenciaService.finalizarTermo(termoReferencia, "Carona");
                carona.setTermoReferencia(termoReferencia);
            }
        }

        carona = save(carona);
        saveArquivos(arquivos, carona);
    }

    @Override
    public void remover(Long id) throws AppException {
        Optional<Carona> entidadeOpt = getRepository().findById(id);
        if (entidadeOpt.isPresent()) {
            Carona carona = entidadeOpt.get();
            validarRemover(carona);
            carona.setStatus(StatusLicitacao.REMOVIDA);
            getRepository().save(carona);
        }
    }

    @Override
    public BuscaResponse<Carona> buscarAdvanced(AdvancedSearchRequest filtro) {
        setDefaultFilterParams(filtro);
        return super.buscarAdvanced(filtro);
    }

    @Override
    protected void setDefaultFilterParams(AdvancedSearchRequest filtro) {
        AdvancedSearchParameter situacaoParam = new AdvancedSearchParameter("status", SearchOperator.NOT_EQUAL_TO, StatusLicitacao.REMOVIDA.name());
        filtro.getAndParameters().add(situacaoParam);
    }

    @Override
    public ArquivoCaronaService getArquivoService() {
        return arquivoCaronaService;
    }

    @Override
    public ArquivoCaronaFileService getFileService() {
        return arquivoCaronaFileService;
    }

    @Override
    public ArquivoCaronaToDtoMapper getMapper() {
        return arquivoCaronaToDtoMapper;
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() {
        AdvancedSearchRequest filtro = new AdvancedSearchRequest();
        this.inicializaFiltro(filtro);
        AdvancedSearchParameter arquivoObjetoParam = new AdvancedSearchParameter("objeto", SearchOperator.EQUAL_TO, Objeto.CARONA.name());
        filtro.getAndParameters().add(arquivoObjetoParam);

        return obrigatoriedadeArquivoService.buscarAdvanced(filtro).getItems();
    }

    private List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios(ArquivoFiltroCarona filtros) {
        List<String> filtrosArquivos = Stream.of(
                filtros.getLei(),
                filtros.getTipoOrgao(),
                filtros.getTipoCarona(),
                filtros.getNaturezaObjObra()
        ).filter(Objects::nonNull).collect(Collectors.toList());
        List<ObrigatoriedadeArquivo> obrigatoriedadeArquivos = this.obrigatoriedadeArquivoService.getArquivosObrigatorios("CARONA", filtrosArquivos);
        return obrigatoriedadeArquivos.stream().filter(ObrigatoriedadeArquivo::getObrigatorio).collect(Collectors.toList());
    }

    @Override
    protected ArquivoCarona getNewArquivo() {
        return new ArquivoCarona();
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoCarona arquivoEntity, Carona entity, ArquivoCaronaDTO arquivoUpload) {
        arquivoEntity.setCarona(entity);
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    @Override
    public List<ItemContrato> itensEmContratos(Long idCarona) {
        return this.repository.itensEmContratos(idCarona);
    }

    @Override
    public Carona getById(Long idCarona) {
        Carona carona = super.getById(idCarona);
        carona.setTda(tdaRepository.getByIdCarona(idCarona));
        return carona;
    }

    @Override
    public UltimaAlteracaoDTO getUltimaAlteracao(Long idCarona) {
        List<Object[]> ultimasModificadores = repository.getUltimaAlteracao(idCarona);
        if (ultimasModificadores.size() > 0) {
            UltimaAlteracaoDTO response = new UltimaAlteracaoDTO();
            Object[] ultimaModificacao = ultimasModificadores.get(0);
            response.setData(((Timestamp) ultimaModificacao[1]).toLocalDateTime());
            response.setNome((String) ultimaModificacao[0]);
            return response;
        } else {
            return null;
        }
    }

    @Override
    public Boolean getTresCasasDecimais(Long idCarona) {
        return getRepository().getTresCasasDecimais(idCarona);
    }

    public ArquivoBinarioDTO download(ArquivoDTO arquivoDTO, Boolean countDownloads) throws AppException {
        ArquivoBinarioDTO download = arquivoCaronaFileService.download(arquivoDTO);
        if (arquivoDTO.ehArquivoDefinitivoValido() && Boolean.TRUE.equals(countDownloads)) {
            Long idArquivoCarona = Long.parseLong(arquivoDTO.getLookupId());
            repository.updateNumeroDownloadsCarona(idArquivoCarona);
        }
        return download;
    }

    @Override
    public void setEmAnalise(Long idProcesso, Boolean emAnalise) {
        LocalDateTime dataAnalise = LocalDateTime.now();
        if (!emAnalise) {
            dataAnalise = null;
        }
        this.repository.updateEmAnalise(idProcesso, emAnalise, dataAnalise);
        this.repository.updateLastAuditTimestamp(idProcesso);
    }

    @Override
    public void desarquivarCarona(Long idProcesso) {
        this.repository.desarquivarCarona(idProcesso, LocalDateTime.now());
        this.repository.updateLastAuditTimestamp(idProcesso);
    }

    @Override
    protected void afterSave(boolean isNew, Carona entity) {
        if (isNew) {
            Usuario usuario = getCurrentUser();
            EmailDTO emailDTO = new EmailDTO();
            emailDTO.setUsuario(usuario.getLogin());
            emailDTO.setSistema(app);
            emailDTO.setAssunto("Confirmação de Cadastro");
            emailDTO.setDestinatarios(new HashSet<>(Collections.singletonList(usuario.getEmail())));

            String naturezas = "";
            if (entity.getNaturezasDoObjeto() != null) {
                naturezas = StringUtils.join(entity.getNaturezasDoObjeto().stream().map(n -> n.getValor()).collect(Collectors.toList()), ", ");
            }
            String mensagem = String.format(GeradorMensagemEmail.getMensagemCadastroCarona(), usuario.getNome(), entity.getDataCadastro().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")), entity.getNumeroProcessoGerenciadorAta(), naturezas, entity.getObjeto());
            emailDTO.setMensagem(mensagem);
            emailService.sendEmail(emailDTO);
        }
        this.asyncService.analisaProcesso(entity.getId(), "C");
        repository.updateLastModifiedTimestamp(entity.getId());
    }

    @Override
    public void arquivarProcesso(Long idCarona, StatusAuditoriaLicitacao status) {
        if (isUsuarioInspetor() || ThreadContext.get("groups").equals("Administrador")) {
            Optional<Carona> optCarona = getRepository().findById(idCarona);
            if (optCarona.isPresent()) {
                Carona carona = optCarona.get();
                carona.setStatusProcessoArquivado(status);
                getRepository().save(carona);
                getRepository().updateLastAuditTimestamp(idCarona);
            }
        } else {
            throw new AppException("O usuário não tem permissão para realizar a operação", HttpStatus.UNAUTHORIZED);
        }
    }

    private Boolean checkXlsMimeTypes(String mimeType) {
        List<ContentType> types = Arrays.asList(ContentType.MICROSOFT_EXCEL,
                ContentType.MICROSOFT_EXCEL_XML,
                ContentType.CSV,
                ContentType.PDF);
        return types.stream().anyMatch(type -> type.getMimeType().equals(mimeType));
    }

    @Override
    public void validarArquivos(List<ArquivoCaronaDTO> arquivos, ArquivoFiltroCarona filtros) {
        if (arquivos.size() == 0) {
            throw new AppException("É necessário adicionar arquivos!", HttpStatus.BAD_REQUEST);
        }

        List<TipoArquivoCarona> tiposArquivosEnviados = extrairTiposArquivos(arquivos);
        this.validarArquivosTipos(tiposArquivosEnviados, filtros);
        this.verificarDescricaoTipoOutros(arquivos);

        if (arquivos.stream().anyMatch(arquivo -> arquivo.getTipo().equals(TipoArquivoCarona.MAPA_COMPARATIVO_ADERENTE) && !checkXlsMimeTypes(arquivo.getArquivo().getTipoArquivo()))) {
            throw new AppException("O arquivo do tipo 'Mapa Comparativo de Preços do Órgão/Entidade aderente (carona)' deve ser uma planilha ou um PDF", HttpStatus.BAD_REQUEST);
        }

        if (arquivos.stream().anyMatch(arquivo -> arquivo.getTipo().equals(TipoArquivoCarona.MAPA_LANCES) && !checkXlsMimeTypes(arquivo.getArquivo().getTipoArquivo()))) {
            throw new AppException("O arquivo do tipo 'Mapa de lances da licitação' deve ser uma planilha ou um PDF", HttpStatus.BAD_REQUEST);
        }

        if (arquivos.stream().anyMatch(arquivo -> arquivo.getTipo().equals(TipoArquivoCarona.PROJETO_ENGENHARIA) && !checkTypeDWG(arquivo.getArquivo().getTipoArquivo()))) {
            throw new AppException("O arquivo do tipo 'Projetos de engenharia (básico, executivo e afins)' deve ser em formato DWG", HttpStatus.BAD_REQUEST);
        }

        if (arquivos.stream().anyMatch(arquivo -> arquivo.getTipo().equals(TipoArquivoCarona.MAPA_COMPARATIVO_GERENCIADOR) && !checkXlsMimeTypes(arquivo.getArquivo().getTipoArquivo()))) {
            throw new AppException("O arquivo do tipo 'Mapa comparativo de preços do Órgão/Entidade gerenciador' deve ser uma planilha ou um PDF", HttpStatus.BAD_REQUEST);
        }

        List<ArquivoCaronaDTO> arquivoErro = arquivos.stream()
                .filter(arquivo ->
                        !arquivo.getTipo().equals(TipoArquivoCarona.PROJETO_ENGENHARIA) &&
                                !arquivo.getTipo().equals(TipoArquivoCarona.MAPA_COMPARATIVO_ADERENTE) &&
                                !arquivo.getTipo().equals(TipoArquivoCarona.MAPA_LANCES) &&
                                !arquivo.getArquivo().getTipoArquivo().equals(ContentType.PDF.getMimeType()))
                .collect(Collectors.toList());

        if (!arquivoErro.isEmpty()) {
            throw new AppException("O arquivo do tipo '" + arquivoErro.get(0).getTipo().getValor() + "' deve ser um PDF", HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    public void validarArquivosTipos(List<TipoArquivoCarona> tipos, ArquivoFiltroCarona filtros) throws AppException {
        this.verificarArquivosObrigatorios(tipos, this.getArquivosTiposObrigatorios(filtros));
    }

    @Override
    public List<Integer> getAnosCarona() {
        List<Integer> years = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (int year = 2002; year <= now.getYear(); year++) {
            years.add(year);
        }
        if (now.getMonth().getValue() == 12) {
            years.add(now.getYear() + 1);
        }
        years.sort(Collections.reverseOrder());
        return years;
    }

    @Override
    public List<PessoaResponsavelEntidadeResponseDTO> getResponsaveisCaronaEntidadeByIdEntidade(Long idEntidade) {
        return this.cjurApiService.getPessoasResponsaveisEntidadeByIdEntidade(idEntidade);
    }

    @Override
    public void finalizarTermoCarona(Long id) {
        Optional<Carona> entidadeOpt = getRepository().findById(id);
        if (entidadeOpt.isPresent()) {
            Carona carona = entidadeOpt.get();
            if (carona.getTermoReferencia() != null) {
                TermoReferencia termoReferencia = carona.getTermoReferencia();
                termoReferencia.setIsFinalizado(false);
            }
        }
    }

    public Long getAlertaCarona(Long idProcesso, String tipoProcesso) {
        AnaliseProcessoView alerta = analiseProcessoViewService.getProcesso(idProcesso, tipoProcesso);
        if (alerta != null) {
            return alerta.getIdAlertaAnalise();
        }
        return null;
    }

    @Override
    public void anularRevogar(Long idProcesso, AnulacaoRevogacaoDTO dto) {
        Carona entity = repository.getById(idProcesso);
        AnulacaoRevogacao persistedAnulacaoRevogacao = anulacaoRevogacaoService.saveAnulacaoRevogacao(dto);
        entity.setAnulacaoRevogacao(persistedAnulacaoRevogacao);
        save(entity);
    }
}
