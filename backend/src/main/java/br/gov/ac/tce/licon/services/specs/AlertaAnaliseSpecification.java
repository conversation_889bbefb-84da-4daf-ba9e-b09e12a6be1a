package br.gov.ac.tce.licon.services.specs;

import br.gov.ac.tce.licon.dtos.requests.AlertaAnaliseFiltroRequest;
import br.gov.ac.tce.licon.entities.AlertaAnalise;
import br.gov.ac.tce.licon.entities.AlertaAnalise_;
import lombok.AllArgsConstructor;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public class AlertaAnaliseSpecification implements ISpecification<AlertaAnalise> {

	private static final long serialVersionUID = -6136676263926937229L;

	private final AlertaAnaliseFiltroRequest filtro;

	@Override
	public Predicate toPredicate(Root<AlertaAnalise> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
		List<Predicate> predicados = new ArrayList<>();

		addIfExists(getPredicate(root.get(AlertaAnalise_.data), filtro.getData(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.mensagem), filtro.getMensagem(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.prazoResposta), filtro.getPrazoResposta(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.usuarioAtual), filtro.getUsuarioAtual(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.usuarioResponsavel), filtro.getUsuarioResponsavel(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.diretorDafo), filtro.getDiretorDafo(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.conselheiro), filtro.getConselheiro(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.jurisdicionadoResposta), filtro.getJurisdicionadoResposta(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.usuarioRejeicao), filtro.getUsuarioRejeicao(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.status), filtro.getStatus(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.destinatarios), filtro.getDestinatarios(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.resposta), filtro.getResposta(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.respostaRejeicao), filtro.getRespostaRejeicao(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.dataRejeicao), filtro.getDataRejeicao(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.dataResposta), filtro.getDataResposta(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.passouPorAuditor), filtro.getPassouPorAuditor(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.passouPorDafo), filtro.getPassouPorDafo(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.dataArquivamento), filtro.getDataArquivamento(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.tipo), filtro.getTipo(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.usuarioArquivamento), filtro.getUsuarioArquivamento(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.jurisdicionado), filtro.getJurisdicionado(), builder, filtro.getFilterType()), predicados);
		addIfExists(getPredicate(root.get(AlertaAnalise_.dataVisualizacao), filtro.getDataVisualizacao(), builder, filtro.getFilterType()), predicados);

		return builder.and(predicados.toArray(new Predicate[0]));
	}

}
