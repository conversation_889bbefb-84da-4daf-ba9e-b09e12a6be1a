package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.requests.VencedorLicitacaoFiltroRequest;
import br.gov.ac.tce.licon.entities.VencedorLicitacao;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.VencedorLicitacaoRepository;
import br.gov.ac.tce.licon.services.VencedorLicitacaoService;
import br.gov.ac.tce.licon.services.specs.VencedorLicitacaoSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public class VencedorLicitacaoServiceImpl extends AbstractService<VencedorLicitacao, VencedorLicitacaoFiltroRequest, VencedorLicitacaoRepository> implements VencedorLicitacaoService {

	@Autowired
	private VencedorLicitacaoRepository repository;
	
	@Override
	public VencedorLicitacaoRepository getRepository() {
		return repository;
	}
	
	@Override
	protected Example<VencedorLicitacao> obterExemploChecarSeJahExiste(VencedorLicitacao entity) throws AppException {
		VencedorLicitacao exemplo = VencedorLicitacao.builder().
				licitacao(entity.getLicitacao()).
				licitante(entity.getLicitante()).
				valor(entity.getValor()).
				build();
		return Example.of(exemplo);
	}

	@Override
	protected void lancarErroEntidadeJahExistente(VencedorLicitacao entity) throws AppException {
		throw new AppException(String.format("Vencedor %s da licitação %s no valor %f já existe",
				entity.getLicitante().getNome(), entity.getLicitacao().getNumero() + "/" + entity.getLicitacao().getAno(),
				entity.getValor()),
				HttpStatus.UNPROCESSABLE_ENTITY);
	}

	@Override
	protected Specification<VencedorLicitacao> getSpecification(VencedorLicitacaoFiltroRequest filtro) {
		return new VencedorLicitacaoSpecification(filtro);
	}

	@Override
	public List<VencedorLicitacao> getAllByLicitacao(Long idLicitacao) {
		return repository.getByLicitacao(idLicitacao);
	}

}
