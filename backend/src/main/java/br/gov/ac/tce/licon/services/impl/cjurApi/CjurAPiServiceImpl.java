package br.gov.ac.tce.licon.services.impl.cjurApi;

import br.gov.ac.tce.licon.dtos.responses.cjurApi.EntidadeResponseDTO;
import br.gov.ac.tce.licon.dtos.responses.cjurApi.PessoaResponsavelEntidadeResponseDTO;
import br.gov.ac.tce.licon.entities.Entidade;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.services.EntidadeService;
import br.gov.ac.tce.licon.services.cjurApi.CjurApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class CjurAPiServiceImpl implements CjurApiService {

    @Value("${spring.cjurapi.url}")
    protected String urlCjurApi;

    @Value("${spring.profiles.active}")
    private String profile;

    @Autowired
    private EntidadeService entidadeService;

    public List<PessoaResponsavelEntidadeResponseDTO> getPessoasResponsaveisEntidadeByIdEntidade(Long idEntidade) {
        Entidade entidade = entidadeService.getById(idEntidade);
        try {
            if (profile.equals("dev")) {
                List<PessoaResponsavelEntidadeResponseDTO> pessoasResponsaveis = new ArrayList<>();
                PessoaResponsavelEntidadeResponseDTO pessoa1 = new PessoaResponsavelEntidadeResponseDTO(1L, "Usuário Desenvolvimento 1", false, new Date());
                PessoaResponsavelEntidadeResponseDTO pessoa2 = new PessoaResponsavelEntidadeResponseDTO(2L, "Usuário Desenvolvimento 2", false, new Date());
                pessoasResponsaveis.add(pessoa1);
                pessoasResponsaveis.add(pessoa2);
                return pessoasResponsaveis;
            } else {
                String url = urlCjurApi + "/pessoa/responsaveis-entidade/{id}";
                RestTemplate restTemplate = new RestTemplate();
                return restTemplate.exchange(
                        url,
                        HttpMethod.GET,
                        null,
                        new ParameterizedTypeReference<List<PessoaResponsavelEntidadeResponseDTO>>() {
                        },
                        entidade.getEntidadeCjur()
                ).getBody();
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
            throw new AppException("Responsáveis Entidade: Ocorreu um erro ao buscar os dados.");
        }
    }

    public List<EntidadeResponseDTO> getEntidades() {
        try {
            String url = urlCjurApi + "/entidade";
            RestTemplate restTemplate = new RestTemplate();
            return restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<List<EntidadeResponseDTO>>() {
                    }
            ).getBody();
        } catch (Exception e) {
            System.out.println(e.getMessage());
            throw new AppException("Entidades: Ocorreu um erro ao buscar os dados.");
        }
    }
}
