package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.requests.CredenciadoItemFiltroRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.responses.BuscaResponse;
import br.gov.ac.tce.licon.entities.Credenciado;
import br.gov.ac.tce.licon.entities.CredenciadoItem;
import br.gov.ac.tce.licon.entities.ItemContrato;
import br.gov.ac.tce.licon.repositories.CredenciadoItemRepository;
import br.gov.ac.tce.licon.repositories.CredenciadoRepository;
import br.gov.ac.tce.licon.services.CredenciadoItemService;
import br.gov.ac.tce.licon.services.specs.CredenciadoItemSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@Transactional
public class CredenciadoItemServiceImpl extends AbstractViewService<CredenciadoItem, CredenciadoItemFiltroRequest, CredenciadoItemRepository> implements CredenciadoItemService {

    @Autowired
    private CredenciadoItemRepository repository;

    @Autowired
    private CredenciadoRepository credenciadoRepository;

    @Override
    public CredenciadoItemRepository getRepository() {
        return repository;
    }

    @Override
    public BuscaResponse<CredenciadoItem> buscarAdvanced(AdvancedSearchRequest filtro) {
        setDefaultFilterParams(filtro);
        return super.buscarAdvanced(filtro);
    }

    @Override
    protected void setDefaultFilterParams(AdvancedSearchRequest filtro) {
    }

    @Override
    protected Specification<CredenciadoItem> getSpecification(CredenciadoItemFiltroRequest filtro) {
        return new CredenciadoItemSpecification(filtro);
    }

    @Override
    public List<CredenciadoItem> getAllByCredenciado(Long idCredenciado) {
        return repository.getByCredenciado(idCredenciado);
    }

    @Override
    public List<CredenciadoItem> getAllByCredenciamento(Long idCredenciamento) {
        List<Credenciado> credenciados = credenciadoRepository.getCredenciadoByCredenciamentoId(idCredenciamento);
        List<CredenciadoItem> credenciadoItems = new ArrayList<>();
        credenciados.forEach((credenciado -> {
            credenciado.getCredenciadoItems().forEach((credenciadoItem -> {
                credenciadoItems.add(credenciadoItem);
            }));
        }));

        return credenciadoItems;
    }

    @Override
    public List<ItemContrato> itensEmContratos(Long idCredenciamento) {
        return this.repository.itensEmContratos(idCredenciamento);
    }
}
