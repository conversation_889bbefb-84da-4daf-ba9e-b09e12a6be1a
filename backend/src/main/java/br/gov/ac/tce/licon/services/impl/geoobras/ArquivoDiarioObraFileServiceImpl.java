package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.entities.Arquivo;
import br.gov.ac.tce.licon.entities.enums.TipoEntidade;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.services.geoobras.ArquivoDiarioObraFileService;
import br.gov.ac.tce.licon.services.geoobras.ArquivoDiarioObraService;
import br.gov.ac.tce.licon.services.impl.AbstractFileServiceImpl;
import org.springframework.stereotype.Service;

import javax.inject.Inject;

@Service
public class ArquivoDiarioObraFileServiceImpl extends AbstractFileServiceImpl implements ArquivoDiarioObraFileService {

    @Inject
    private ArquivoDiarioObraService arquivoDiarioObraService;

    @Override
    protected Arquivo lookupArquivoParaDownload(Long idArquivo) throws AppException {
        return arquivoDiarioObraService.getById(idArquivo);
    }

    @Override
    protected TipoEntidade getTipoEntidade() {
        return TipoEntidade.DIARIO_OBRA;
    }
}
