package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.mapper.ArquivoRelatorioObraToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.ArquivoRelatorioObraDTO;
import br.gov.ac.tce.licon.dtos.requests.ArquivoRelatorioObraFiltroRequest;
import br.gov.ac.tce.licon.dtos.requests.geoobras.RelatorioObraFiltroRequest;
import br.gov.ac.tce.licon.entities.ArquivoRelatorioObra;
import br.gov.ac.tce.licon.entities.ObrigatoriedadeArquivo;
import br.gov.ac.tce.licon.entities.Usuario;
import br.gov.ac.tce.licon.entities.enums.TipoArquivoRelatorioObra;
import br.gov.ac.tce.licon.entities.geoobras.ItemObra;
import br.gov.ac.tce.licon.entities.geoobras.RelatorioObra;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.geoobras.RelatorioObraRepository;
import br.gov.ac.tce.licon.services.*;
import br.gov.ac.tce.licon.services.geoobras.CatalogoObraElasticService;
import br.gov.ac.tce.licon.services.specs.geoobras.RelatorioObraSpecification;
import com.google.common.collect.ImmutableList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

@Service
@Transactional
public class RelatorioObraServiceImpl extends AbstractUploadTipoServiceImpl<RelatorioObra, RelatorioObraFiltroRequest, RelatorioObraRepository, ArquivoRelatorioObraFileService, ArquivoRelatorioObra, ArquivoRelatorioObraFiltroRequest, ArquivoRelatorioObraService, ArquivoRelatorioObraDTO, ArquivoRelatorioObraToDtoMapper, TipoArquivoRelatorioObra> implements RelatorioObraService {

    @Autowired
    private RelatorioObraRepository repository;

    @Autowired
    private CatalogoObraElasticService catalogoObraElasticService;

    @Autowired
    private ArquivoRelatorioObraService arquivoRelatorioObraService;

    @Autowired
    private ArquivoRelatorioObraFileService arquivoRelatorioObraFileService;

    @Autowired
    private ArquivoRelatorioObraToDtoMapper arquivoRelatorioObraToDtoMapper;

    @Autowired
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Autowired
    private NotificacaoService notificacaoService;

    @Override
    public RelatorioObraRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<RelatorioObra> getSpecification(RelatorioObraFiltroRequest filtro) {
        return new RelatorioObraSpecification(filtro);
    }

    @Override
    protected void beforeSave(RelatorioObra entity) {
        if (entity.getId() == null) {
            if (entity.getUsuario() == null) {
                entity.setUsuario(getCurrentUser());
            }
            entity.setCorrespondenciaRealizada(false);
            entity.setImportado(false);
            entity.setDataCadastro(LocalDateTime.now());
        }
    }

    @Override
    public RelatorioObra getById(Long id) throws AppException {
        Optional<RelatorioObra> entidadeOpt = getRepository().findById(id);
        if (entidadeOpt.isPresent()) {
            List<ItemObra> itensObra = entidadeOpt.get().getItensObra();
            itensObra = catalogoObraElasticService.getReferenciasByItens(itensObra);
            entidadeOpt.get().setItensObra(itensObra);
            return entidadeOpt.get();
        } else {
            throw new AppException(String.format("Entidade '%s' com ID '%d' não encontrada.", getEntityName(), id), HttpStatus.NOT_FOUND);
        }
    }

    @Override
    public ArquivoRelatorioObraService getArquivoService() {
        return this.arquivoRelatorioObraService;
    }

    @Override
    public ArquivoRelatorioObraFileService getFileService() {
        return this.arquivoRelatorioObraFileService;
    }

    @Override
    public ArquivoRelatorioObraToDtoMapper getMapper() {
        return this.arquivoRelatorioObraToDtoMapper;
    }

    @Override
    protected ArquivoRelatorioObra getNewArquivo() {
        return new ArquivoRelatorioObra();
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() {
        return obrigatoriedadeArquivoService.getArquivosObrigatorios("RELATORIO_OBRA", ImmutableList.of("RELATORIO_OBRA"));
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoRelatorioObra arquivoEntity, RelatorioObra entity, ArquivoRelatorioObraDTO arquivoUpload) {
        arquivoEntity.setRelatorioObra(entity);
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    @Async("taskExecutor")
    public void importarRelatorio(RelatorioObra relatorioObra, List<ArquivoRelatorioObraDTO> arquivos) {
        Usuario usuario = getCurrentUser();
        try {
            validarArquivos(arquivos);
            relatorioObra.setImportado(true);
            relatorioObra.setCorrespondenciaRealizada(false);
            relatorioObra.setDataCadastro(LocalDateTime.now());
            relatorioObra.setUsuario(usuario);
            RelatorioObra relatorioObraPersisted = this.repository.save(relatorioObra);
            saveArquivos(arquivos, relatorioObraPersisted);
            RelatorioObra relatorio = this.gerarItensImportados(relatorioObraPersisted);
            if (relatorio.getItensObra().isEmpty()) {
                this.remover(relatorioObra.getId());
                this.notificacaoService.saveNotificacao("SICRO/SINAPI", "Data desejada sem itens próximos no período antecedente de 3 meses!", "", null, null, List.of(usuario));
            } else {
                repository.save(relatorio);
                String link = String.format("/obras/relatorio-obra", relatorioObraPersisted.getId());
                this.notificacaoService.saveNotificacao("SICRO/SINAPI", "Relatório importado com sucesso!", "", link, null, List.of(usuario));
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
            this.notificacaoService.saveNotificacao("SICRO/SINAPI", "Ocorreu um erro ao importar o relatório!", "Entre em contato com a equipe de manutenção!", null, null, List.of(usuario));
        }
    }

    private RelatorioObra gerarItensImportados(RelatorioObra relatorioObra) {
        relatorioObra.setItensObra(new ArrayList<>());
        List<ArquivoRelatorioObraDTO> arquivos = this.recuperarArquivos(relatorioObra.getId());
        ArquivoBinarioDTO arquivoBinarioDTO = this.download(arquivos.get(0).getArquivo());
        byte[] binary = arquivoBinarioDTO.getBinario();
        ByteArrayResource resource = new ByteArrayResource(binary);
        try {
            Workbook workbook = new XSSFWorkbook(resource.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            Row rowHeader = sheet.getRow(0);
            Map<Integer, String> columnsPositions = validateHeader(rowHeader);
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                String cellCodigo = "";
                String cellDescricao = "";
                if (row.getCell(0) != null) {
                    Cell cell = row.getCell(0);
                    cellCodigo = (cell.getCellType() == CellType.NUMERIC) ? String.valueOf((int) cell.getNumericCellValue()) : cell.getStringCellValue();
                }
                if (row.getCell(1) != null) {
                    cellDescricao = row.getCell(1).getStringCellValue();
                }
                if ((cellCodigo != null && !cellCodigo.isEmpty()) || (cellDescricao != null && !cellDescricao.isEmpty())) {
                    ItemObra itemObraGerado = gerarItem(row, columnsPositions, relatorioObra.getDataDesejavel());
                    if (itemObraGerado != null) {
                        itemObraGerado.setRelatorioObra(relatorioObra);
                        relatorioObra.getItensObra().add(itemObraGerado);
                    }
                }
            }
        } catch (IOException e) {
            System.out.println(e.getMessage());
        }
        return relatorioObra;
    }

    private ItemObra gerarItem(Row row, Map<Integer, String> positionColumns, LocalDate dataDesejavel) {
        ItemObra itemObra = new ItemObra();
        itemObra.setImportado(true);
        for (int columnIndex = 0; columnIndex < row.getLastCellNum(); columnIndex++) {
            Cell cell = row.getCell(columnIndex);
            String currentColumn = positionColumns.get(columnIndex);
            if (cell != null && currentColumn != null && !currentColumn.isEmpty() && !currentColumn.isBlank()) {
                if (currentColumn.equals("CODIGO")) {
                    String value = (cell.getCellType() == CellType.NUMERIC) ? String.valueOf((int) cell.getNumericCellValue()) : cell.getStringCellValue();
                    itemObra.setCodigo(value);
                    itemObra.setCodigoPlanilha(value);
                } else if (currentColumn.equals("DESCRICAO")) {
                    String value = cell.getStringCellValue();
                    itemObra.setDescricaoPlanilha(value);
                } else if (currentColumn.equals("QUANT")) {
                    Integer value = Integer.valueOf((int) cell.getNumericCellValue());
                    itemObra.setQuantidade(BigDecimal.valueOf(value));
                } else if (currentColumn.equals("PREÇO_UNITARIO")) {
                    BigDecimal value = BigDecimal.valueOf(cell.getNumericCellValue());
                    itemObra.setPrecoUnitario(value);
                } else if (currentColumn.equals("UNIDADE_DE_MEDIDA")) {
                    String value = cell.getStringCellValue();
                    itemObra.setUnidadeMedidaPlanilha(value);
                }
            }
        }
        return initItemObraElastic(itemObra, dataDesejavel);
    }

    private ItemObra initItemObraElastic(ItemObra itemObra, LocalDate dataDesejavel) {
        return this.catalogoObraElasticService.findItemObra(itemObra, dataDesejavel);
    }

    private Map<Integer, String> validateHeader(Row row) {
        List<String> columns = ImmutableList.of("CODIGO", "DESCRICAO", "QUANT", "PREÇO_UNITARIO", "UNIDADE_DE_MEDIDA");
        Map<Integer, String> columnIndexMap = new HashMap<>();

        String outputError = "";
        for (String columnName : columns) {
            boolean columnFound = false;
            for (int columnIndex = 0; columnIndex < row.getLastCellNum(); columnIndex++) {
                Cell cell = row.getCell(columnIndex);
                if (cell != null && cell.getStringCellValue().equals(columnName)) {
                    columnFound = true;
                    columnIndexMap.put(columnIndex, columnName);
                    break;
                }
            }
            if (!columnFound) {
                outputError += columnName + ",";
            }
        }

        if (!outputError.isEmpty()) {
            outputError = outputError.substring(0, outputError.length() - 1);
            throw new IllegalArgumentException("As seguintes colunas estão faltando: " + outputError);
        }

        return columnIndexMap;
    }

    @Override
    @Transactional
    public void remover(Long id) throws AppException {
        Optional<RelatorioObra> entidadeOpt = getRepository().findById(id);
        if (entidadeOpt.isPresent()) {
            RelatorioObra entidade = entidadeOpt.get();
            validarRemover(entidade);
            this.arquivoRelatorioObraService.deletarArquivo(entidade);
            getRepository().delete(entidade);
        } else {
            throw new AppException(String.format("Entidade '%s' com ID '%d' não encontrada.", getEntityName(), id), HttpStatus.NOT_FOUND);
        }
    }

}
