package br.gov.ac.tce.licon.services.specs;

import br.gov.ac.tce.licon.dtos.requests.BoardLicitacaoViewFiltroRequest;
import br.gov.ac.tce.licon.entities.BoardLicitacaoView;
import br.gov.ac.tce.licon.entities.BoardLicitacaoView_;
import lombok.AllArgsConstructor;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public class BoardLicitacaoViewSpecification implements ISpecification<BoardLicitacaoView> {

    private static final long serialVersionUID = 783351234654331L;

    private final BoardLicitacaoViewFiltroRequest filtro;

    @Override
    public Predicate toPredicate(Root<BoardLicitacaoView> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
        List<Predicate> predicados = new ArrayList<>();
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.numero), filtro.getNumero(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.numeroAnoLicitacao), filtro.getNumeroAnoLicitacao(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.ano), filtro.getAno(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.objeto), filtro.getObjeto(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.entidade), filtro.getEntidade(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.statusOcorrenciaAtual), filtro.getStatusOcorrenciaAtual(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.naturezaOcorrencia), filtro.getNaturezaOcorrencia(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.fase), filtro.getFase(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.orgao), filtro.getOrgao(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.dataCadastro), filtro.getDataCadastro(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.valorEstimado), filtro.getValorEstimado(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.valorAdjudicado), filtro.getValorAdjudicado(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.dataAbertura), filtro.getDataAbertura(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.status), filtro.getStatus(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.ultimaOcorrencia), filtro.getUltimaOcorrencia(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.dataUltimaOcorrencia), filtro.getDataUltimaOcorrencia(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.dataCadastroPreparatoria), filtro.getDataCadastroPreparatoria(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(BoardLicitacaoView_.dataCadastroVencedores), filtro.getDataCadastroVencedores(), builder, filtro.getFilterType()), predicados);
        return builder.and(predicados.toArray(new Predicate[0]));
    }

}
