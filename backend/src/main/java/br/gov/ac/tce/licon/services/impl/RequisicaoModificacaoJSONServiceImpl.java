package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.ArquivoDTO;
import br.gov.ac.tce.licon.dtos.mapper.*;
import br.gov.ac.tce.licon.dtos.requests.*;
import br.gov.ac.tce.licon.entities.*;
import br.gov.ac.tce.licon.entities.enums.*;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.*;
import br.gov.ac.tce.licon.services.*;
import br.gov.ac.tce.licon.utils.UtilsRequisicaoModificacao;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class RequisicaoModificacaoJSONServiceImpl extends AbstractService<RequisicaoModificacao, RequisicaoModificacaoFiltroRequest, RequisicaoModificacaoRepository> implements RequisicaoModificacaoService {

    private static final String NEW_VALUE_FIELD_NAME = "newValue";

    private static final String OLD_VALUE_FIELD_NAME = "oldValue";

    private static final String ADMIN_PERMISSION_NAME = "admin";

    private static final String USER_ID_THREAD_KEY = "userId";

    private static final String ENTIDADE_FIELD = "entidade";

    private static final String TERMO_REFERENCIA_FIELD = "termoReferencia";

    private static final String PROCESSO_FIELD = "processo";

    private static final String CONTRATO_LICITANTE_FIELD = "contratoLicitante";

    private static final String ID_REQUISICAO_MODIFICACAO_FIELD = "idRequisicaoModificacao";

    private static final String DIFFERENCES_FIELD = "differences";

    private static final String FILES_FIELD = "files";

    private static final String MODIFICADO_FIELD = "modificado";

    private static final String ID_ARQUIVO_FIELD = "idArquivo";

    private static final String ID_FIELD = "id";

    private static final String REMOCAO_TAG = "REMOCAO";

    private static final String ADICAO_TAG = "ADICAO";

    private static final String EDICAO_TAG = "EDICAO";

    private static final String ORIGINAL_PREFIX = "original";

    private static final String NEW_PREFIX = "new";

    private static final String OBRA_FIELD = "obra";

    private static final String ITENS_DESERTOS_FIELD = "itensDesertos";

    private static final String LOTES_FRACASSADOS_FIELD = "lotesFracassados";

    private final List<String> complexFields = Arrays.asList("vencedores", "detentores", "fornecedores", OBRA_FIELD, "edificacao", "lotes", "itens", "secoesCriadas", "secoes", "licitantes", "publicacoes", FILES_FIELD, "arquivosTemporarios", "secao");

    private final List<String> requiredOriginalAttributes = Arrays.asList(TERMO_REFERENCIA_FIELD, "lote");

    private final List<String> attributesToIgnoreChangesOnView = Arrays.asList("itemCatalogo", "arquivosTemporarios", "fase", "size");

    @Inject
    private UsuarioService usuarioService;

    @Autowired
    private LicitacaoService licitacaoService;

    @Autowired
    private EntidadeService entidadeService;

    @Inject
    private ContratoService contratoService;

    @Inject
    private InexigibilidadeService inexigibilidadeService;

    @Inject
    private CaronaService caronaService;

    @Inject
    private DispensaService dispensaService;

    @Autowired
    private CredenciamentoRepository credenciamentoRepository;

    @Autowired
    private CaronaRepository caronaRepository;

    @Autowired
    private InexigibilidadeRepository inexigibilidadeRepository;

    @Autowired
    private DispensaRepository dispensaRepository;

    @Inject
    private AditivoContratoService aditivoContratoService;

    @Autowired
    private RequisicaoModificacaoRepository requisicaoModificacaoRepository;

    @Autowired
    private UserPermissionService userPermissionService;

    @Autowired
    private TermoReferenciaService termoReferenciaService;

    @Autowired
    private OcorrenciaLicitacaoService ocorrenciaLicitacaoService;

    @Autowired
    private ArquivoTermoReferenciaRepository arquivoTermoReferenciaRepository;

    @Autowired
    private ArquivoTermoReferenciaToDtoMapper arquivoTermoReferenciaToDtoMapper;

    @Autowired
    private ArquivoRequisicaoModificacaoFileService arquivoRequisicaoModificacaoFileService;

    @Autowired
    private ArquivoAditivoContratoToDtoMapper arquivoAditivoContratoToDtoMapper;

    @Autowired
    private ArquivoCaronaToDtoMapper arquivoCaronaToDtoMapper;

    @Autowired
    private ArquivoContratoToDtoMapper arquivoContratoToDtoMapper;

    @Autowired
    private ArquivoDispensaToDtoMapper arquivoDispensaToDtoMapper;

    @Autowired
    private ArquivoInexigibilidadeToDtoMapper arquivoInexigibilidadeToDtoMapper;

    @Autowired
    private ArquivoLicitacaoToDtoMapper arquivoLicitacaoToDtoMapper;

    @Autowired
    private ArquivoAditivoContratoRepository arquivoAditivoContratoRepository;

    @Autowired
    private ArquivoCaronaRepository arquivoCaronaRepository;

    @Autowired
    private ArquivoContratoRepository arquivoContratoRepository;

    @Autowired
    private ArquivoDispensaRepository arquivoDispensaRepository;

    @Autowired
    private ArquivoInexigibilidadeRepository arquivoInexigibilidadeRepository;

    @Autowired
    private ArquivoLicitacaoRepository arquivoLicitacaoRepository;

    @Autowired
    private ObraMedicaoService obraMedicaoService;

    @Autowired
    private ArquivoObraMedicaoRepository arquivoObraMedicaoRepository;

    @Autowired
    private ArquivoCredenciamentoRepository arquivoCredenciamentoRepository;

    @Autowired
    private ArquivoCredenciadoRepository arquivoCredenciadoRepository;

    @Autowired
    private ArquivoObraMedicaoToDtoMapper arquivoObraMedicaoToDtoMapper;

    @Autowired
    private ArquivoCredenciamentoToDtoMapper arquivoCredenciamentoToDtoMapper;

    @Autowired
    private ArquivoCredenciadoToDtoMapper arquivoCredenciadoToDtoMapper;

    @Autowired
    private ObraService obraService;

    @Inject
    private CredenciamentoService credenciamentoService;

    @Inject
    private CredenciadoService credenciadoService;

    @Autowired
    private AnulacaoRevogacaoService anulacaoRevogacaoService;

    @Autowired
    private ArquivoAnulacaoRevogacaoRepository arquivoAnulacaoRevogacaoRepository;

    @Autowired
    private ArquivoAnulacaoRevogacaoToDtoMapper arquivoAnulacaoRevogacaoToDtoMapper;

    @Override
    public RequisicaoModificacaoRepository getRepository() {
        return requisicaoModificacaoRepository;
    }

    @Override
    protected Specification<RequisicaoModificacao> getSpecification(RequisicaoModificacaoFiltroRequest filtro) {
        return null;
    }

    private List<String> getIgnoreEntidade() {
        ArrayList<String> result = new ArrayList<>();
        if (!userPermissionService.getAuthorities().contains(ADMIN_PERMISSION_NAME)) {
            result.add(ENTIDADE_FIELD);
        }
        return result;
    }

    @Override
    public void requisicaoModificacaoAditivoContrato(RequisicaoModificacaoAditivoContratoDTO dto) throws AppException {
        AditivoContrato aditivoContrato = aditivoContratoService.getById(dto.getIdAditivo());
        aditivoContratoService.validarArquivos(dto.getAditivoDTO().getArquivosAditivo());
        this.requisitarModificacaoGenerico(dto, aditivoContratoService, Objeto.ADITIVO, aditivoContrato, dto.getAditivoDTO().getAditivoContrato(), dto.getAditivoDTO().getArquivosAditivo(), this.getIgnoreEntidade());
    }

    @Override
    public void requisitarModificacaoCarona(RequisicaoModificacaoCaronaDTO dto) throws AppException {
        Carona caronaOriginal = caronaService.getById(dto.getIdCarona());
        caronaService.validarArquivos(dto.getCaronaDTO().getArquivosCarona(), dto.getCaronaDTO().getFiltros());
        List<String> attrToIgnore = this.getIgnoreEntidade();
        attrToIgnore.add(TERMO_REFERENCIA_FIELD);
        this.requisitarModificacaoGenerico(dto, caronaService, Objeto.CARONA, caronaOriginal, dto.getCaronaDTO().getCarona(), dto.getCaronaDTO().getArquivosCarona(), attrToIgnore);
        caronaRepository.updateLastModifiedTimestamp(caronaOriginal.getId());
    }

    @Override
    public void requisicaoModificacaoContrato(RequisicaoModificacaoContratoDTO dto) throws AppException {
        Contrato contrato = contratoService.getById(dto.getIdContrato());
        Contrato contratoModificado = dto.getContratoDTO().getContrato();
        contratoService.validarArquivos(dto.getContratoDTO().getArquivosContrato(), dto.getContratoDTO().getFiltros());
        List<String> attrToIgnore = this.getIgnoreEntidade();
        attrToIgnore.add(PROCESSO_FIELD);
        attrToIgnore.add(CONTRATO_LICITANTE_FIELD);
        this.requisitarModificacaoGenerico(dto, contratoService, Objeto.CONTRATO, contrato, contratoModificado, dto.getContratoDTO().getArquivosContrato(), attrToIgnore);
    }

    @Override
    public void requisicaoModificacaoDispensa(RequisicaoModificacaoDispensaDTO dto, String lei) throws AppException {
        Dispensa dispensa = dispensaService.getById(dto.getIdDispensa());
        dispensaService.validarArquivos(dto.getDispensaDTO().getArquivosDispensa(), lei);
        Dispensa dispensaModificada = dto.getDispensaDTO().getDispensa();
        List<String> attrToIgnore = this.getIgnoreEntidade();
        attrToIgnore.add(TERMO_REFERENCIA_FIELD);
        DispensaDTO dispensaDTO = dto.getDispensaDTO();
        if (dispensaDTO.getObra() != null) {
            Obra obra = dispensaModificada.getObra();
            Edificacao edificacao = Optional.ofNullable(obra.getEdificacao()).orElse(new Edificacao());
            edificacao.setGeometry(dispensaDTO.getObra().getTipoCamada(), dispensaDTO.getObra().getCoordenadas());
            edificacao.setObra(obra);
            obra.setEdificacao(edificacao);
            dispensaModificada.setObra(obra);
            obra.setDispensa(dispensaModificada);
        } else if (dispensaDTO.getEdificacao() != null) {
            Edificacao edificacao = dispensaDTO.getEdificacao();
            Obra obra = dispensaModificada.getObra();
            obra.setEdificacao(edificacao);
            edificacao.setObra(obra);
            dispensaModificada.setObra(obra);
            obra.setDispensa(dispensaModificada);
        }

        this.requisitarModificacaoGenerico(dto, dispensaService, Objeto.DISPENSA, dispensa, dispensaModificada, dto.getDispensaDTO().getArquivosDispensa(), attrToIgnore);
        dispensaRepository.updateLastModifiedTimestamp(dispensa.getId());
    }

    @Override
    public void requisitarModificacaoInexigibilidade(RequisicaoModificacaoInexigibilidadeDTO dto, String lei) throws AppException {
        Inexigibilidade inexigibilidade = inexigibilidadeService.getById(dto.getIdInexigibilidade());
        inexigibilidadeService.validarArquivos(dto.getInexigibilidadeDTO().getArquivosInexigibilidade(), lei);
        Inexigibilidade inexigibilidadeModificada = dto.getInexigibilidadeDTO().getInexigibilidade();
        List<String> attrToIgnore = this.getIgnoreEntidade();
        attrToIgnore.add(TERMO_REFERENCIA_FIELD);

        if (dto.getInexigibilidadeDTO().getObra() != null) {
            Obra obra = inexigibilidadeModificada.getObra();
            Edificacao edificacao = Optional.ofNullable(obra.getEdificacao()).orElse(new Edificacao());
            edificacao.setGeometry(dto.getInexigibilidadeDTO().getObra().getTipoCamada(), dto.getInexigibilidadeDTO().getObra().getCoordenadas());
            edificacao.setObra(obra);
            obra.setEdificacao(edificacao);
            inexigibilidadeModificada.setObra(obra);
            obra.setInexigibilidade(inexigibilidadeModificada);
        } else if (dto.getInexigibilidadeDTO().getEdificacao() != null) {
            Edificacao edificacao = dto.getInexigibilidadeDTO().getEdificacao();
            Obra obra = inexigibilidadeModificada.getObra();
            obra.setEdificacao(edificacao);
            edificacao.setObra(obra);
            inexigibilidadeModificada.setObra(obra);
            obra.setInexigibilidade(inexigibilidadeModificada);
        }

        this.requisitarModificacaoGenerico(dto, inexigibilidadeService, Objeto.INEXIGIBILIDADE, inexigibilidade, inexigibilidadeModificada, dto.getInexigibilidadeDTO().getArquivosInexigibilidade(), attrToIgnore);
        inexigibilidadeRepository.updateLastModifiedTimestamp(inexigibilidade.getId());
    }

    @Override
    public void requisitarModificacaoLicitacao(RequisicaoModificacaoLicitacaoDTO dto) throws AppException {
        Licitacao licitacaoOriginal = licitacaoService.getById(dto.getIdLicitacao());
        UtilsRequisicaoModificacao.validarAlteracaoFaseLicitacao(dto, licitacaoOriginal.getFase());
        Licitacao licitacaoModificada = !Objects.isNull(dto.getCadastroLicitacaoDTO()) ? dto.getCadastroLicitacaoDTO().getLicitacao() : null;
        List<ArquivoLicitacaoDTO> arquivos = dto.getFaseLicitacao() == FaseLicitacao.DIVULGACAO_PUBLICACAO_LICITACAO ? dto.getLicitacaoPublicacaoDTO().getArquivosLicitacao() : dto.getCadastroLicitacaoDTO().getArquivosLicitacao();

        arquivos.forEach(arq -> {
            if (arq.getFase() == null && arq.getIdArquivo() == null) {
                arq.setFase(dto.getFaseLicitacao());
            }
        });

        //Bloqueia os termos de referencia envolvidos na requisição de modificação até que o processo seja concluido
        TermoReferencia termoLicModificada = licitacaoModificada == null ? null : licitacaoModificada.getTermoReferencia();
        if (!Objects.isNull(termoLicModificada) && !Objects.isNull(licitacaoOriginal.getTermoReferencia()) && !licitacaoOriginal.getTermoReferencia().getId().equals(licitacaoModificada.getTermoReferencia().getId())) {
            List<Long> idsTermos = Arrays.asList(licitacaoOriginal.getTermoReferencia().getId(), termoLicModificada.getId());
            termoReferenciaService.changeBlockedTermo(idsTermos, true);
        }

        if (licitacaoModificada != null) {

            boolean isFinalizacao = dto.getFaseLicitacao().equals(FaseLicitacao.FINALIZACAO);

            boolean hasTermoReferenciaAndSrp = !Objects.isNull(licitacaoModificada.getTermoReferencia())
                    && licitacaoModificada.getTermoReferencia().getSrp();

            boolean containsNaturezaObras = licitacaoModificada.getNaturezasDoObjeto().stream()
                    .anyMatch(natureza -> natureza.toString().equals("OBRAS"));

            licitacaoService.validarArquivos(
                    arquivos,
                    dto.getFaseLicitacao(),
                    licitacaoOriginal.getLei(),
                    isFinalizacao && hasTermoReferenciaAndSrp,
                    containsNaturezaObras
            );
        }

        // Tratamento especifico de obra para Licitação na fase preparatoria
        if (dto.getFaseLicitacao() == FaseLicitacao.PREPARATORIA && licitacaoModificada != null) {
            CadastroLicitacaoDTO cadastroDTO = dto.getCadastroLicitacaoDTO();
            if (cadastroDTO.getObra() != null) {
                Obra obra = licitacaoModificada.getObra();
                Edificacao edificacao = Optional.ofNullable(obra.getEdificacao()).orElse(new Edificacao());
                edificacao.setGeometry(cadastroDTO.getObra().getTipoCamada(), cadastroDTO.getObra().getCoordenadas());
                edificacao.setObra(obra);
                obra.setEdificacao(edificacao);
                licitacaoModificada.setObra(obra);
                obra.setLicitacao(licitacaoModificada);
            } else if (cadastroDTO.getEdificacao() != null) {
                Edificacao edificacao = cadastroDTO.getEdificacao();
                Obra obra = licitacaoModificada.getObra();
                obra.setEdificacao(edificacao);
                edificacao.setObra(obra);
                licitacaoModificada.setObra(obra);
                obra.setLicitacao(licitacaoModificada);
            }
        } else if (dto.getFaseLicitacao() == FaseLicitacao.DIVULGACAO_PUBLICACAO_LICITACAO) {
            try {
                licitacaoModificada = (Licitacao) UtilsRequisicaoModificacao.cloneObject(licitacaoOriginal, Licitacao.class);
                licitacaoModificada.setPublicacoes(dto.getLicitacaoPublicacaoDTO().getPublicacoesLicitacao());
            } catch (JsonProcessingException e) {
                throw new AppException("Ocorreu um erro ao tentar registrar a requisição de modificação para a licitação", HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }

        List<String> attrToIgnore = this.getIgnoreEntidade();
        this.requisitarModificacaoGenerico(dto, licitacaoService, Objeto.LICITACAO, licitacaoOriginal, licitacaoModificada, arquivos, attrToIgnore);
    }

    @Override
    public void requisitarReaberturaLicitacao(RequisicaoReaberturaLicitacaoDTO dto) throws AppException {
        Licitacao licitacaoOriginal = licitacaoService.getById(dto.getIdLicitacao());
        List<? extends  ArquivoUploadDTO> arquivos = dto.getOcorrenciaLicitacaoDTO().getArquivos();
        this.requisitarReabertura(dto, licitacaoService, Objeto.LICITACAO, licitacaoOriginal, arquivos);
    }

    @Override
    public void requisitarModificacaoObraMedicao(RequisicaoModificacaoObraMedicaoDTO dto) throws AppException {
        Long idObra = dto.getObraMedicaoDTO().getObraMedicao().getObra().getId();
        if (!obraMedicaoService.listarMedicoesObra(idObra).stream().anyMatch((med) -> !Objects.isNull(med.getObraMedicao().getIdRequisicaoModificacao()))) {
            ObraMedicao obraMedicaoOriginal = obraMedicaoService.getById(dto.getIdObraMedicao());
            obraMedicaoService.validarArquivos(dto.getObraMedicaoDTO().getArquivosObraMedicao());
            List<String> attrToIgnore = new ArrayList<>();
            attrToIgnore.addAll(Arrays.asList(OBRA_FIELD, "usuario"));
            this.requisitarModificacaoGenerico(dto, obraMedicaoService, Objeto.OBRA_MEDICAO, obraMedicaoOriginal, dto.getObraMedicaoDTO().getObraMedicao(), dto.getObraMedicaoDTO().getArquivosObraMedicao(), attrToIgnore);
        } else {
            throw new AppException("Requisição de modificação não pode ser criada pois já existe uma requisição de modificação em aberto para alguma medição desta obra", HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    public void requisicaoModificacaoRescisaoContratual(RequisicaoModificacaoContratoDTO dto) throws AppException {
        Contrato contrato = contratoService.getById(dto.getIdContrato());
        List<String> attrToIgnore = this.getIgnoreEntidade();
        attrToIgnore.add(PROCESSO_FIELD);
        attrToIgnore.add(CONTRATO_LICITANTE_FIELD);
        this.requisitarModificacaoGenerico(dto, contratoService, Objeto.RESCISAO_CONTRATUAL, contrato, dto.getContratoDTO().getContrato(), dto.getContratoDTO().getArquivosContrato(), attrToIgnore);
    }

    @Override
    public void requisicaoModificacaoAnulacaoRevogacao(RequisicaoModificacaoAnulacaoRevogacaoDTO dto) throws AppException {
        AnulacaoRevogacao anulacaoRevogacao = anulacaoRevogacaoService.getById(dto.getId());
        List<String> attrToIgnore = new ArrayList<>();
        this.requisitarModificacaoGenerico(dto, anulacaoRevogacaoService, Objeto.ANULACAO_REVOGACAO, anulacaoRevogacao, dto.getAnulacaoRevogacaoDTO().getAnulacaoRevogacao(), dto.getAnulacaoRevogacaoDTO().getArquivos(), attrToIgnore);

    }

    @Override
    public void requisitarModificacaoTermoReferencia(RequisicaoModificacaoTermoReferenciaDTO dto) throws AppException {
        TermoReferencia termoReferencia = termoReferenciaService.getById(dto.getIdTermoReferencia());
        if (!termoReferencia.getCanCreateReqModificacao()) {
            throw new AppException("Este termo de referência não pode ser editado via requisição de modificação", HttpStatus.BAD_REQUEST);
        }

        List<ArquivoTermoReferenciaDTO> arquivosOriginais = this.arquivoTermoReferenciaRepository.buscarPor(dto.getIdTermoReferencia()).stream().map(arquivo -> arquivoTermoReferenciaToDtoMapper.map(arquivo)).collect(Collectors.toList());
        termoReferenciaService.validarArquivos(dto.getTermoReferencia().getArquivosTemporarios(), dto.getTermoReferencia());
        arquivoRequisicaoModificacaoFileService.moveTempFilesToReqModDirectory(dto.getTermoReferencia().getArquivosTemporarios());
        termoReferencia.setArquivosTemporarios(arquivosOriginais);
        List<String> attrToIgnore = this.getIgnoreEntidade();
        attrToIgnore.add("secoesCriadas");
        attrToIgnore.add("secoes");
        this.requisitarModificacaoGenerico(dto, termoReferenciaService, Objeto.TERMO_REFERENCIA, termoReferencia, dto.getTermoReferencia(), null, attrToIgnore);
    }

    private RequisicaoModificacao buildRequisicaoModificacao(RequisicaoModificacaoDTO dto, Usuario currentUser, AbstractIdentificavel processoOriginal, Entidade entidade, Objeto tipoProcesso, String tituloProcesso, FaseLicitacao faseLicitacao) {
        RequisicaoModificacao requisicaoModificacao = new RequisicaoModificacao();
        requisicaoModificacao.setIdProcesso(processoOriginal.getId());
        requisicaoModificacao.setJustificativaJurisdicionado(dto.getJustificativaJurisdicionado());
        requisicaoModificacao.setUsuario(currentUser);
        requisicaoModificacao.setEntidade(entidade);
        requisicaoModificacao.setStatus(StatusReqModificacao.ENVIADA);
        requisicaoModificacao.setTipo(TipoModificacao.EDICAO);
        requisicaoModificacao.setTipoProcesso(tipoProcesso);
        requisicaoModificacao.setTituloProcesso(tituloProcesso);
        requisicaoModificacao.setDataRequisicao(LocalDateTime.now());
        requisicaoModificacao.setFaseLicitacao(faseLicitacao);

        return requisicaoModificacao;
    }

    private void requisitarModificacaoGenerico(RequisicaoModificacaoDTO dto, IService service, Objeto tipoProcesso, AbstractRequisicaoModificacaoIdentificavel processoOriginal, AbstractRequisicaoModificacaoIdentificavel novoProcesso, List<? extends ArquivoUploadDTO> arquivos, List<String> attributesToIgnore) throws AppException {
        Long idEntidade = dto.getIdEntidade();
        if (!entidadeService.getContextEntitiesIds().contains(idEntidade)) {
            throw new AppException("Operação não autorizada. Você não tem acesso a esta entidade.", HttpStatus.FORBIDDEN);
        }

        Usuario usuario = usuarioService.getById(Long.parseLong(ThreadContext.get(USER_ID_THREAD_KEY)));
        Entidade entidade = entidadeService.getById(idEntidade);

        service.checarSeJahExiste(novoProcesso);

        FaseLicitacao faseLicitacao = null;
        if (RequisicaoModificacaoLicitacaoDTO.class.isAssignableFrom(dto.getClass())) {
            faseLicitacao = ((RequisicaoModificacaoLicitacaoDTO) dto).getFaseLicitacao();
        }

        List<? extends ArquivoUploadDTO> arquivosOriginais = null;
        if (!tipoProcesso.equals(Objeto.TERMO_REFERENCIA)) {
            if (AbstractUploadService.class.isAssignableFrom(service.getClass())) {
                arquivosOriginais = (List<? extends ArquivoUploadDTO>) ((AbstractUploadService<?, ?, ?, ?>) service).recuperarArquivos(processoOriginal.getId());
            } else if (AbstractUploadTipoService.class.isAssignableFrom(service.getClass())) {
                arquivosOriginais = ((AbstractUploadTipoService<?, ?, ?, ?, ?>) service).recuperarArquivos(processoOriginal.getId());
            }
        }

        RequisicaoModificacao requisicaoModificacao = buildRequisicaoModificacao(dto, usuario, processoOriginal, entidade, tipoProcesso, processoOriginal.titulo(), faseLicitacao);
        try {
            JSONObject reqModificacaoObject = this.getJSONObjectRequisicaoModificacao(processoOriginal, novoProcesso, arquivos, arquivosOriginais, attributesToIgnore);
            requisicaoModificacaoRepository.save(requisicaoModificacao);
            processoOriginal.setIdRequisicaoModificacao(requisicaoModificacao.getId());
            service.save(processoOriginal);
            arquivoRequisicaoModificacaoFileService.moveTempFilesToReqModDirectory(arquivos);
            saveArquivoRequisicaoModificacao(requisicaoModificacao, reqModificacaoObject);
        } catch (IOException e) {
            throw new AppException(String.format("Houve algum erro ao tentar registrar a Requisição de Modificação: %s", e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private void requisitarReabertura(RequisicaoReaberturaLicitacaoDTO dto, IService service, Objeto tipoProcesso, AbstractRequisicaoModificacaoIdentificavel processoOriginal, List<? extends ArquivoUploadDTO> arquivos) throws AppException {
        Long idEntidade = dto.getIdEntidade();
        if (!entidadeService.getContextEntitiesIds().contains(idEntidade)) {
            throw new AppException("Operação não autorizada. Você não tem acesso a esta entidade.", HttpStatus.FORBIDDEN);
        }

        Usuario usuario = usuarioService.getById(Long.parseLong(ThreadContext.get(USER_ID_THREAD_KEY)));
        Entidade entidade = entidadeService.getById(idEntidade);
        Licitacao licitacao = licitacaoService.getById(dto.getIdLicitacao());

        FaseLicitacao faseLicitacao = licitacao.getFase();

        RequisicaoModificacao requisicaoModificacao = new RequisicaoModificacao();
        requisicaoModificacao.setIdProcesso(processoOriginal.getId());
        requisicaoModificacao.setJustificativaJurisdicionado(dto.getJustificativaJurisdicionado());
        requisicaoModificacao.setUsuario(usuario);
        requisicaoModificacao.setEntidade(entidade);
        requisicaoModificacao.setStatus(StatusReqModificacao.ENVIADA);
        requisicaoModificacao.setTipo(TipoModificacao.REABERTURA);
        requisicaoModificacao.setTipoProcesso(tipoProcesso);
        requisicaoModificacao.setTituloProcesso(processoOriginal.titulo());
        requisicaoModificacao.setDataRequisicao(LocalDateTime.now());
        requisicaoModificacao.setFaseLicitacao(faseLicitacao);

        try {
            JSONObject reqModificacaoObject = getJSONObjectRequisicaoReabertura(processoOriginal, dto, arquivos);
            requisicaoModificacaoRepository.save(requisicaoModificacao);
            processoOriginal.setIdRequisicaoModificacao(requisicaoModificacao.getId());
            service.save(processoOriginal);
            arquivoRequisicaoModificacaoFileService.moveTempFilesToReqModDirectory(arquivos);
            saveArquivoRequisicaoModificacao(requisicaoModificacao, reqModificacaoObject);
        } catch (IOException e) {
            throw new AppException(String.format("Houve algum erro ao tentar registrar a Requisição de Modificação: %s", e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private void setBigDecimalsToThreeDecimalPlatesAndOrderLists(Map<String, Object> entityMap) {
        entityMap.entrySet().forEach((entry) -> {
            Object value = entry.getValue();
            if (Objects.nonNull(value)) {
                if (BigDecimal.class.isAssignableFrom(value.getClass())) {
                    entry.setValue(((BigDecimal) value).setScale(3, RoundingMode.CEILING));
                } else if (Map.class.isAssignableFrom(value.getClass())) {
                    this.setBigDecimalsToThreeDecimalPlatesAndOrderLists((Map<String, Object>) value);
                } else if (List.class.isAssignableFrom(value.getClass()) && !((List<?>) value).isEmpty()) {
                    if (BigDecimal.class.isAssignableFrom(((List<?>) value).get(0).getClass())) {
                        entry.setValue(((List<?>) value).stream().map(listValue ->
                                ((BigDecimal) listValue).setScale(3, RoundingMode.CEILING)
                        ).collect(Collectors.toList()));
                    } else if (Map.class.isAssignableFrom(((List<?>) value).get(0).getClass())) {
                        ((List<?>) value).sort(Comparator.comparing((map) -> Optional.ofNullable((Long) ((Map<?, ?>) map).get("id")).orElse(0L)));
                        ((List<?>) entry.getValue()).forEach((listValue) -> {
                            this.setBigDecimalsToThreeDecimalPlatesAndOrderLists((Map<String, Object>) listValue);
                        });
                    }
                }
            }
        });
    }

    private JSONObject getJSONObjectRequisicaoReabertura(AbstractIdentificavel entity, RequisicaoReaberturaLicitacaoDTO dto, List<? extends ArquivoUploadDTO> files) throws JsonProcessingException {
        JsonMapper jsonMapper = UtilsRequisicaoModificacao.getJsonMapper();

        Map<String, Object> entityJson = jsonMapper.convertValue(entity, Map.class);
        Map<String, Object> occurrence = jsonMapper.convertValue(dto, Map.class);

        JSONObject json = new JSONObject();
        json.put("entity", new JSONObject(jsonMapper.writeValueAsString(entityJson)));
        json.put("occurrence", new JSONObject(jsonMapper.writeValueAsString(occurrence)));
        if (files != null) {
            json.put(FILES_FIELD, new JSONArray(jsonMapper.writeValueAsString(files)));
        }
        return json;
    }

    private JSONObject getJSONObjectRequisicaoModificacao(AbstractIdentificavel oldEntity, AbstractIdentificavel newEntity, List<? extends ArquivoUploadDTO> files, List<? extends ArquivoUploadDTO> arquivosOriginais, List<String> attributesToIgnore) throws JsonProcessingException {
        JsonMapper jsonMapper = UtilsRequisicaoModificacao.getJsonMapper();
        attributesToIgnore.add(ID_REQUISICAO_MODIFICACAO_FIELD);

        Map<String, Object> oldEntityMap = jsonMapper.convertValue(oldEntity, Map.class);
        Map<String, Object> newEntityMap = jsonMapper.convertValue(newEntity, Map.class);
        for (String attr : attributesToIgnore) {
            oldEntityMap.put(attr, null);
            newEntityMap.put(attr, null);
        }

        this.setBigDecimalsToThreeDecimalPlatesAndOrderLists(oldEntityMap);
        this.setBigDecimalsToThreeDecimalPlatesAndOrderLists(newEntityMap);

        MapDifference<String, Object> differences = Maps.difference(oldEntityMap, newEntityMap);

        if (differences.areEqual() && UtilsRequisicaoModificacao.equalsArquivos(files, arquivosOriginais)) {
            throw new AppException("Não houve modificação efetiva sendo solicitada.", HttpStatus.UNPROCESSABLE_ENTITY);
        }

        JSONObject differencesObject = new JSONObject();
        differences.entriesDiffering().entrySet().forEach(diff -> {
            JSONObject diffObject = new JSONObject();
            diffObject.put(OLD_VALUE_FIELD_NAME, diff.getValue().leftValue());
            diffObject.put(NEW_VALUE_FIELD_NAME, diff.getValue().rightValue());
            differencesObject.put(diff.getKey(), diffObject);
        });

        JSONObject json = new JSONObject();
        json.put(DIFFERENCES_FIELD, differencesObject);
        if (files != null) {
            json.put(FILES_FIELD, new JSONArray(jsonMapper.writeValueAsString(files)));
        }

        return json;
    }

    private void saveArquivoRequisicaoModificacao(RequisicaoModificacao reqMod, JSONObject differencesObject) throws IOException {
        arquivoRequisicaoModificacaoFileService.createAndSaveNewJSONFile(differencesObject.toString(), String.format("RequisicaoModificacaoId_%d.json", reqMod.getId()), reqMod);
    }

    private RequisicaoModificacao requisicaoRemocao(RequisicaoRemocaoDTO requisicaoRemocaoDTO, Objeto tipoProcesso, String tituloProcesso) throws AppException {
        Long idEntidade = requisicaoRemocaoDTO.getIdEntidade();
        Long idProcesso = requisicaoRemocaoDTO.getIdProcesso();
        String justificativa = requisicaoRemocaoDTO.getJustificativaJurisdicionado();

        if (!entidadeService.getContextEntitiesIds().contains(idEntidade)) {
            throw new AppException("Operação não autorizada. Você não tem acesso a esta entidade.", HttpStatus.FORBIDDEN);
        }

        Usuario usuario = usuarioService.getById(Long.parseLong(ThreadContext.get(USER_ID_THREAD_KEY)));
        Entidade entidade = entidadeService.getById(idEntidade);

        RequisicaoModificacao requisicaoModificacao = new RequisicaoModificacao();
        requisicaoModificacao.setUsuario(usuario);
        requisicaoModificacao.setEntidade(entidade);
        requisicaoModificacao.setTituloProcesso(tituloProcesso);
        requisicaoModificacao.setIdProcesso(idProcesso);
        requisicaoModificacao.setTipoProcesso(tipoProcesso);
        requisicaoModificacao.setJustificativaJurisdicionado(justificativa);
        requisicaoModificacao.setDataRequisicao(LocalDateTime.now());
        requisicaoModificacao.setStatus(StatusReqModificacao.ENVIADA);
        requisicaoModificacao.setTipo(TipoModificacao.REMOCAO);

        return this.save(requisicaoModificacao);
    }

    @Override
    public void requisicaoRemocaoAditivoContrato(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        AditivoContrato aditivoContrato = aditivoContratoService.getById(requisicaoRemocaoDTO.getIdProcesso());
        RequisicaoModificacao requisicaoModificacao = requisicaoRemocao(requisicaoRemocaoDTO, Objeto.ADITIVO, aditivoContrato.toString());
        aditivoContrato.setIdRequisicaoModificacao(requisicaoModificacao.getId());
        aditivoContratoService.save(aditivoContrato);
    }

    @Override
    public void requisicaoRemocaoCarona(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        Carona carona = caronaService.getById(requisicaoRemocaoDTO.getIdProcesso());
        RequisicaoModificacao requisicaoModificacao = requisicaoRemocao(requisicaoRemocaoDTO, Objeto.CARONA, carona.titulo());
        carona.setIdRequisicaoModificacao(requisicaoModificacao.getId());
        caronaService.save(carona);
    }

    @Override
    public void requisicaoRemocaoContrato(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        Contrato contrato = contratoService.getById(requisicaoRemocaoDTO.getIdProcesso());
        RequisicaoModificacao requisicaoModificacao = requisicaoRemocao(requisicaoRemocaoDTO, Objeto.CONTRATO, contrato.toString());
        contrato.setIdRequisicaoModificacao(requisicaoModificacao.getId());
        contratoService.save(contrato);
    }

    @Override
    public void requisicaoRemocaoDispensa(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        Dispensa dispensa = dispensaService.getById(requisicaoRemocaoDTO.getIdProcesso());
        RequisicaoModificacao requisicaoModificacao = requisicaoRemocao(requisicaoRemocaoDTO, Objeto.DISPENSA, dispensa.titulo());
        dispensa.setIdRequisicaoModificacao(requisicaoModificacao.getId());
        dispensaService.save(dispensa);
        dispensaRepository.updateLastModifiedTimestamp(dispensa.getId());
    }

    @Override
    public void requisicaoRemocaoInexigibilidade(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        Inexigibilidade inexigibilidade = inexigibilidadeService.getById(requisicaoRemocaoDTO.getIdProcesso());
        RequisicaoModificacao requisicaoModificacao = requisicaoRemocao(requisicaoRemocaoDTO, Objeto.INEXIGIBILIDADE, inexigibilidade.titulo());
        inexigibilidade.setIdRequisicaoModificacao(requisicaoModificacao.getId());
        inexigibilidadeService.save(inexigibilidade);
        inexigibilidadeRepository.updateLastModifiedTimestamp(inexigibilidade.getId());
    }

    @Override
    public void requisicaoRemocaoLicitacao(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        Licitacao licitacao = licitacaoService.getById(requisicaoRemocaoDTO.getIdProcesso());
        RequisicaoModificacao requisicaoModificacao = requisicaoRemocao(requisicaoRemocaoDTO, Objeto.LICITACAO, licitacao.getCabecalho().toUpperCase());
        licitacao.setIdRequisicaoModificacao(requisicaoModificacao.getId());
        licitacaoService.save(licitacao);
    }

    @Override
    public void requisitarModificacaoCredenciamento(RequisicaoModificacaoCredenciamentoDTO dto) throws AppException {
        Credenciamento credenciamento = credenciamentoService.getById(dto.getIdCredenciamento());
        credenciamentoService.validarArquivos(dto.getCredenciamentoDTO().getArquivos());
        Credenciamento credenciamentoModificado = dto.getCredenciamentoDTO().getCredenciamento();
        List<String> attrToIgnore = this.getIgnoreEntidade();
        if (!credenciadoService.getAllByCredenciamento(credenciamento.getId()).isEmpty()) {
            attrToIgnore.add(TERMO_REFERENCIA_FIELD);
        }

        if (dto.getCredenciamentoDTO().getObra() != null) {
            Obra obra = credenciamentoModificado.getObra();
            Edificacao edificacao = Optional.ofNullable(obra.getEdificacao()).orElse(new Edificacao());
            edificacao.setGeometry(dto.getCredenciamentoDTO().getObra().getTipoCamada(), dto.getCredenciamentoDTO().getObra().getCoordenadas());
            edificacao.setObra(obra);
            obra.setEdificacao(edificacao);
            credenciamentoModificado.setObra(obra);
            obra.setCredenciamento(credenciamentoModificado);
        } else if (dto.getCredenciamentoDTO().getEdificacao() != null) {
            Edificacao edificacao = dto.getCredenciamentoDTO().getEdificacao();
            Obra obra = credenciamentoModificado.getObra();
            obra.setEdificacao(edificacao);
            edificacao.setObra(obra);
            credenciamentoModificado.setObra(obra);
            obra.setCredenciamento(credenciamentoModificado);
        }

        this.requisitarModificacaoGenerico(dto, credenciamentoService, Objeto.CREDENCIAMENTO, credenciamento, credenciamentoModificado, dto.getCredenciamentoDTO().getArquivos(), attrToIgnore);
        credenciamentoRepository.updateLastModifiedTimestamp(credenciamento.getId());
    }

    @Override
    public void requisicaoRemocaoCredenciamento(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        Credenciamento credenciamento = credenciamentoService.getById(requisicaoRemocaoDTO.getIdProcesso());
        RequisicaoModificacao requisicaoModificacao = requisicaoRemocao(requisicaoRemocaoDTO, Objeto.CREDENCIAMENTO, credenciamento.titulo());
        credenciamento.setIdRequisicaoModificacao(requisicaoModificacao.getId());
        credenciamentoService.save(credenciamento);
        credenciamentoRepository.updateLastModifiedTimestamp(credenciamento.getId());
    }

    @Override
    public void requisicaoRemocaoCredenciado(RequisicaoRemocaoDTO requisicaoRemocaoDTO) throws AppException {
        Credenciado credenciado = credenciadoService.getById(requisicaoRemocaoDTO.getIdProcesso());
        RequisicaoModificacao requisicaoModificacao = requisicaoRemocao(requisicaoRemocaoDTO, Objeto.CREDENCIADO, credenciado.titulo());
        credenciado.setIdRequisicaoModificacao(requisicaoModificacao.getId());
        credenciadoService.save(credenciado);
        credenciamentoRepository.updateLastModifiedTimestamp(credenciado.getCredenciamento().getId());
    }

    @Override
    public void julgamentoAuditorRequisicao(RequisicaoModificacaoJulgamentoAuditorDTO dto) throws AppException {
        try {
            RequisicaoModificacao requisicaoModificacao = this.getById(dto.getIdRequisicaoModificacao());
            StatusReqModificacao statusReqModificacao = null;

            for (StatusReqModificacao s : StatusReqModificacao.values()) {
                if (s.getValor().equalsIgnoreCase(dto.getParecer())) {
                    statusReqModificacao = s;
                    break;
                }
            }

            if (requisicaoModificacao != null && statusReqModificacao != null) {
                requisicaoModificacao.setJustificativaAuditor(dto.getJustificativaAuditor());
                requisicaoModificacao.setStatus(statusReqModificacao);
                requisicaoModificacao.setDataJulgamento(LocalDateTime.now());
                this.save(requisicaoModificacao);
                this.resetRequisicaoModificacaoProcesso(requisicaoModificacao);
            }
        } catch (IOException e) {
            throw new AppException("Houve algum erro ao tentar registrar o Julgamento do Auditor", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private AbstractRequisicaoModificacaoIdentificavel applyJulgamentoGenerico(RequisicaoModificacao requisicaoModificacao, AbstractRequisicaoModificacaoIdentificavel processo, JSONObject differences) throws JsonProcessingException {
        processo.setIdRequisicaoModificacao(null);
        if (requisicaoModificacao.getStatus().equals(StatusReqModificacao.ACEITA)) {
            if (requisicaoModificacao.getTipo().equals(TipoModificacao.REMOCAO)) {
                if (LogicallyRemovable.class.isAssignableFrom(processo.getClass())) {
                    ((LogicallyRemovable) processo).setStatus(StatusLicitacao.REMOVIDA);
                    if (processo instanceof Carona) {
                        caronaService.finalizarTermoCarona(processo.getId());
                    }
                    if (processo instanceof AditivoContrato) {
                        aditivoContratoService.afterModificarListaAditivos(((AditivoContrato) processo));
                    }
                } else {
                    throw new AppException(String.format("Remoção lógica não disponível para esta entidade: %s", processo.getClass().getName()), HttpStatus.UNPROCESSABLE_ENTITY);
                }
            } else if (requisicaoModificacao.getTipo().equals(TipoModificacao.EDICAO) || requisicaoModificacao.getTipo().equals(TipoModificacao.REABERTURA)) {
            processo = (AbstractRequisicaoModificacaoIdentificavel) aplicarModificacoes(differences, processo);
            }
        }
        if (processo instanceof Carona) {
            caronaRepository.updateLastModifiedTimestamp(processo.getId());
        }

        return processo;
    }

    private void resetRequisicaoModificacaoProcesso(RequisicaoModificacao requisicaoModificacao) throws AppException, IOException {
        TipoModificacao tipoModificacao = requisicaoModificacao.getTipo();
        Objeto tipoProcesso = requisicaoModificacao.getTipoProcesso();
        Long idProcesso = requisicaoModificacao.getIdProcesso();
        String fileContent = arquivoRequisicaoModificacaoFileService.getFileContentByIdRequisicaoModificacao(requisicaoModificacao.getId());
        JSONObject reqModificacaoFile = new JSONObject(fileContent);

        JSONObject differencesObject = reqModificacaoFile.has(DIFFERENCES_FIELD) ? reqModificacaoFile.getJSONObject(DIFFERENCES_FIELD) : null;
        if (tipoModificacao.equals(TipoModificacao.REABERTURA)) {
            this.applyRequisicaoReaberturaLicitacao(requisicaoModificacao, reqModificacaoFile);
        } else
        if (tipoProcesso.equals(Objeto.LICITACAO)) {
            this.applyRequisicaoModificacaoLicitacao(requisicaoModificacao, differencesObject, reqModificacaoFile);
        } else if (tipoProcesso.equals(Objeto.TERMO_REFERENCIA)) {
            this.applyRequisicaoModificacaoTermoReferencia(requisicaoModificacao, differencesObject);
        } else if (tipoProcesso.equals(Objeto.OBRA_MEDICAO)) {
            this.applyRequisicaoModificacaoObraMedicao(requisicaoModificacao, differencesObject, reqModificacaoFile);
        } else if (tipoProcesso.equals(Objeto.CREDENCIAMENTO)) {
            this.applyRequisicaoModificacaoCredenciamento(requisicaoModificacao, differencesObject, reqModificacaoFile);
        } else if (tipoProcesso.equals(Objeto.DISPENSA)) {
            this.applyRequisicaoModificaoDispensa(requisicaoModificacao, differencesObject, reqModificacaoFile);
        } else if (tipoProcesso.equals(Objeto.INEXIGIBILIDADE)) {
            this.applyRequisicaoModificacaoInexigibilidade(requisicaoModificacao, differencesObject, reqModificacaoFile);
        } else if (tipoProcesso.equals(Objeto.ANULACAO_REVOGACAO)) {
            this.applyRequisicaoModificacaoAnulacaoRevogacao(requisicaoModificacao, differencesObject, reqModificacaoFile);
        } else {
            AbstractUploadService<AbstractRequisicaoModificacaoIdentificavel, AbstractFiltroRequest, ArquivoMappedSuperclassDTO, ArquivoIdentificavel> service = this.getServiceByTipoProcesso(requisicaoModificacao.getTipoProcesso());
            AbstractRequisicaoModificacaoIdentificavel processo = service.getById(idProcesso);
            processo = this.applyJulgamentoGenerico(requisicaoModificacao, processo, differencesObject);
            service.save(processo);
            if (requisicaoModificacao.getStatus().equals(StatusReqModificacao.ACEITA) && requisicaoModificacao.getTipo().equals(TipoModificacao.EDICAO) && reqModificacaoFile.has(FILES_FIELD)) {
                aplicarModificacoesArquivos(reqModificacaoFile.getJSONArray(FILES_FIELD), processo, service, requisicaoModificacao.getTipoProcesso().getTipoArquivoDTO());
            }
        }
    }

    private void applyRequisicaoModificacaoAnulacaoRevogacao(RequisicaoModificacao requisicaoModificacao, JSONObject differencesObject, JSONObject reqModificacaoFile) throws JsonProcessingException {
        AnulacaoRevogacao anulacaoRevogacao = this.anulacaoRevogacaoService.getById(requisicaoModificacao.getIdProcesso());
        anulacaoRevogacao = (AnulacaoRevogacao) this.applyJulgamentoGenerico(requisicaoModificacao, anulacaoRevogacao, differencesObject);

        this.anulacaoRevogacaoService.save(anulacaoRevogacao);
        if (requisicaoModificacao.getStatus().equals(StatusReqModificacao.ACEITA) && requisicaoModificacao.getTipo().equals(TipoModificacao.EDICAO) && reqModificacaoFile.has(FILES_FIELD)) {
            aplicarModificacoesArquivos(reqModificacaoFile.getJSONArray(FILES_FIELD), anulacaoRevogacao, anulacaoRevogacaoService, ArquivoAnulacaoRevogacaoDTO.class);
        }
    }

    private void applyRequisicaoReaberturaLicitacao(RequisicaoModificacao requisicaoModificacao, JSONObject reqModificacaoFile) throws AppException {
        Licitacao licitacao = this.licitacaoService.getById(requisicaoModificacao.getIdProcesso());
        licitacao.setIdRequisicaoModificacao(null);

        if (requisicaoModificacao.getStatus().equals(StatusReqModificacao.ACEITA)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
            JSONObject ocorrenciaJson = reqModificacaoFile.getJSONObject("occurrence").getJSONObject("ocorrenciaLicitacaoDTO");

            OcorrenciaLicitacaoDTO ocorrenciaLicitacaoDTO = new OcorrenciaLicitacaoDTO();
            ocorrenciaLicitacaoDTO.setIdLicitacao(requisicaoModificacao.getIdProcesso());
            ocorrenciaLicitacaoDTO.setMotivoOcorrencia(ocorrenciaJson.getString("motivoOcorrencia"));
            ocorrenciaLicitacaoDTO.setTipoOcorrencia(StatusOcorrenciaLicitacao.valueOf(ocorrenciaJson.getString("tipoOcorrencia")));
            ocorrenciaLicitacaoDTO.setDataAviso(LocalDateTime.parse(ocorrenciaJson.getString("dataAviso"), formatter));
            ocorrenciaLicitacaoDTO.setDataProrrogada(LocalDateTime.parse(ocorrenciaJson.getString("dataProrrogada"), formatter));

            JsonMapper jm = UtilsRequisicaoModificacao.getJsonMapper();
            List<ArquivoOcorrenciaLicitacaoDTO> arquivos = reqModificacaoFile.getJSONArray(FILES_FIELD).toList().stream().map(arq -> {
                try {
                    return jm.readValue(jm.writeValueAsString(arq), ArquivoOcorrenciaLicitacaoDTO.class);
                } catch (JsonProcessingException e) {
                    throw new AppException("Falha ao aplicar alterações dos arquivos", HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }).collect(Collectors.toList());

            arquivoRequisicaoModificacaoFileService.returnTempFilesFromReqModDirectory(arquivos);
            ocorrenciaLicitacaoDTO.setArquivos(arquivos);
            ocorrenciaLicitacaoService.reabrir(ocorrenciaLicitacaoDTO);
        }
    }

    private void applyRequisicaoModificacaoLicitacao(RequisicaoModificacao requisicaoModificacao, JSONObject differencesObject, JSONObject reqModificacaoFile) throws JsonProcessingException {
        Licitacao licitacao = this.licitacaoService.getById(requisicaoModificacao.getIdProcesso());
        licitacao = (Licitacao) this.applyJulgamentoGenerico(requisicaoModificacao, licitacao, differencesObject);
        if (!Objects.isNull(differencesObject) && (differencesObject.has(TERMO_REFERENCIA_FIELD))) {
            JSONObject termosReferencia = differencesObject.getJSONObject(TERMO_REFERENCIA_FIELD);
            List<Long> idsTermos = new ArrayList<>();
            if (termosReferencia.has("oldValue") && !Objects.isNull(termosReferencia.getJSONObject("oldValue"))) {
                idsTermos.add(termosReferencia.getJSONObject("oldValue").getLong("id"));
            }
            if (termosReferencia.has("newValue") && !Objects.isNull(termosReferencia.getJSONObject("newValue"))) {
                idsTermos.add(termosReferencia.getJSONObject("newValue").getLong("id"));
            }

            termoReferenciaService.changeBlockedTermo(idsTermos, false);
        }

            if (licitacao.getObra() != null && licitacao.getObra().getEdificacao() != null) {
                licitacao.getObra().getEdificacao().setObra(licitacao.getObra());
            }

            this.licitacaoService.save(licitacao, requisicaoModificacao.getFaseLicitacao(), requisicaoModificacao.getUsuario());
            if (requisicaoModificacao.getStatus().equals(StatusReqModificacao.ACEITA) && requisicaoModificacao.getTipo().equals(TipoModificacao.REMOCAO)) {
                TermoReferencia termoReferencia = licitacao.getTermoReferencia();
                termoReferencia.setIsFinalizado(false);
            }
            if (requisicaoModificacao.getStatus().equals(StatusReqModificacao.ACEITA) && requisicaoModificacao.getTipo().equals(TipoModificacao.EDICAO) && reqModificacaoFile.has(FILES_FIELD)) {
                if (differencesObject.has(TERMO_REFERENCIA_FIELD)) {
                    JSONObject termoReferencia = differencesObject.getJSONObject(TERMO_REFERENCIA_FIELD);
                    if (termoReferencia.has("oldValue") && !Objects.isNull(termoReferencia.getJSONObject("oldValue"))) {
                        Long oldTermo = termoReferencia.getJSONObject("oldValue").getLong("id");
                        termoReferenciaService.changeFinalizadoTermo(Arrays.asList(oldTermo), false);
                    }
                    if (termoReferencia.has("newValue") && !Objects.isNull(termoReferencia.getJSONObject("newValue"))) {
                        Long newTermo = termoReferencia.getJSONObject("newValue").getLong("id");
                        termoReferenciaService.changeFinalizadoTermo(Arrays.asList(newTermo), true);
                    }
                }
                aplicarModificacoesArquivos(reqModificacaoFile.getJSONArray(FILES_FIELD), licitacao, licitacaoService, ArquivoLicitacaoDTO.class);
            }
        }

    private void applyRequisicaoModificacaoCredenciamento(RequisicaoModificacao requisicaoModificacao, JSONObject differencesObject, JSONObject reqModificacaoFile) throws JsonProcessingException {
        Credenciamento credenciamento = this.credenciamentoService.getById(requisicaoModificacao.getIdProcesso());
        credenciamento = (Credenciamento) this.applyJulgamentoGenerico(requisicaoModificacao, credenciamento, differencesObject);

        if (!Objects.isNull(differencesObject) && (differencesObject.has(TERMO_REFERENCIA_FIELD)) && !credenciadoService.getAllByCredenciamento(credenciamento.getId()).isEmpty()) {
            JSONObject termoReferencia = differencesObject.getJSONObject(TERMO_REFERENCIA_FIELD);
            List<Long> idsTermos = Arrays.asList(termoReferencia.getJSONObject("newValue").getLong("id"), termoReferencia.getJSONObject("oldValue").getLong("id"));
            termoReferenciaService.changeBlockedTermo(idsTermos, false);
        }

        if (credenciamento.getObra() != null && credenciamento.getObra().getEdificacao() != null) {
            credenciamento.getObra().getEdificacao().setObra(credenciamento.getObra());
        }

        this.credenciamentoService.save(credenciamento);
        if (requisicaoModificacao.getStatus().equals(StatusReqModificacao.ACEITA) && requisicaoModificacao.getTipo().equals(TipoModificacao.EDICAO) && reqModificacaoFile.has(FILES_FIELD)) {
            if (differencesObject.has(TERMO_REFERENCIA_FIELD) && !credenciadoService.getAllByCredenciamento(credenciamento.getId()).isEmpty()) {
                JSONObject termoReferencia = differencesObject.getJSONObject(TERMO_REFERENCIA_FIELD);
                Long oldTermo = termoReferencia.getJSONObject("oldValue").getLong("id");
                termoReferenciaService.changeFinalizadoTermo(Arrays.asList(oldTermo), false);
                Long newTermo = termoReferencia.getJSONObject("newValue").getLong("id");
                termoReferenciaService.changeFinalizadoTermo(Arrays.asList(newTermo), true);
            }
            aplicarModificacoesArquivos(reqModificacaoFile.getJSONArray(FILES_FIELD), credenciamento, credenciamentoService, ArquivoCredenciamentoDTO.class);
        }
        if (requisicaoModificacao.getStatus().equals(StatusReqModificacao.ACEITA) && requisicaoModificacao.getTipo().equals(TipoModificacao.REMOCAO)) {
            TermoReferencia termoReferencia = credenciamento.getTermoReferencia();
            termoReferencia.setIsFinalizado(false);
        }
    }

    private void applyRequisicaoModificaoDispensa(RequisicaoModificacao requisicaoModificacao, JSONObject differencesObject, JSONObject reqModificacaoFile) throws JsonProcessingException {
        Dispensa dispensa = this.dispensaService.getById(requisicaoModificacao.getIdProcesso());
        dispensa = (Dispensa) this.applyJulgamentoGenerico(requisicaoModificacao, dispensa, differencesObject);

        if (dispensa.getObra() != null && dispensa.getObra().getEdificacao() != null) {
            dispensa.getObra().getEdificacao().setObra(dispensa.getObra());
        }
        this.dispensaService.save(dispensa);
        if (requisicaoModificacao.getStatus().equals(StatusReqModificacao.ACEITA) && requisicaoModificacao.getTipo().equals(TipoModificacao.EDICAO) && reqModificacaoFile.has(FILES_FIELD)) {
            aplicarModificacoesArquivos(reqModificacaoFile.getJSONArray(FILES_FIELD), dispensa, dispensaService, ArquivoDispensaDTO.class);
        }
        if (requisicaoModificacao.getStatus().equals(StatusReqModificacao.ACEITA) && requisicaoModificacao.getTipo().equals(TipoModificacao.REMOCAO)) {
            TermoReferencia termoReferencia = dispensa.getTermoReferencia();
            termoReferencia.setIsFinalizado(false);
        }
    }

    private void applyRequisicaoModificacaoInexigibilidade(RequisicaoModificacao requisicaoModificacao, JSONObject differencesObject, JSONObject reqModificacaoFile) throws JsonProcessingException {
        Inexigibilidade inexigibilidade = this.inexigibilidadeService.getById(requisicaoModificacao.getIdProcesso());
        inexigibilidade = (Inexigibilidade) this.applyJulgamentoGenerico(requisicaoModificacao, inexigibilidade, differencesObject);

        if (inexigibilidade.getObra() != null && inexigibilidade.getObra().getEdificacao() != null) {
            inexigibilidade.getObra().getEdificacao().setObra(inexigibilidade.getObra());
        }

        this.inexigibilidadeService.save(inexigibilidade);
        if (requisicaoModificacao.getStatus().equals(StatusReqModificacao.ACEITA) && requisicaoModificacao.getTipo().equals(TipoModificacao.EDICAO) && reqModificacaoFile.has(FILES_FIELD)) {
            aplicarModificacoesArquivos(reqModificacaoFile.getJSONArray(FILES_FIELD), inexigibilidade, inexigibilidadeService, ArquivoInexigibilidadeDTO.class);
        }
        if (requisicaoModificacao.getStatus().equals(StatusReqModificacao.ACEITA) && requisicaoModificacao.getTipo().equals(TipoModificacao.REMOCAO)) {
            TermoReferencia termoReferencia = inexigibilidade.getTermoReferencia();
            termoReferencia.setIsFinalizado(false);
        }
    }

    private void applyRequisicaoModificacaoObraMedicao(RequisicaoModificacao requisicaoModificacao, JSONObject differencesObject, JSONObject reqModificacaoFile) throws JsonProcessingException {
        ObraMedicao medicao = this.obraMedicaoService.getById(requisicaoModificacao.getIdProcesso());
        medicao = (ObraMedicao) this.applyJulgamentoGenerico(requisicaoModificacao, medicao, differencesObject);
        this.obraMedicaoService.save(medicao);
        if (requisicaoModificacao.getStatus().equals(StatusReqModificacao.ACEITA) && requisicaoModificacao.getTipo().equals(TipoModificacao.EDICAO) && reqModificacaoFile.has(FILES_FIELD)) {
            aplicarModificacoesArquivos(reqModificacaoFile.getJSONArray(FILES_FIELD), medicao, obraMedicaoService, ArquivoObraMedicaoDTO.class);
        }
        if (requisicaoModificacao.getStatus().equals(StatusReqModificacao.ACEITA) && !Objects.isNull(differencesObject) && differencesObject.has("percConclusao")) {
            JSONObject difference = differencesObject.getJSONObject("percConclusao");
            if (difference.get("oldValue").equals(100) && !difference.get("newValue").equals(100)) {
                obraService.setStatusObra(medicao.getObra().getId(), StatusObra.EM_ANDAMENTO);
            } else if (!difference.get("oldValue").equals(100) && difference.get("newValue").equals(100)) {
                obraService.setStatusObra(medicao.getObra().getId(), StatusObra.FINALIZADA);
            }
        }
    }

    private void applyRequisicaoModificacaoTermoReferencia(RequisicaoModificacao requisicaoModificacao, JSONObject differencesObject) throws JsonProcessingException {
        TermoReferencia termoReferencia = this.termoReferenciaService.getById(requisicaoModificacao.getIdProcesso());
        termoReferencia = (TermoReferencia) this.applyJulgamentoGenerico(requisicaoModificacao, termoReferencia, differencesObject);
        arquivoRequisicaoModificacaoFileService.returnTempFilesFromReqModDirectory(termoReferencia.getArquivosTemporarios());
        if (termoReferenciaService.termoAssociadoLicitacao(termoReferencia.getId())) {
            licitacaoService.atualizaLicitacaoAssociadaAoTermo(termoReferencia);
        }
        this.termoReferenciaService.save(termoReferencia);
    }

    private Object aplicarModificacoes(JSONObject differences, Object entity) throws JsonProcessingException {
        JsonMapper jsonMapper = UtilsRequisicaoModificacao.getJsonMapper();
        JSONObject object = new JSONObject(jsonMapper.writeValueAsString(entity));
        differences.keySet().forEach(attr -> {
            JSONObject diff = differences.getJSONObject(attr);
            if (diff.has(NEW_VALUE_FIELD_NAME)) {
                object.put(attr, diff.get(NEW_VALUE_FIELD_NAME));
            } else {
                object.remove(attr);
            }
        });
        entity = jsonMapper.readValue(object.toString(), entity.getClass());
        return entity;
    }

    private void aplicarModificacoesArquivos(JSONArray newFiles, AbstractIdentificavel entity, AbstractUploadService service, Class<?> fileDtoType) throws AppException {
        JsonMapper jm = UtilsRequisicaoModificacao.getJsonMapper();
        List<ArquivoUploadDTO> arquivos = newFiles.toList().stream().map(arq -> {
            try {
                return (ArquivoUploadDTO) jm.readValue(jm.writeValueAsString(arq), fileDtoType);
            } catch (JsonProcessingException e) {
                throw new AppException("Falha ao aplicar alterações dos arquivos", HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }).collect(Collectors.toList());
        arquivoRequisicaoModificacaoFileService.returnTempFilesFromReqModDirectory(arquivos);
        service.saveArquivos(arquivos, entity);
    }

    private EntityToDtoMapper<Arquivo, ArquivoUploadDTO> getMapperByTipo(Objeto tipoProcesso) {
        EntityToDtoMapper mapper;
        if (tipoProcesso.equals(Objeto.ADITIVO)) {
            mapper = arquivoAditivoContratoToDtoMapper;
        } else if (tipoProcesso.equals(Objeto.CARONA)) {
            mapper = arquivoCaronaToDtoMapper;
        } else if (tipoProcesso.equals(Objeto.CONTRATO) || tipoProcesso.equals(Objeto.RESCISAO_CONTRATUAL)) {
            mapper = arquivoContratoToDtoMapper;
        } else if (tipoProcesso.equals(Objeto.DISPENSA)) {
            mapper = arquivoDispensaToDtoMapper;
        } else if (tipoProcesso.equals(Objeto.INEXIGIBILIDADE)) {
            mapper = arquivoInexigibilidadeToDtoMapper;
        } else if (tipoProcesso.equals(Objeto.LICITACAO)) {
            mapper = arquivoLicitacaoToDtoMapper;
        } else if (tipoProcesso.equals(Objeto.TERMO_REFERENCIA)) {
            mapper = arquivoTermoReferenciaToDtoMapper;
        } else if (tipoProcesso.equals(Objeto.OBRA_MEDICAO)) {
            mapper = arquivoObraMedicaoToDtoMapper;
        } else if (tipoProcesso.equals(Objeto.CREDENCIAMENTO)) {
            mapper = arquivoCredenciamentoToDtoMapper;
        } else if (tipoProcesso.equals(Objeto.CREDENCIADO)) {
            mapper = arquivoCredenciadoToDtoMapper;
        } else if (tipoProcesso.equals(Objeto.ANULACAO_REVOGACAO)) {
            mapper = arquivoAnulacaoRevogacaoToDtoMapper;
        } else {
            throw new AppException("Serviço de mapeamento de arquivos não criado para requisição de modificação", HttpStatus.UNPROCESSABLE_ENTITY);
        }
        return mapper;
    }

    private ArquivoRepository<? extends Arquivo> getRepositoryArquivoByTipo(Objeto tipoProcesso) {
        ArquivoRepository<? extends Arquivo> repository;
        if (tipoProcesso.equals(Objeto.ADITIVO)) {
            repository = arquivoAditivoContratoRepository;
        } else if (tipoProcesso.equals(Objeto.CARONA)) {
            repository = arquivoCaronaRepository;
        } else if (tipoProcesso.equals(Objeto.CONTRATO) || tipoProcesso.equals(Objeto.RESCISAO_CONTRATUAL)) {
            repository = arquivoContratoRepository;
        } else if (tipoProcesso.equals(Objeto.DISPENSA)) {
            repository = arquivoDispensaRepository;
        } else if (tipoProcesso.equals(Objeto.INEXIGIBILIDADE)) {
            repository = arquivoInexigibilidadeRepository;
        } else if (tipoProcesso.equals(Objeto.LICITACAO)) {
            repository = arquivoLicitacaoRepository;
        } else if (tipoProcesso.equals(Objeto.TERMO_REFERENCIA)) {
            repository = arquivoTermoReferenciaRepository;
        } else if (tipoProcesso.equals(Objeto.OBRA_MEDICAO)) {
            repository = arquivoObraMedicaoRepository;
        } else if (tipoProcesso.equals(Objeto.CREDENCIAMENTO)) {
            repository = arquivoCredenciamentoRepository;
        } else if (tipoProcesso.equals(Objeto.CREDENCIADO)) {
            repository = arquivoCredenciadoRepository;
        } else if (tipoProcesso.equals(Objeto.ANULACAO_REVOGACAO)) {
            repository = arquivoAnulacaoRevogacaoRepository;
        } else {
            throw new AppException("Serviço de arquivos não criado para requisição de modificação", HttpStatus.UNPROCESSABLE_ENTITY);
        }
        return repository;
    }

    @Override
    public JSONObject getModificacoes(Long idRequisicaoModificacao) throws AppException {
        try {
            JsonMapper jm = UtilsRequisicaoModificacao.getJsonMapper();
            RequisicaoModificacao reqMod = this.getRepository().getById(idRequisicaoModificacao);

            String userGroups = ThreadContext.get("groups");
            if (!entidadeService.getContextEntitiesIds().contains(reqMod.getEntidade().getId())
                    && (userGroups == null || !userGroups.contains("Auditor") && !userGroups.contains("Administrador"))) {
                throw new AppException("Operação não autorizada. Você não tem acesso a esta entidade.", HttpStatus.FORBIDDEN);
            }

            Object processo = this.getServiceByTipoProcesso(reqMod.getTipoProcesso()).getById(reqMod.getIdProcesso());
            JSONObject processoJSON = new JSONObject(jm.writeValueAsString(processo));
            JSONArray arquivosOriginais = new JSONArray(jm.writeValueAsString(this.getRepositoryArquivoByTipo(reqMod.getTipoProcesso()).buscarPor(reqMod.getIdProcesso()).stream().map(arq -> this.getMapperByTipo(reqMod.getTipoProcesso()).map(arq)).collect(Collectors.toList())));

            String fileContent = arquivoRequisicaoModificacaoFileService.getFileContentByIdRequisicaoModificacao(idRequisicaoModificacao);
            JSONObject reqModificacaoJSON = new JSONObject(fileContent);
            if (reqMod.getTipo().equals(TipoModificacao.EDICAO)) {
                JSONObject differences = reqModificacaoJSON.has(DIFFERENCES_FIELD) ? reqModificacaoJSON.getJSONObject(DIFFERENCES_FIELD) : new JSONObject();
                if (reqModificacaoJSON.has(FILES_FIELD)) {
                    JSONArray arquivosModificados = reqModificacaoJSON.getJSONArray(FILES_FIELD);
                    JSONObject filesDifferences = new JSONObject();
                    filesDifferences.put(OLD_VALUE_FIELD_NAME, arquivosOriginais);
                    filesDifferences.put(NEW_VALUE_FIELD_NAME, arquivosModificados);
                    differences.put(FILES_FIELD, filesDifferences);
                }
                this.setChangesRecursive(processoJSON, null, differences, reqMod.getTipoProcesso().getTipo());
            } else {
                processoJSON.put(FILES_FIELD, arquivosOriginais);
            }

            return processoJSON;
        } catch (IOException e) {
            throw new AppException("Ocorreu um erro ao tentar visualizar as modificações", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private JSONObject differencesMapToJSONObject(MapDifference<String, Object> differencesMap) {
        JSONObject differencesObject = new JSONObject();
        differencesMap.entriesDiffering().entrySet().stream().filter(entry -> !this.attributesToIgnoreChangesOnView.contains(entry.getKey())).forEach(diff -> {
            JSONObject diffObject = new JSONObject();
            Object oldValue = diff.getValue().leftValue();
            Object newValue = diff.getValue().rightValue();

            if (oldValue != null) {
                if (Map.class.isAssignableFrom(oldValue.getClass())) {
                    oldValue = UtilsRequisicaoModificacao.mapToJSON((Map) oldValue);
                } else if (List.class.isAssignableFrom(oldValue.getClass())) {
                    oldValue = new JSONArray((List) oldValue);
                }
            }

            if (newValue != null) {
                if (Map.class.isAssignableFrom(newValue.getClass())) {
                    newValue = UtilsRequisicaoModificacao.mapToJSON((Map) newValue);
                } else if (List.class.isAssignableFrom(newValue.getClass())) {
                    newValue = new JSONArray((List) newValue);
                }
            }

            diffObject.put(OLD_VALUE_FIELD_NAME, oldValue);
            diffObject.put(NEW_VALUE_FIELD_NAME, newValue);
            differencesObject.put(diff.getKey(), diffObject);
        });

        this.processRemovedValues(differencesMap, differencesObject);
        this.processNewValues(differencesMap, differencesObject);
        return differencesObject;
    }

    private void processRemovedValues(MapDifference<String, Object> differencesMap, JSONObject object) {
        differencesMap.entriesOnlyOnLeft().entrySet().forEach(diff -> {
            JSONObject diffObject = new JSONObject();
            Object oldValue = diff.getValue();
            if (Map.class.isAssignableFrom(oldValue.getClass())) {
                oldValue = UtilsRequisicaoModificacao.mapToJSON((Map) oldValue);
            } else if (List.class.isAssignableFrom(oldValue.getClass())) {
                oldValue = new JSONArray((List) oldValue);
            }
            diffObject.put(OLD_VALUE_FIELD_NAME, oldValue);
            diffObject.put(NEW_VALUE_FIELD_NAME, "");
            object.put(diff.getKey(), diffObject);
        });
    }

    private void processNewValues(MapDifference<String, Object> differencesMap, JSONObject object) {
        differencesMap.entriesOnlyOnRight().entrySet().forEach(diff -> {
            JSONObject diffObject = new JSONObject();
            Object newValue = diff.getValue();
            if (Map.class.isAssignableFrom(newValue.getClass())) {
                newValue = UtilsRequisicaoModificacao.mapToJSON((Map) newValue);
            } else if (List.class.isAssignableFrom(newValue.getClass())) {
                newValue = new JSONArray((List) newValue);
            }
            diffObject.put(OLD_VALUE_FIELD_NAME, "");
            diffObject.put(NEW_VALUE_FIELD_NAME, newValue);
            object.put(diff.getKey(), diffObject);
        });
    }

    /*
     * Método que aplica labels e define as modificações recursivamente a partir de algumas regras documentadas no codigo.
     */
    private void setChangesRecursive(JSONObject object, JSONObject newObject, JSONObject differences, Class<?> type) {
        JSONObject localDifferences = new JSONObject();
        /*
         * Na primeira execução o parametro differences já está definido e é resgatado do objeto JSON salvo representante da requisição de modificação, já
         * quando o método é chamado recursivamente, esse parametro é passado como null, sendo necessário recalcular as diferenças entre os objetos.
         */
        if (differences == null) {
            Map<String, Object> oldEntityMap = object.toMap();
            Map<String, Object> newEntityMap = newObject.toMap();
            MapDifference<String, Object> differencesMap = Maps.difference(oldEntityMap, newEntityMap);
            localDifferences = this.differencesMapToJSONObject(differencesMap);
            if (differencesMap.entriesDiffering().entrySet().stream().filter(entry -> !this.attributesToIgnoreChangesOnView.contains(entry.getKey())).count() > 0) {
                object.put(MODIFICADO_FIELD, EDICAO_TAG);
            }
        }

        JSONObject differencesObject = Optional.ofNullable(differences).orElse(localDifferences);
        differencesObject.keySet().stream().forEach(attr -> {
            // O seguinte bloco realiza a identificação dos campos existentes na classe para validar se o campo existe antes de avaliar as modificações dos mesmos, a identificação ocorre recursivamente para as superclasses.
            List<String> attrClassList = UtilsRequisicaoModificacao.getAllFieldsByClass(type);
            if (attrClassList.contains(attr) || attr.equals(FILES_FIELD)) {
                JSONObject diff = differencesObject.getJSONObject(attr);
                Class<?> attrType = attr.equals(FILES_FIELD) ? ArquivoUploadDTO.class : UtilsRequisicaoModificacao.getAttrType(type, attr);
                if (diff.has(OLD_VALUE_FIELD_NAME) && diff.has(NEW_VALUE_FIELD_NAME)) {
                    // Caso exista um valor antigo e um novo valor, o campo em questão foi editado, resultando nos tratamentos a seguir...
                    this.handleValueChanged(object, diff, attr, attrType, type);
                } else if (diff.has(OLD_VALUE_FIELD_NAME)) {
                    // Caso exista um valor antigo, mas não exista um novo valor, o valor do campo foi removido, resultando no seguinte tratamento...
                    object.put(attr, UtilsRequisicaoModificacao.getString(diff.get(OLD_VALUE_FIELD_NAME), attr, type));
                    object.put(NEW_PREFIX + StringUtils.capitalize(attr), "");
                } else if (diff.has(NEW_VALUE_FIELD_NAME)) {
                    // Caso exista um novo valor, mas não exista um valor antigo, o valor do campo foi adicionado, resultando no seguinte tratamento...
                    object.put(attr, "");
                    object.put(NEW_PREFIX + StringUtils.capitalize(attr), UtilsRequisicaoModificacao.getString(diff.get(NEW_VALUE_FIELD_NAME), attr, type));
                }
            }
        });
    }

    private void handleValueChanged(JSONObject object, JSONObject diff, String attr, Class<?> attrType, Class<?> parentType) {
        if (JSONObject.class.isAssignableFrom(diff.get(OLD_VALUE_FIELD_NAME).getClass()) && complexFields.contains(attr)) {
            //Este if representa o caso de uma modificação em um campo complexo de um objeto, o tratamento nesse caso é a chamada recursiva do método com os objetos novos e antigos.
            object.put(attr, diff.getJSONObject(OLD_VALUE_FIELD_NAME));
            this.setChangesRecursive(object.getJSONObject(attr), diff.getJSONObject(NEW_VALUE_FIELD_NAME), null, attrType);
        } else if (JSONArray.class.isAssignableFrom(diff.get(OLD_VALUE_FIELD_NAME).getClass()) && complexFields.contains(attr)) {
            //Este if representa o caso de uma modificação em um campo complexo de uma coleção de valores, o tratamento nesse caso é a identificação dos valores removidos e adicionados da lista, alem da chamada recursiva do método para os que foram editados.
            this.handleListChange(object, diff, attr, attrType);
        } else {
            /*
            Este if representa o caso de uma modificação em um campo simples de um valor de qualquer natureza, nesse caso, um novo campo no formato new<NOME_CAMPO> é criado com o novo valor representado por uma string.
            No caso do valor antigo ter que ser mantido, um novo campo no formato original<NOME_CAMPO>, contendo o objeto estruturado.
            */
            if (this.requiredOriginalAttributes.contains(attr)) {
                object.put(ORIGINAL_PREFIX + StringUtils.capitalize(attr), diff.get(NEW_VALUE_FIELD_NAME));
            }
            object.put(attr, UtilsRequisicaoModificacao.getString(diff.get(OLD_VALUE_FIELD_NAME), attr, parentType));
            if (!UtilsRequisicaoModificacao.equalsJSON(diff.get(OLD_VALUE_FIELD_NAME), diff.get(NEW_VALUE_FIELD_NAME))) {
                object.put(NEW_PREFIX + StringUtils.capitalize(attr), UtilsRequisicaoModificacao.getString(diff.get(NEW_VALUE_FIELD_NAME), attr, parentType));
            }
        }
    }

    private void handleListChange(JSONObject object, JSONObject diff, String attr, Class<?> attrType) {
        String idAttr = ArquivoUploadDTO.class.isAssignableFrom(attrType) ? ID_ARQUIVO_FIELD : ID_FIELD;
        List<Object> listNewIds = diff.getJSONArray(NEW_VALUE_FIELD_NAME).toList().stream().map(jsonObject -> ((Map) jsonObject).containsKey(idAttr) ? ((Map) jsonObject).get(idAttr) : null).collect(Collectors.toList());

        List<Object> listOldIds = diff.getJSONArray(OLD_VALUE_FIELD_NAME).toList().stream().map(jsonObject -> ((Map) jsonObject).containsKey(idAttr) ? ((Map) jsonObject).get(idAttr) : null).collect(Collectors.toList());

        object.put(attr, diff.getJSONArray(OLD_VALUE_FIELD_NAME));
        object.getJSONArray(attr).forEach(value -> {
            if (JSONObject.class.isAssignableFrom(value.getClass())) {
                JSONObject jsonObject = (JSONObject) value;
                if (listNewIds.contains(jsonObject.get(idAttr))) {
                    JSONObject editedObject = diff.getJSONArray(NEW_VALUE_FIELD_NAME).getJSONObject(listNewIds.indexOf(jsonObject.get(idAttr)));
                    this.setChangesRecursive(jsonObject, editedObject, null, attrType);
                } else {
                    jsonObject.put(MODIFICADO_FIELD, REMOCAO_TAG);
                }
            }
        });

        diff.getJSONArray(NEW_VALUE_FIELD_NAME).forEach(value -> {
            if (JSONObject.class.isAssignableFrom(value.getClass())) {
                JSONObject addedObject = (JSONObject) value;
                if (!addedObject.has(idAttr) || !listOldIds.contains(addedObject.get(idAttr))) {
                    addedObject.put(MODIFICADO_FIELD, ADICAO_TAG);
                    object.getJSONArray(attr).put(addedObject);
                }
            }
        });
    }

    private AbstractUploadService<AbstractRequisicaoModificacaoIdentificavel, AbstractFiltroRequest, ArquivoMappedSuperclassDTO, ArquivoIdentificavel> getServiceByTipoProcesso(Objeto tipoProcesso) {
        AbstractUploadService service;
        if (tipoProcesso.equals(Objeto.ADITIVO)) {
            service = aditivoContratoService;
        } else if (tipoProcesso.equals(Objeto.CARONA)) {
            service = caronaService;
        } else if (tipoProcesso.equals(Objeto.CONTRATO) || tipoProcesso.equals(Objeto.RESCISAO_CONTRATUAL)) {
            service = contratoService;
        } else if (tipoProcesso.equals(Objeto.DISPENSA)) {
            service = dispensaService;
        } else if (tipoProcesso.equals(Objeto.INEXIGIBILIDADE)) {
            service = inexigibilidadeService;
        } else if (tipoProcesso.equals(Objeto.LICITACAO)) {
            service = licitacaoService;
        } else if (tipoProcesso.equals(Objeto.TERMO_REFERENCIA)) {
            service = termoReferenciaService;
        } else if (tipoProcesso.equals(Objeto.OBRA_MEDICAO)) {
            service = obraMedicaoService;
        }  else if (tipoProcesso.equals(Objeto.CREDENCIAMENTO)) {
            service = credenciamentoService;
        }  else if (tipoProcesso.equals(Objeto.CREDENCIADO)) {
            service = credenciadoService;
        }  else if (tipoProcesso.equals(Objeto.ANULACAO_REVOGACAO)) {
            service = anulacaoRevogacaoService;
        } else {
            throw new AppException("Serviço não criado para requisição de modificação", HttpStatus.UNPROCESSABLE_ENTITY);
        }
        return service;
    }

    @Override
    public ArquivoBinarioDTO download(Long idReqMod, ArquivoDTO arquivoDTO) throws AppException {
        RequisicaoModificacao reqMod = this.getRepository().getById(idReqMod);
        if (arquivoDTO.ehArquivoTemporarioValido() && reqMod.getStatus().equals(StatusReqModificacao.ENVIADA)) {
            return arquivoRequisicaoModificacaoFileService.downloadTempFileReqMod(arquivoDTO);
        } else {
            return this.getServiceByTipoProcesso(reqMod.getTipoProcesso()).download(arquivoDTO);
        }
    }
}
