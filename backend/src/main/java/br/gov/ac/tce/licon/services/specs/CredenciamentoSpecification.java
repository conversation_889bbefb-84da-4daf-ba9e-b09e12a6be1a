package br.gov.ac.tce.licon.services.specs;

import br.gov.ac.tce.licon.dtos.requests.CredenciamentoFiltroRequest;
import br.gov.ac.tce.licon.entities.Credenciamento;
import br.gov.ac.tce.licon.entities.Credenciamento_;
import lombok.AllArgsConstructor;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public class CredenciamentoSpecification implements ISpecification<Credenciamento> {
    private final CredenciamentoFiltroRequest filtro;

    @Override
    public Predicate toPredicate(Root<Credenciamento> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
        List<Predicate> predicados = new ArrayList<>();

        addIfExists(getPredicate(root.get(Credenciamento_.entidade), filtro.getEntidade(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(Credenciamento_.numeroProcesso), filtro.getNumeroProcesso(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(Credenciamento_.numero), filtro.getNumero(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(Credenciamento_.objeto), filtro.getObjeto(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(Credenciamento_.fontesDeRecurso), filtro.getFontesDeRecurso(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(Credenciamento_.status), filtro.getStatus(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(Credenciamento_.observacoes), filtro.getObservacoes(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(Credenciamento_.inicioVigencia), filtro.getInicioVigencia(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(Credenciamento_.fimVigencia), filtro.getFimVigencia(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(Credenciamento_.responsavel), filtro.getResponsavel(), builder, filtro.getFilterType()), predicados);

        return builder.and(predicados.toArray(new Predicate[0]));
    }

}
