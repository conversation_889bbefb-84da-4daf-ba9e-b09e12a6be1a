package br.gov.ac.tce.licon.services.specs.geoobras;

import br.gov.ac.tce.licon.dtos.requests.geoobras.GeoobraObraFiltroRequest;
import br.gov.ac.tce.licon.entities.geoobras.GeoobraObra;
import br.gov.ac.tce.licon.entities.geoobras.GeoobraObra_;
import br.gov.ac.tce.licon.services.specs.ISpecification;
import lombok.AllArgsConstructor;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public class GeoobraObraSpecification implements ISpecification<GeoobraObra> {

    private static final long serialVersionUID = 1L;

    private final GeoobraObraFiltroRequest filtro;

    @Override
    public Predicate toPredicate(Root<GeoobraObra> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
        List<Predicate> predicados = new ArrayList<>();

        addIfExists(getPredicate(root.get(GeoobraObra_.numero), filtro.getNumero(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.tipo), filtro.getTipo(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.endereco), filtro.getEndereco(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.cep), filtro.getCep(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.dimensoes), filtro.getDimensoes(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.valor), filtro.getValor(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.descricao), filtro.getDescricao(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.incorporavelPatrimonio), filtro.getIncorporavelPatrimonio(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.tipoObjeto), filtro.getTipoObjeto(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.subtipoObjeto), filtro.getSubtipoObjeto(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.dataPrevistaInicio), filtro.getDataPrevistaInicio(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.dataPrevistaConclusao), filtro.getDataPrevistaConclusao(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.dataCadastro), filtro.getDataCadastro(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.status), filtro.getStatus(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.possuiConveniosAssociados), filtro.getPossuiConveniosAssociados(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.possuiRecursosProprios), filtro.getPossuiRecursosProprios(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(GeoobraObra_.entidade), filtro.getEntidade(), builder, filtro.getFilterType()), predicados);

        return builder.and(predicados.toArray(new Predicate[0]));
    }
}
