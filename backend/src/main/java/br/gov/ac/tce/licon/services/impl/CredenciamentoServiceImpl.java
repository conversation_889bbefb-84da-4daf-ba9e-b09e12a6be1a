package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.ArquivoDTO;
import br.gov.ac.tce.licon.dtos.mapper.ArquivoCredenciamentoToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.*;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchParameter;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.SearchOperator;
import br.gov.ac.tce.licon.dtos.responses.BuscaResponse;
import br.gov.ac.tce.licon.entities.*;
import br.gov.ac.tce.licon.entities.enums.StatusAuditoriaLicitacao;
import br.gov.ac.tce.licon.entities.enums.StatusLicitacao;
import br.gov.ac.tce.licon.entities.enums.TipoArquivoCredenciamento;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.*;
import br.gov.ac.tce.licon.services.*;
import br.gov.ac.tce.licon.services.specs.CredenciamentoSpecification;
import com.j256.simplemagic.ContentType;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class CredenciamentoServiceImpl extends AbstractUploadTipoServiceImpl<Credenciamento, CredenciamentoFiltroRequest, CredenciamentoRepository, ArquivoCredenciamentoFileService, ArquivoCredenciamento, ArquivoCredenciamentoFiltroRequest, ArquivoCredenciamentoService, ArquivoCredenciamentoDTO, ArquivoCredenciamentoToDtoMapper, TipoArquivoCredenciamento> implements CredenciamentoService {

    @Inject
    private ArquivoCredenciamentoService arquivoCredenciamentoService;

    @Inject
    private ArquivoCredenciamentoFileService arquivoCredenciamentoFileService;

    @Autowired
    private CredenciamentoRepository repository;

    @Autowired
    private ObraRepository obraRepository;

    @Autowired
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Autowired
    private ArquivoCredenciamentoToDtoMapper arquivoCredenciamentoToDtoMapper;

    @Autowired
    private TermoReferenciaService termoReferenciaService;

    @Autowired
    private TermoReferenciaRepository termoReferenciaRepository;

    @Override
    public CredenciamentoRepository getRepository() {
        return repository;
    }

    @Autowired
    private TdaCredenciamentoRepository tdaRepository;

    @Autowired
    private AnaliseProcessoViewServiceImpl analiseProcessoViewService;

    @Autowired
    private AnulacaoRevogacaoService anulacaoRevogacaoService;

    @Override
    protected Example<Credenciamento> obterExemploChecarSeJahExiste(Credenciamento entity) throws AppException {
        Credenciamento exemplo = Credenciamento.builder().numeroProcesso(entity.getNumeroProcesso()).build();
        return Example.of(exemplo);
    }

    @Override
    protected void lancarErroEntidadeJahExistente(Credenciamento entity) throws AppException {
        throw new AppException(String.format("Credenciamento com dado número de processo já existe: %s", entity.getNumeroProcesso()), HttpStatus.UNPROCESSABLE_ENTITY);
    }

    @Override
    protected Specification<Credenciamento> getSpecification(CredenciamentoFiltroRequest filtro) {
        return new CredenciamentoSpecification(filtro);
    }

    @Override
    protected void beforeSave(Credenciamento entity) {
        if (entity.getId() == null) {
            entity.setDataCadastro(LocalDateTime.now());
            entity.setUsuario(getCurrentUser());
            entity.setStatus(StatusLicitacao.PUBLICADA);
        }
    }

    public void saveCredenciamento(CredenciamentoDTO dto) throws AppException {
        Credenciamento credenciamento = dto.getCredenciamento();
        List<ArquivoCredenciamentoDTO> arquivos = dto.getArquivos();
        this.validarArquivos(arquivos);
        TermoReferencia termoReferencia = credenciamento.getTermoReferencia();
        if (credenciamento.getId() == null && termoReferencia != null && termoReferenciaRepository.checkAssociatedTerm(termoReferencia.getId())) {
            throw new AppException("A operação não pôde ser concluída, pois o Termo de Referência escolhido já está associado a algum processo.", HttpStatus.UNPROCESSABLE_ENTITY);
        }

        if (termoReferencia != null && !termoReferencia.getIsFinalizado()) {
            termoReferencia = termoReferenciaService.finalizarTermo(termoReferencia, "Credenciamento");
            credenciamento.setTermoReferencia(termoReferencia);
        }

        Edificacao edificacao;
        Obra obra;

        if (dto.getObra() != null) {
            obra = credenciamento.getObra();
            edificacao = Optional.ofNullable(obra.getEdificacao()).orElse(new Edificacao());
            edificacao.setGeometry(dto.getObra().getTipoCamada(), dto.getObra().getCoordenadas());
            edificacao.setObra(obra);
            obra.setEdificacao(edificacao);
            credenciamento.setObra(obra);
            credenciamento.getObra().setCredenciamento(credenciamento);
        } else if (dto.getEdificacao() != null) {
            edificacao = dto.getEdificacao();
            obra = credenciamento.getObra();
            obra.setEdificacao(edificacao);
            edificacao.setObra(obra);
            credenciamento.setObra(obra);
            credenciamento.getObra().setCredenciamento(credenciamento);
        }

        credenciamento = save(credenciamento);
        saveArquivos(arquivos, credenciamento);
    }

    @Override
    protected void afterSave(boolean isNew, Credenciamento entity) {
        repository.updateLastModifiedTimestamp(entity.getId());
    }

    @Override
    public BuscaResponse<Credenciamento> buscarAdvanced(AdvancedSearchRequest filtro) {
        if (filtro.getAndParameters().isEmpty()) {
            setDefaultFilterParams(filtro);
        }
        return super.buscarAdvanced(filtro);
    }

    @Override
    protected void setDefaultFilterParams(AdvancedSearchRequest filtro) {
        AdvancedSearchParameter situacaoParam = new AdvancedSearchParameter("status", SearchOperator.NOT_EQUAL_TO, StatusLicitacao.REMOVIDA.name());
        String[] idEntidadesContexto = ThreadContext.get("entities").replace("[", "").replace("]", "").split(", ");
        for (String id : idEntidadesContexto) {
            if (!id.isEmpty()) {
                AdvancedSearchParameter entidadeParam = new AdvancedSearchParameter("entidade", SearchOperator.EQUAL_TO, Long.valueOf(id));
                filtro.getOrParameters().add(entidadeParam);
            }
        }
        filtro.getAndParameters().add(situacaoParam);
    }

    @Override
    public ArquivoCredenciamentoService getArquivoService() {
        return arquivoCredenciamentoService;
    }

    @Override
    public ArquivoCredenciamentoFileService getFileService() {
        return arquivoCredenciamentoFileService;
    }

    @Override
    public ArquivoCredenciamentoToDtoMapper getMapper() {
        return arquivoCredenciamentoToDtoMapper;
    }

    @Override
    protected ArquivoCredenciamento getNewArquivo() {
        return new ArquivoCredenciamento();
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoCredenciamento arquivoEntity, Credenciamento entity, ArquivoCredenciamentoDTO arquivoUpload) {
        arquivoEntity.setCredenciamento(entity);
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    @Override
    public UltimaAlteracaoDTO getUltimaAlteracao(Long id) {
        List<Object[]> ultimasModificadores = repository.getUltimaAlteracao(id);
        if (!ultimasModificadores.isEmpty()) {
            UltimaAlteracaoDTO response = new UltimaAlteracaoDTO();
            Object[] ultimaModificacao = ultimasModificadores.get(0);
            response.setData(((Timestamp) ultimaModificacao[1]).toLocalDateTime());
            response.setNome((String) ultimaModificacao[0]);
            return response;
        } else {
            return null;
        }
    }

    public ArquivoBinarioDTO download(ArquivoDTO arquivoDTO, Boolean countDownloads) throws AppException {
        ArquivoBinarioDTO download = arquivoCredenciamentoFileService.download(arquivoDTO);
        if (arquivoDTO.ehArquivoDefinitivoValido() && Boolean.TRUE.equals(countDownloads)) {
            Long idArquivoCredenciamento = Long.parseLong(arquivoDTO.getLookupId());
            repository.updateNumeroDownloadsCredenciamento(idArquivoCredenciamento);
        }
        return download;
    }

    @Override
    public void validarArquivos(List<ArquivoCredenciamentoDTO> arquivos) {
        List<TipoArquivoCredenciamento> tiposArquivosEnviados = extrairTiposArquivos(arquivos);
        this.verificarDescricaoTipoOutros(arquivos);
        this.verificarArquivosObrigatorios(tiposArquivosEnviados, this.getArquivosTiposObrigatorios());

        if (arquivos.stream().anyMatch(arquivo -> arquivo.getTipo().equals(TipoArquivoCredenciamento.MAPA_COMPARATIVO_PRECO) && !checkXlsMimeTypes(arquivo.getArquivo().getTipoArquivo()))) {
            throw new AppException("O arquivo do tipo 'Mapa Comparativo de Preços' deve ser uma Planilha ou um PDF", HttpStatus.BAD_REQUEST);
        } else if (arquivos.stream().anyMatch(arquivo -> arquivo.getTipo().equals(TipoArquivoCredenciamento.PROJETO_ENGENHARIA) && !checkTypeDWG(arquivo.getArquivo().getTipoArquivo()))) {
            throw new AppException("O arquivo do tipo 'Projetos de engenharia (básico, executivo e afins)' deve ser em formato DWG", HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() throws AppException {
        List<String> filtros = new ArrayList<>();
        filtros.add("CREDENCIAMENTO");
        List<ObrigatoriedadeArquivo> obrigatoriedadeArquivos = this.obrigatoriedadeArquivoService.getArquivosObrigatorios("CREDENCIAMENTO", filtros);
        return obrigatoriedadeArquivos.stream().filter(ObrigatoriedadeArquivo::getObrigatorio).collect(Collectors.toList());
    }

    @Override
    protected void validateCollectionsRemoval(Credenciamento object, Credenciamento persistedObject) {
        validateObrasRemoval(object, persistedObject);
    }

    private void validateObrasRemoval(Credenciamento object, Credenciamento persistedObject) {
        if (persistedObject.getObra() != null && (object.getObra() == null || !persistedObject.getObra().equals(object.getObra()))) {
            obraRepository.deleteById(persistedObject.getObra().getId());
        }
    }

    private Boolean checkXlsMimeTypes(String mimeType) {
        List<ContentType> types = Arrays.asList(ContentType.MICROSOFT_EXCEL,
                ContentType.MICROSOFT_EXCEL_XML,
                ContentType.CSV,
                ContentType.PDF);
        return types.stream().anyMatch(type -> type.getMimeType().equals(mimeType));
    }

    @Override
    public void setEmAnalise(Long idProcesso, Boolean emAnalise) {
        LocalDateTime dataAnalise = LocalDateTime.now();
        if (!emAnalise) {
            dataAnalise = null;
        }
        this.repository.updateEmAnalise(idProcesso, emAnalise, dataAnalise);
        this.repository.updateLastAuditTimestamp(idProcesso);
    }

    @Override
    public Credenciamento getById(Long idCredenciameto) {
        Credenciamento credenciamento = super.getById(idCredenciameto);
        credenciamento.setTda(tdaRepository.getByIdCredenciamento(idCredenciameto));
        return credenciamento;
    }

    @Override
    public void arquivarProcesso(Long idCredenciamento, StatusAuditoriaLicitacao status) {
        if (isUsuarioInspetor() || ThreadContext.get("groups").equals("Administrador")) {
            Optional<Credenciamento> optCredenciamento = getRepository().findById(idCredenciamento);
            if (optCredenciamento.isPresent()) {
                Credenciamento credenciamento = optCredenciamento.get();
                credenciamento.setStatusProcessoArquivado(status);
                getRepository().save(credenciamento);
                getRepository().updateLastAuditTimestamp(idCredenciamento);
            }
        } else {
            throw new AppException("O usuário não tem permissão para realizar a operação", HttpStatus.UNAUTHORIZED);
        }
    }

    @Override
    public void desarquivarCredenciamento(Long idProcesso) {
        this.repository.desarquivarCredenciamento(idProcesso, LocalDateTime.now());
        this.repository.updateLastAuditTimestamp(idProcesso);
    }

    public Long getAlertaCredenciamento(Long idProcesso, String tipoProcesso) {
        AnaliseProcessoView alerta = analiseProcessoViewService.getProcesso(idProcesso, tipoProcesso);
        if (alerta != null) {
            return alerta.getIdAlertaAnalise();
        }
        return null;
    }

    @Override
    public void anularRevogar(Long idProcesso, AnulacaoRevogacaoDTO dto) {
        Credenciamento entity = repository.getById(idProcesso);
        AnulacaoRevogacao persistedAnulacaoRevogacao = anulacaoRevogacaoService.saveAnulacaoRevogacao(dto);
        entity.setAnulacaoRevogacao(persistedAnulacaoRevogacao);
        save(entity);
    }
}
