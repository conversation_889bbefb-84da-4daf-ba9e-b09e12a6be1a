package br.gov.ac.tce.licon.entities;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

import br.gov.ac.tce.licon.entities.enums.Legislacao;
import org.hibernate.envers.Audited;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Audited(withModifiedFlag = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "MODALIDADE_LICITACAO")
@AttributeOverride(name = "id", column = @Column(name = "ID_MODALIDADE_LICITACAO"))
public class ModalidadeLicitacao extends AbstractIdentificavel {

    @NotNull
    @Column(name = "NOME")
    private String nome;

    @NotNull
    @Column(name = "VIGENCIA_DE")
    private LocalDateTime vigenciaDe;

    @NotNull
    @Column(name = "VIGENCIA_ATE")
    private LocalDateTime vigenciaAte;

    @NotNull
    @Column(name = "PERMITE_CONSORCIO")
    private Boolean permiteConsorcio;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "LEGISLACAO")
    private Legislacao legislacao;

    @Override
    public String toString() {
        return nome;
    }

}
