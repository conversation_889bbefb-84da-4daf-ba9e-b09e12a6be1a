
package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.mapper.ArquivoEmpenhoContratoToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.*;
import br.gov.ac.tce.licon.entities.*;
import br.gov.ac.tce.licon.entities.enums.StatusLicitacao;
import br.gov.ac.tce.licon.entities.enums.TipoArquivoEmpenho;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.EmpenhoContratoRepository;
import br.gov.ac.tce.licon.services.*;
import br.gov.ac.tce.licon.services.specs.EmpenhoContratoSpecification;
import com.google.common.collect.ImmutableList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class EmpenhoContratoServiceImpl extends AbstractUploadTipoServiceImpl<EmpenhoContrato, EmpenhoContratoFiltroRequest, EmpenhoContratoRepository, ArquivoEmpenhoContratoFileService, ArquivoEmpenhoContrato, ArquivoEmpenhoContratoFiltroResquest, ArquivoEmpenhoContratoService, ArquivoEmpenhoContratoDTO, ArquivoEmpenhoContratoToDtoMapper, TipoArquivoEmpenho> implements EmpenhoContratoService {

    @Autowired
    private EmpenhoContratoRepository repository;

    @Autowired
    private ArquivoEmpenhoContratoService arquivoEmpenhoContratoService;

    @Autowired
    private ArquivoEmpenhoContratoFileService arquivoEmpenhoContratoFileService;

    @Autowired
    private ArquivoEmpenhoContratoToDtoMapper arquivoEmpenhoContratoToDtoMapper;

    @Inject
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Override
    public EmpenhoContratoRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<EmpenhoContrato> getSpecification(EmpenhoContratoFiltroRequest filtro) {
        return new EmpenhoContratoSpecification(filtro);
    }

    @Override
    protected void lancarErroEntidadeJahExistente(EmpenhoContrato entity) throws AppException {
        throw new AppException(String.format("Empenho com dado número já existe: %s", entity.getNumeroEmpenho()),
                HttpStatus.UNPROCESSABLE_ENTITY);
    }

    @Override
    protected void beforeSave(EmpenhoContrato entity) {
        if (entity.getDataCadastro() == null) {
            entity.setDataCadastro(LocalDateTime.now());
            entity.setStatus(StatusLicitacao.PUBLICADA);
        }
    }

    @Override
    public void salvarArquivosEmpenhoContrato(Long idEmpenhoContrato, List<ArquivoEmpenhoContratoDTO> filesDto) {
        Optional<EmpenhoContrato> empenhoContrato = this.repository.findById(idEmpenhoContrato);
        if (empenhoContrato.isPresent()) {
            validarArquivos(filesDto);
            saveArquivos(filesDto, empenhoContrato.get());
        }
    }

    @Override
    public EmpenhoContrato saveEmpenhoContrato(EmpenhoContratoDTO dto) {
        EmpenhoContrato empenhoContrato = super.save(dto.getEmpenhoContrato());
        salvarArquivosEmpenhoContrato(empenhoContrato.getId(), dto.getArquivosEmpenhoContrato());
        return empenhoContrato;
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoEmpenhoContrato arquivoEntity, EmpenhoContrato entity, ArquivoEmpenhoContratoDTO arquivoUpload) {
        arquivoEntity.setEmpenhoContrato(entity);
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    @Override
    public ArquivoEmpenhoContratoService getArquivoService() {
        return arquivoEmpenhoContratoService;
    }

    @Override
    public ArquivoEmpenhoContratoFileService getFileService() {
        return arquivoEmpenhoContratoFileService;
    }

    @Override
    public ArquivoEmpenhoContratoToDtoMapper getMapper() {
        return arquivoEmpenhoContratoToDtoMapper;
    }

    @Override
    protected ArquivoEmpenhoContrato getNewArquivo() {
        return new ArquivoEmpenhoContrato();
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() {
        return obrigatoriedadeArquivoService.getArquivosObrigatorios("EMPENHO", ImmutableList.of("EMPENHO")).stream().filter(ObrigatoriedadeArquivo::getObrigatorio).collect(Collectors.toList());
    }

    @Override
    public void remover(Long id) throws AppException {
        Optional<EmpenhoContrato> entidadeOpt = getRepository().findById(id);
        if (entidadeOpt.isPresent()) {
            EmpenhoContrato empenho = entidadeOpt.get();
            validarRemover(empenho);
            empenho.setStatus(StatusLicitacao.REMOVIDA);
            getRepository().save(empenho);
        }
    }
}
