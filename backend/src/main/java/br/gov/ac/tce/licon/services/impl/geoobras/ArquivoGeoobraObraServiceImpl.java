package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivoGeoobraObraFiltroRequest;
import br.gov.ac.tce.licon.entities.geoobras.ArquivoGeoobraObra;
import br.gov.ac.tce.licon.repositories.geoobras.ArquivoGeoobraObraRepository;
import br.gov.ac.tce.licon.services.geoobras.ArquivoGeoobraObraService;
import br.gov.ac.tce.licon.services.impl.AbstractService;
import br.gov.ac.tce.licon.services.specs.geoobras.ArquivoGeoobraObraSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Transactional
public class ArquivoGeoobraObraServiceImpl
        extends AbstractService<ArquivoGeoobraObra, ArquivoGeoobraObraFiltroRequest, ArquivoGeoobraObraRepository>
        implements ArquivoGeoobraObraService {

    @Autowired
    private ArquivoGeoobraObraRepository repository;

    @Override
    public ArquivoGeoobraObraRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<ArquivoGeoobraObra> getSpecification(ArquivoGeoobraObraFiltroRequest filtro) {
        return new ArquivoGeoobraObraSpecification(filtro);
    }

    @Override
    protected void beforeSave(ArquivoGeoobraObra entity) {
        if (entity.getId() == null) {
            entity.setDataEnvio(LocalDateTime.now());
        }
        if (entity.getFase() == null) {
            entity.setFase(entity.getObra().getFase());
        }
    }

    @Override
    public List<ArquivoGeoobraObra> buscarPor(Long idObra) {
        return repository.buscarPor(idObra);
    }
}
