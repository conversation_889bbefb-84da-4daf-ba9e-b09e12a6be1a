package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.ArquivoDTO;
import br.gov.ac.tce.licon.dtos.mapper.ArquivoTermoReferenciaToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.ArquivoTermoReferenciaDTO;
import br.gov.ac.tce.licon.dtos.requests.ArquivoTermoReferenciaFiltroRequest;
import br.gov.ac.tce.licon.dtos.requests.TermoReferenciaFiltroRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchParameter;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.SearchOperator;
import br.gov.ac.tce.licon.dtos.responses.ProcessoResponseDTO;
import br.gov.ac.tce.licon.entities.*;
import br.gov.ac.tce.licon.entities.enums.Objeto;
import br.gov.ac.tce.licon.entities.enums.TipoArquivoTermoReferencia;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.*;
import br.gov.ac.tce.licon.services.*;
import br.gov.ac.tce.licon.services.specs.TermoReferenciaSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class TermoReferenciaServiceImpl
        extends AbstractUploadTipoServiceImpl<TermoReferencia, TermoReferenciaFiltroRequest, TermoReferenciaRepository, ArquivoTermoReferenciaFileService, ArquivoTermoReferencia, ArquivoTermoReferenciaFiltroRequest, ArquivoTermoReferenciaService, ArquivoTermoReferenciaDTO, ArquivoTermoReferenciaToDtoMapper, TipoArquivoTermoReferencia>
        implements TermoReferenciaService {

    @Autowired
    private TermoReferenciaRepository repository;

    @Autowired
    private SecaoRepository secaoRepository;

    @Autowired
    private TermoReferenciaSecaoRepository termoReferenciaSecaoRepository;

    @Autowired
    private LoteRepository loteRepository;

    @Autowired
    private ArquivoTermoReferenciaService arquivoTermoReferenciaService;

    @Autowired
    private ArquivoTermoReferenciaFileService arquivoTermoReferenciaFileService;

    @Autowired
    private ArquivoTermoReferenciaToDtoMapper arquivoTermoReferenciaToDtoMapper;

    @Autowired
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Autowired
    private LicitacaoRepository licitacaoRepository;

    @Autowired
    private CaronaRepository caronaRepository;

    @Autowired
    private DispensaRepository dispensaRepository;

    @Autowired
    private InexigibilidadeRepository inexigibilidadeRepository;

    @Autowired
    private CredenciamentoRepository credenciamentoRepository;

    @Override
    public TermoReferenciaRepository getRepository() {
        return repository;
    }

    @Override
    public TermoReferencia save(TermoReferencia entity) throws AppException {
        beforeSave(entity);
        if (entity.getId() != null) {
            beforeUpdate(entity);
        }
        if (Boolean.TRUE.equals(entity.getIsFinalizado())) {
            checarSeJahExiste(entity);
        }
        validar(entity);
        List<ArquivoTermoReferenciaDTO> arquivos = entity.getArquivosTemporarios();
        if (arquivos != null) {
            validarArquivos(arquivos, entity);
        }
        TermoReferencia savedEntity = getRepository().save(entity);
        if (arquivos != null) {
            saveArquivos(arquivos, savedEntity);
        }
        return savedEntity;
    }

    @Override
    public TermoReferencia saveSkippingFiles(TermoReferencia entity) throws AppException {
        beforeSave(entity);
        if (entity.getId() != null) {
            beforeUpdate(entity);
        }
        if (Boolean.TRUE.equals(entity.getIsFinalizado())) {
            checarSeJahExiste(entity);
        }
        validar(entity);
        TermoReferencia savedEntity = getRepository().save(entity);
        return savedEntity;
    }

    @Override
    protected void beforeUpdate(TermoReferencia entity) {
        for (TermoReferenciaSecao termo : entity.getSecoes()) {
            if (termo.getId() == null) {
                Secao persistedSecao = secaoRepository.save(termo.getSecao());
                termo.setSecao(persistedSecao);
            }
        }
        super.beforeUpdate(entity);
    }

    @Override
    protected void validateCollectionsRemoval(TermoReferencia termoReferencia, TermoReferencia persistedTermoReferencia) {
        validateGeneratedSectionsRemoval(termoReferencia, persistedTermoReferencia);
        validateSectionsRemoval(termoReferencia, persistedTermoReferencia);
        validateLoteRemoval(termoReferencia, persistedTermoReferencia);
    }

    private void validateGeneratedSectionsRemoval(TermoReferencia termoReferencia, TermoReferencia persistedTermoReferencia) {
        if (!persistedTermoReferencia.getSecoesCriadas().equals(termoReferencia.getSecoesCriadas())) {
            List<Long> newIdList = termoReferencia.getSecoesCriadas().stream().map(AbstractIdentificavel::getId).collect(Collectors.toList());
            for (Long persistedId : persistedTermoReferencia.getSecoesCriadas().stream().map(AbstractIdentificavel::getId).collect(Collectors.toList())) {
                if (!newIdList.contains(persistedId)) {
                    List<TermoReferenciaSecao> termoReferenciaSecoes = termoReferenciaSecaoRepository.findBySecaoGeradaId(persistedId);
                    if (termoReferenciaSecoes.size() > 1) {
                        throw new AppException("Seção gerada está associada a mais de um termo de referência.", HttpStatus.UNPROCESSABLE_ENTITY);
                    }
                    termoReferenciaSecaoRepository.deleteAll(termoReferenciaSecoes);
                    secaoRepository.deleteById(persistedId);
                }
            }
        }
    }

    private void validateSectionsRemoval(TermoReferencia termoReferencia, TermoReferencia persistedTermoReferencia) {
        List<TermoReferenciaSecao> termoReferenciaSecoes = termoReferencia.getSecoes();
        List<TermoReferenciaSecao> termoReferenciaSecoesPersisted = persistedTermoReferencia.getSecoes().stream().filter(ptr -> !ptr.getSecao().getGeradaTermo()).collect(Collectors.toList());
        if (!termoReferenciaSecoesPersisted.equals(termoReferenciaSecoes)) {
            List<Long> newIdList = termoReferenciaSecoes.stream().map(AbstractIdentificavel::getId).collect(Collectors.toList());
            for (Long persistedId : termoReferenciaSecoesPersisted.stream().map(AbstractIdentificavel::getId).collect(Collectors.toList())) {
                if (!newIdList.contains(persistedId)) {
                    termoReferenciaSecaoRepository.deleteById(persistedId);
                }
            }
        }
    }

    private void validateLoteRemoval(TermoReferencia termoReferencia, TermoReferencia persistedTermoReferencia) {
        if (!persistedTermoReferencia.getLotes().equals(termoReferencia.getLotes())) {
            List<Long> newIdList = termoReferencia.getLotes().stream().map(AbstractIdentificavel::getId).collect(Collectors.toList());
            for (Long persistedId : persistedTermoReferencia.getLotes().stream().map(AbstractIdentificavel::getId).collect(Collectors.toList())) {
                if (!newIdList.contains(persistedId)) {
                    loteRepository.deleteById(persistedId);
                }
            }
        }
    }

    @Override
    protected void beforeSave(TermoReferencia entity) {

        if (entity.getId() == null && !(getRepository().getTermoByIdentificadorProcessoAndEntidade(entity.getIdentificadorProcesso(), entity.getEntidade().getId()).isEmpty())) {
                throw new AppException("Termo de Referência não pôde ser salvo. Já existe um Termo de Referência na entidade com o mesmo identificador.", HttpStatus.UNPROCESSABLE_ENTITY);
        }

        LocalDateTime time = LocalDateTime.now();
        Usuario user = getCurrentUser();

        if (entity.getId() == null) {
            entity.setDataCadastro(time);
            entity.setUsuario(user);
        }

        for (Secao secao : entity.getSecoesCriadas()) {
            secao.setTermoReferencia(entity);
        }

        for (TermoReferenciaSecao item : entity.getSecoes()) {
            item.setTermoReferencia(entity);
            if (item.getSecao().getId() == null && item.getSecao().getGeradaTermo()) {
                item.setSecao(entity.getSecoesCriadas().stream()
                        .filter(secao -> secao.getTitulo().equals(item.getSecao().getTitulo()))
                        .findFirst()
                        .orElse(null));
            }
        }

        for (Lote lote : entity.getLotes()) {
            for (ItemLote item : lote.getItens()) {
                if (item.getId() == null) {
                    item.setUsuario(user);
                }
            }
        }
    }

    public TermoReferencia finalizarTermo(TermoReferencia entity, String processo) throws AppException {
        String resultMessage = processo.equals("Licitação") ? "publicada" : "salva";
        for (Lote lote : entity.getLotes()) {
            for (ItemLote item : lote.getItens()) {
                if (item.getValorUnitarioEstimado() == null) {
                    throw new AppException(processo + " não pôde ser " + resultMessage + " - Todos os itens do Termo de Referência devem ter valor preenchido.", HttpStatus.UNPROCESSABLE_ENTITY);

                }
            }
        }

        Example<TermoReferencia> exemplo = obterExemploChecarSeJahExisteFinalizar(entity);
        if (getRepository().exists(exemplo)) {
            throw new AppException(processo + " não pôde ser " + resultMessage + " - Já existe outro Termo de Referência com o mesmo identificador para esta entidade.", HttpStatus.UNPROCESSABLE_ENTITY);
        }

        entity.setIsFinalizado(true);

        Integer maiorOrdem = 0;

        for (TermoReferenciaSecao itemSecao : entity.getSecoes()) {
            if (itemSecao.getSecao().getOrdem() != null && itemSecao.getSecao().getOrdem() > maiorOrdem) {
                maiorOrdem = itemSecao.getSecao().getOrdem();
            }
        }

        for (Secao secaoCriada : entity.getSecoesCriadas()) {
            secaoCriada.setOrdem(secaoCriada.getOrdem() + maiorOrdem + 1);
        }

        for (TermoReferenciaSecao itemSecao : entity.getSecoes()) {
            if (Boolean.FALSE.equals(itemSecao.getSecao().getGeradaTermo())) {
                Secao antigaSecao = itemSecao.getSecao();
                Secao novaSecao = new Secao(antigaSecao.getTitulo(), antigaSecao.getObrigatoria(), antigaSecao.getOrdem(), antigaSecao.getNotasExplicativas(), entity, true);
                novaSecao = secaoRepository.save(novaSecao);
                entity.getSecoesCriadas().add(novaSecao);
                itemSecao.setSecao(novaSecao);
            }
        }

        getRepository().save(entity);
        return entity;
    }

    @Override
    @Transactional
    public TermoReferencia cloneById(Long id, String identificador) throws AppException {
        TermoReferencia original = getById(id);
        if (!getRepository().getTermoByIdentificadorProcessoAndEntidade(identificador, original.getEntidade().getId()).isEmpty()) {
            throw new AppException("Termo de Referência não pôde ser clonado. Já existe um Termo de Referência com o mesmo identificador.", HttpStatus.UNPROCESSABLE_ENTITY);
        }
        TermoReferencia copia = new TermoReferencia(
                null,
                original.getEntidade(),
                null,
                identificador,
                false,
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptyList(),
                original.getSrp(),
                original.getObraEngenharia(),
                original.getTresCasasDecimais(),
                original.getFormaPreenchimentoSecao(),
                false,
                original.getTipo(),
                Collections.emptyList(),
                null,
                original.getCanCreateReqModificacao(),
                null);

        copia = this.saveSkippingFiles(copia);

        List<TermoReferenciaSecao> itensSecao = new ArrayList<>();
        List<Lote> lotes = new ArrayList<>();
        List<Secao> secoesCriadas = new ArrayList<>();

        for (Secao secao : original.getSecoesCriadas()) {
            Secao secaoClone = secaoRepository.save(new Secao(
                    secao.getTitulo(),
                    secao.getObrigatoria(),
                    secao.getOrdem(),
                    secao.getNotasExplicativas(),
                    copia,
                    secao.getGeradaTermo()));
            secoesCriadas.add(secaoClone);
        }

        for (TermoReferenciaSecao item : original.getSecoes()) {
            if (Boolean.TRUE.equals(item.getSecao().getGeradaTermo())) {
                itensSecao.add(new TermoReferenciaSecao(
                        copia,
                        secoesCriadas.stream()
                                .filter(secaoin -> secaoin.getTitulo().equals(item.getSecao().getTitulo()))
                                .findFirst()
                                .orElse(null),
                        item.getValor()));
            } else {
                itensSecao.add(new TermoReferenciaSecao(
                        copia,
                        item.getSecao(),
                        item.getValor()));
            }
        }

        for (Lote lote : original.getLotes()) {
            Lote copiaLote = new Lote(lote.getNome(), copia, new ArrayList<>(), lote.getGerado());

            for (ItemLote itemLote : lote.getItens()) {
                copiaLote.getItens().add(new ItemLote(
                                        copiaLote,
                                        itemLote.getMaterialDetalhamento(),
                                        itemLote.getQuantidade(),
                                        itemLote.getDescricaoComplementar(),
                                        copia.getUsuario(),
                                        LocalDateTime.now(),
                                        itemLote.getValorUnitarioEstimado(),
                                        itemLote.getQuantidadeConsumo(),
                                        itemLote.getNumero(),
                        itemLote.getFracionario(),
                        itemLote.getUnidadeMedida()
                                ));
            }

            lotes.add(copiaLote);
        }

        List<ArquivoTermoReferenciaDTO> arquivos = this.recuperarArquivos(id);
        List<ArquivoTermoReferenciaDTO> arquivosTemp = new ArrayList<>();
        for (ArquivoTermoReferenciaDTO arquivo : arquivos) {
            ArquivoBinarioDTO arquivoBinarioDTO = this.download(arquivo.getArquivo());
            MultipartFile multipartFile = new MockMultipartFile(arquivoBinarioDTO.getNomeOriginal(), arquivoBinarioDTO.getNomeOriginal(), arquivo.getArquivo().getTipoArquivo(), arquivoBinarioDTO.getBinario());
            ArquivoDTO arquivoDTO = this.upload(multipartFile);
            arquivo.setIdArquivo(null);

            ArquivoTermoReferenciaDTO newArquivo = new ArquivoTermoReferenciaDTO();
            newArquivo.setArquivo(arquivoDTO);
            newArquivo.setDescricao(arquivo.getDescricao());
            newArquivo.setTipo(arquivo.getTipo());
            arquivosTemp.add(newArquivo);
        }
        copia.setArquivosTemporarios(arquivosTemp);
        copia.setSecoes(itensSecao);
        copia.setLotes(lotes);
        return this.save(copia);
    }

    @Override
    protected Specification<TermoReferencia> getSpecification(TermoReferenciaFiltroRequest filtro) {
        return new TermoReferenciaSpecification(filtro);
    }

    protected Example<TermoReferencia> obterExemploChecarSeJahExisteFinalizar(TermoReferencia entity) throws AppException {
        TermoReferencia exemplo = TermoReferencia.builder()
                .identificadorProcesso(entity.getIdentificadorProcesso())
                .entidade(entity.getEntidade())
                .isFinalizado(true)
                .build();

        return Example.of(exemplo);
    }

    @Override
    public ArquivoTermoReferenciaService getArquivoService() {
        return arquivoTermoReferenciaService;
    }

    @Override
    public ArquivoTermoReferenciaFileService getFileService() {
        return arquivoTermoReferenciaFileService;
    }

    @Override
    public ArquivoTermoReferenciaToDtoMapper getMapper() {
        return arquivoTermoReferenciaToDtoMapper;
    }

    @Override
    protected ArquivoTermoReferencia getNewArquivo() {
        return new ArquivoTermoReferencia();
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() {
        AdvancedSearchRequest filtro = new AdvancedSearchRequest();
        this.inicializaFiltro(filtro);
        AdvancedSearchParameter arquivoObjetoParam = new AdvancedSearchParameter("objeto", SearchOperator.EQUAL_TO, Objeto.TERMO_REFERENCIA.name());
        filtro.getAndParameters().add(arquivoObjetoParam);

        return obrigatoriedadeArquivoService.buscarAdvanced(filtro).getItems();
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoTermoReferencia arquivoEntity, TermoReferencia entity, ArquivoTermoReferenciaDTO arquivoUpload) {
        arquivoEntity.setTermoReferencia(entity);
        arquivoEntity.setId(arquivoUpload.getIdArquivo());
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    private List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios(TermoReferencia termoReferencia) throws AppException {
        ArrayList<String> filtros = new ArrayList<>();
        if (termoReferencia.getObraEngenharia()) {
            filtros.add("TERMO_OBRA");
        } else {
            filtros.add("TERMO_REFERENCIA");
        }
        List<ObrigatoriedadeArquivo> arquivosObrigatorios = this.obrigatoriedadeArquivoService.getArquivosObrigatorios(Objeto.TERMO_REFERENCIA.name(), filtros);
        return arquivosObrigatorios.stream().filter(ObrigatoriedadeArquivo::getObrigatorio).collect(Collectors.toList());
    }

    @Override
    public void validarArquivos(List<ArquivoTermoReferenciaDTO> arquivos, TermoReferencia termoReferencia) {
        List<TipoArquivoTermoReferencia> tiposArquivosEnviados = extrairTiposArquivos(arquivos);
        this.verificarDescricaoTipoOutros(arquivos);
        this.verificarArquivosObrigatorios(tiposArquivosEnviados, this.getArquivosTiposObrigatorios(termoReferencia));
    }

    @Override
    public boolean termoAssociadoLicitacao(Long idTermo) {
        return getRepository().termoAssociadoLicitacao(idTermo);
    }

    public void changeBlockedTermo(List<Long> termosIds, Boolean state) {
        for (Long idTermo : termosIds) {
            TermoReferencia termoReferencia = repository.getById(idTermo);
            termoReferencia.setBlockedInProcessReqMod(state);
            repository.save(termoReferencia);
        }
    }

    public void changeFinalizadoTermo(List<Long> termosIds, Boolean state) {
        for (Long idTermo : termosIds) {
            TermoReferencia termoReferencia = repository.getById(idTermo);
            termoReferencia.setIsFinalizado(state);
            repository.save(termoReferencia);
        }
    }

        @Override
    public ProcessoResponseDTO getProcessoAssociado(Long idTermoReferencia) {
        ProcessoResponseDTO processoAssociado = new ProcessoResponseDTO();
        String idProcessoLic = licitacaoRepository.getTermoAssociado(idTermoReferencia);
        if (idProcessoLic != null) {
            processoAssociado.setTipoProcesso("L");
            processoAssociado.setIdProcesso(idProcessoLic);
        }
        String idProcessoCarona = caronaRepository.getTermoAssociado(idTermoReferencia);
        if (idProcessoCarona != null) {
            processoAssociado.setTipoProcesso("C");
            processoAssociado.setIdProcesso(idProcessoCarona);
        }
        String idProcessoDis = dispensaRepository.getTermoAssociado(idTermoReferencia);
        if (idProcessoDis != null) {
            processoAssociado.setTipoProcesso("D");
            processoAssociado.setIdProcesso(idProcessoDis);
        }
        String idProcessoInex = inexigibilidadeRepository.getTermoAssociado(idTermoReferencia);
        if (idProcessoInex != null) {
            processoAssociado.setTipoProcesso("I");
            processoAssociado.setIdProcesso(idProcessoInex);
        }
        String idProcessoCred = credenciamentoRepository.getTermoAssociado(idTermoReferencia);
        if (idProcessoCred != null) {
            processoAssociado.setTipoProcesso("CR");
            processoAssociado.setIdProcesso(idProcessoCred);
        }
        return processoAssociado;
    }
}
