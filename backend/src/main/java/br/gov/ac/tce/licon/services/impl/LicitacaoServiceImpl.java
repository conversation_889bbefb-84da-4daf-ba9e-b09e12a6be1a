package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.ArquivoDTO;
import br.gov.ac.tce.licon.dtos.EmailDTO;
import br.gov.ac.tce.licon.dtos.mapper.ArquivoLicitacaoToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.*;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchParameter;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.SearchOperator;
import br.gov.ac.tce.licon.dtos.responses.BuscaResponse;
import br.gov.ac.tce.licon.entities.*;
import br.gov.ac.tce.licon.entities.enums.*;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.*;
import br.gov.ac.tce.licon.services.*;
import br.gov.ac.tce.licon.services.specs.LicitacaoSpecification;
import br.gov.ac.tce.licon.utils.GeradorMensagemEmail;
import com.j256.simplemagic.ContentType;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class LicitacaoServiceImpl extends AbstractUploadTipoServiceImpl<Licitacao, LicitacaoFiltroRequest, LicitacaoRepository, ArquivoLicitacaoFileService, ArquivoLicitacao, ArquivoLicitacaoFiltroRequest, ArquivoLicitacaoService, ArquivoLicitacaoDTO, ArquivoLicitacaoToDtoMapper, TipoArquivoLicitacao> implements LicitacaoService {

    @Inject
    private ArquivoLicitacaoService arquivoLicitacaoService;

    @Inject
    private PublicacaoService publicacaoService;

    @Inject
    private ArquivoLicitacaoFileService arquivoLicitacaoFileService;

    @Autowired
    private ItemLoteFracassadoRepository itemLoteFracassadoRepository;

    @Autowired
    private ItemDesertoRepository itemDesertoRepository;

    @Inject
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Autowired
    private LicitacaoRepository repository;

    @Autowired
    private VencedorLicitacaoRepository vencedorLicitacaoRepository;

    @Autowired
    private OcorrenciaLicitacaoRepository ocorrenciaLicitacaoRepository;

    @Autowired
    private PublicacaoRepository publicacaoRepository;

    @Autowired
    private ObraRepository obraRepository;

    @Autowired
    private ItemRepository itemRepository;

    @Autowired
    private EdificacaoService edificacaoService;

    @Inject
    private ArquivoLicitacaoToDtoMapper arquivoLicitacaoToDtoMapper;

    @Autowired
    private TdaLicitacaoRepository tdaRepository;

    @Autowired
    private IEmailService emailService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private TermoReferenciaServiceImpl termoReferenciaService;

    @Autowired
    private AnaliseProcessoViewServiceImpl analiseProcessoViewService;

    @Autowired
    private TermoReferenciaRepository termoReferenciaRepository;

    @Override
    public LicitacaoRepository getRepository() {
        return repository;
    }

    @Override
    public ArquivoLicitacaoService getArquivoService() {
        return arquivoLicitacaoService;
    }

    @Override
    public ArquivoLicitacaoFileService getFileService() {
        return arquivoLicitacaoFileService;
    }

    @Override
    public ArquivoLicitacaoToDtoMapper getMapper() {
        return arquivoLicitacaoToDtoMapper;
    }

    @Override
    protected Specification<Licitacao> getSpecification(LicitacaoFiltroRequest filtro) {
        return new LicitacaoSpecification(filtro);
    }

    @Override
    protected void beforeSave(Licitacao entity) {
        if (entity.getId() == null) {
            LocalDate dataLimiteAbertura = LocalDate.now().plusYears(1);
            LocalDate dataAbertura = entity.getDataAbertura().toLocalDate();
            if (dataAbertura.isAfter(dataLimiteAbertura)) {
                throw new AppException("Data de Abertura inválida.", HttpStatus.UNPROCESSABLE_ENTITY);
            }
            entity.setDataCadastroPreparatoria(LocalDateTime.now());
            entity.setUsuario(getCurrentUser());
            entity.setFase(FaseLicitacao.PREPARATORIA);
            entity.setStatus(StatusLicitacao.NAO_PUBLICADA);
        }
    }

    @Override
    protected void validateCollectionsRemoval(Licitacao object, Licitacao persistedObject) {
        validateOcorrenciasRemoval(object, persistedObject);
        validatePublicacoesRemoval(object, persistedObject);
        if (object.getFase().equals(FaseLicitacao.PREPARATORIA)) {
            validateObrasRemoval(object, persistedObject);
        }
    }

    private void validateOcorrenciasRemoval(Licitacao object, Licitacao persistedObject) {
        if (!persistedObject.getOcorrencias().equals(object.getOcorrencias()) && persistedObject.getOcorrencias() != null) {
            List<Long> newIdList = new ArrayList<>();
            if (object.getOcorrencias() != null) {
                newIdList.addAll(object.getOcorrencias().stream().map(AbstractIdentificavel::getId).collect(Collectors.toList()));
            }
            for (Long persistedId : persistedObject.getOcorrencias().stream().map(AbstractIdentificavel::getId).collect(Collectors.toList())) {
                if (!newIdList.contains(persistedId)) {
                    ocorrenciaLicitacaoRepository.deleteById(persistedId);
                }
            }
        }
    }

    private void validatePublicacoesRemoval(Licitacao object, Licitacao persistedObject) {
        if (!persistedObject.getPublicacoes().equals(object.getPublicacoes()) && persistedObject.getPublicacoes() != null) {
            List<Long> newIdList = new ArrayList<>();
            if (object.getPublicacoes() != null) {
                newIdList.addAll(object.getPublicacoes().stream().map(AbstractIdentificavel::getId).collect(Collectors.toList()));
            }
            for (Long persistedId : persistedObject.getPublicacoes().stream().map(AbstractIdentificavel::getId).collect(Collectors.toList())) {
                if (!newIdList.contains(persistedId)) {
                    publicacaoRepository.deleteById(persistedId);
                }
            }
        }
    }

    private void validateObrasRemoval(Licitacao object, Licitacao persistedObject) {
        if (persistedObject.getObra() != null && (object.getObra() == null || !persistedObject.getObra().equals(object.getObra()))) {
            obraRepository.deleteById(persistedObject.getObra().getId());
        }
    }

    @Override
    public void remover(Long id) throws AppException {
        Optional<Licitacao> entidadeOpt = getRepository().findById(id);
        if (entidadeOpt.isPresent()) {
            Licitacao lic = entidadeOpt.get();
            validarRemover(lic);
            lic.setStatus(StatusLicitacao.REMOVIDA);
            this.save(lic);
        }
    }

    @Override
    public BuscaResponse<Licitacao> buscarAdvanced(AdvancedSearchRequest filtro) {
        setDefaultFilterParams(filtro);
        BuscaResponse<Licitacao> buscaResponse = super.buscarAdvanced(filtro);
        String[] groups = ThreadContext.get("groups").replace("[", "").replace("]", "").split(",");

        if (Arrays.asList(groups).contains("Auditor")) {
            buscaResponse.getItems().forEach(licitacao -> licitacao.setRisco(licitacao.getValorDeRisco()));
        }

        return buscaResponse;
    }

    @Override
    protected void setDefaultFilterParams(AdvancedSearchRequest filtro) {
        AdvancedSearchParameter situacaoParam = new AdvancedSearchParameter("status", SearchOperator.NOT_EQUAL_TO, StatusLicitacao.REMOVIDA.name());
        filtro.getAndParameters().add(situacaoParam);
    }

    @Override
    public void cadastrar(CadastroLicitacaoDTO dto) throws AppException {
        Licitacao licitacao = dto.getLicitacao();
        List<ArquivoLicitacaoDTO> arquivos = dto.getArquivosLicitacao();
        arquivos.forEach(arq -> {
            if (arq.getFase() == null && arq.getIdArquivo() == null) {
                arq.setFase(FaseLicitacao.PREPARATORIA);
            }
        });

        validarArquivos(arquivos, FaseLicitacao.PREPARATORIA, licitacao.getLei(), false, licitacao.getNaturezasDoObjeto().stream()
                .anyMatch(natureza -> natureza.toString().equals("OBRAS"))
        );

        Edificacao edificacao = null;
        Obra obra = null;

        if (dto.getObra() != null) {
            obra = licitacao.getObra();
            edificacao = obra.getEdificacao() != null ? obra.getEdificacao() : new Edificacao();
            edificacao.setGeometry(dto.getObra().getTipoCamada(), dto.getObra().getCoordenadas());
            edificacao.setObra(obra);
            obra.setEdificacao(edificacao);
            licitacao.setObra(obra);
            licitacao.getObra().setLicitacao(licitacao);
        } else if (dto.getEdificacao() != null) {
            edificacao = dto.getEdificacao();
            obra = licitacao.getObra();
            obra.setEdificacao(edificacao);
            edificacao.setObra(obra);
            licitacao.setObra(obra);
            licitacao.getObra().setLicitacao(licitacao);
        }

        if (licitacao.getId() == null && licitacao.getTermoReferencia() != null && termoReferenciaRepository.checkAssociatedTerm(licitacao.getTermoReferencia().getId())) {
            throw new AppException("A operação não pôde ser concluída, pois o Termo de Referência escolhido já está associado a algum processo.", HttpStatus.UNPROCESSABLE_ENTITY);
        }

        if (licitacao.getTermoReferencia() != null && licitacao.getTermoReferencia().getIdentificadorProcesso() == null) {
            throw new AppException("A operação não pôde ser concluída, pois o Termo de Referência escolhido deve possuir um identificador.", HttpStatus.UNPROCESSABLE_ENTITY);
        }

        licitacao = save(licitacao, FaseLicitacao.PREPARATORIA, getCurrentUser());
        saveArquivos(arquivos, licitacao);
    }

    @Override
    protected Example<Licitacao> obterExemploChecarSeJahExiste(Licitacao entity) throws AppException {
        Licitacao exemplo = null;
        exemplo = Licitacao.builder().numero(entity.getNumero()).ano(entity.getAno()).entidade(entity.getEntidade()).build();

        return Example.of(exemplo);
    }

    @Override
    protected void lancarErroEntidadeJahExistente(Licitacao entity) throws AppException {
        String msg = String.format("Licitação com número %s/%s já existe para a entidade %s.", entity.getNumero(), entity.getAno(), entity.getEntidade().getNome());
        throw new AppException(msg, HttpStatus.UNPROCESSABLE_ENTITY);
    }

    @Override
    public Licitacao save(Licitacao entity, FaseLicitacao faseLicitacao, Usuario usuario) throws AppException {
        beforeSave(entity);
        resolverRelacionamentos(entity);
        checarSeJahExiste(entity);
        validar(entity);
        if (entity.getId() != null) {
            beforeUpdate(entity);
        }
        boolean isNew = entity.getId() == null;
        entity.setUpdatedAt(LocalDateTime.now());
        entity.setUserUpdated(usuario);
        Licitacao licitacao = getRepository().saveAndFlush(entity);
        if (FaseLicitacao.DIVULGACAO_PUBLICACAO_LICITACAO.equals(faseLicitacao)) {
            this.asyncService.analisaProcesso(licitacao.getId(), "L");
        }
        afterSave(isNew, licitacao);
        return entity;
    }

    @Override
    public void publicar(LicitacaoDTO dto) throws AppException {
        Licitacao licitacao = getById(dto.getId());
        List<ArquivoLicitacaoDTO> arquivos = dto.getArquivosLicitacao();
        arquivos.forEach(arq -> {
            if (arq.getFase() == null && arq.getIdArquivo() == null) {
                arq.setFase(FaseLicitacao.DIVULGACAO_PUBLICACAO_LICITACAO);
            }
        });

        validarArquivos(arquivos, FaseLicitacao.DIVULGACAO_PUBLICACAO_LICITACAO, licitacao.getLei(), false, false);

        List<Publicacao> publicacoes = dto.getPublicacoesLicitacao();
        this.publicacaoService.deletePublicacoesRemovidas(licitacao.getId(), publicacoes);

        for (Publicacao publicacao : publicacoes) {
            publicacao.setLicitacao(licitacao);
            this.publicacaoService.save(publicacao);
        }

        if (licitacao.getFase() == null || licitacao.getFase().equals(FaseLicitacao.PREPARATORIA)) {
            licitacao.setFase(FaseLicitacao.DIVULGACAO_PUBLICACAO_LICITACAO);
            licitacao.setDataCadastro(LocalDateTime.now());
            licitacao.setUserCreatedPublicacao(getCurrentUser());
        }

        TermoReferencia termoReferencia = licitacao.getTermoReferencia();
        if (termoReferencia != null && !termoReferencia.getIsFinalizado()) {
            termoReferencia = termoReferenciaService.finalizarTermo(termoReferencia, "Licitação");
            licitacao.setTermoReferencia(termoReferencia);
        }

        licitacao.setStatus(StatusLicitacao.PUBLICADA);

        licitacao = save(licitacao, FaseLicitacao.DIVULGACAO_PUBLICACAO_LICITACAO, licitacao.getUsuario());

        saveArquivos(arquivos, licitacao);
    }

    @Transactional
    @Override
    public void salvarVencedor(LicitacaoVencedorDTO licitacaoVencedorDTO) throws AppException {
        Licitacao licitacaoPersistida = getById(licitacaoVencedorDTO.getId());
        atualizarFaseEDataLicitacao(licitacaoVencedorDTO, licitacaoPersistida);
        validarEConfigurarArquivos(licitacaoVencedorDTO.getArquivosLicitacao(), licitacaoPersistida.getLei());
        finalizarEConfigurarTermoReferencia(licitacaoVencedorDTO, licitacaoPersistida);
        atualizarVencedores(licitacaoVencedorDTO, licitacaoPersistida);
        atualizarLotesFracassados(licitacaoVencedorDTO, licitacaoPersistida);
        atualizarItensDesertos(licitacaoVencedorDTO, licitacaoPersistida);
        atualizarLicitantes(licitacaoVencedorDTO, licitacaoPersistida);
        licitacaoPersistida.setTipoAdjudicacao(licitacaoVencedorDTO.getTipoAdjudicacao());
        licitacaoPersistida.setValorAdjudicado(licitacaoVencedorDTO.getValorAdjudicado());
        licitacaoPersistida = save(licitacaoPersistida, FaseLicitacao.APRESENTACAO_PROPOSTAS_LANCES, licitacaoPersistida.getUsuario());
        saveArquivos(licitacaoVencedorDTO.getArquivosLicitacao(), licitacaoPersistida);
    }

    private void atualizarFaseEDataLicitacao(LicitacaoVencedorDTO licitacaoVencedorDTO, Licitacao licitacaoPersistida) {
        if (estaEmFasePublicacao(licitacaoVencedorDTO)) {
            licitacaoPersistida.setFase(FaseLicitacao.APRESENTACAO_PROPOSTAS_LANCES);
            licitacaoPersistida.setDataCadastroVencedores(LocalDateTime.now());
        }
    }

    private boolean estaEmFasePublicacao(LicitacaoVencedorDTO licitacaoVencedorDTO) {
        return licitacaoVencedorDTO.getFaseLicitacao() == null || licitacaoVencedorDTO.getFaseLicitacao().equals(FaseLicitacao.DIVULGACAO_PUBLICACAO_LICITACAO);
    }

    private void validarEConfigurarArquivos(List<ArquivoLicitacaoDTO> arquivos, String lei) {
        arquivos.forEach(this::configurarFaseArquivoSeNecessario);
        validarArquivos(arquivos, FaseLicitacao.APRESENTACAO_PROPOSTAS_LANCES, lei, false, false);
    }

    private void configurarFaseArquivoSeNecessario(ArquivoLicitacaoDTO arquivo) {
        if (arquivo.getFase() == null && arquivo.getIdArquivo() == null) {
            arquivo.setFase(FaseLicitacao.APRESENTACAO_PROPOSTAS_LANCES);
        }
    }

    private void finalizarEConfigurarTermoReferencia(LicitacaoVencedorDTO licitacaoVencedorDTO, Licitacao licitacaoPersistida) {
        if (licitacaoVencedorDTO.getIdTermoReferencia() != null) {
            TermoReferencia termoReferencia = termoReferenciaService.getById(licitacaoVencedorDTO.getIdTermoReferencia());
            if (termoReferencia != null && !termoReferencia.getIsFinalizado()) {
                termoReferencia = termoReferenciaService.finalizarTermo(termoReferencia, "Licitação");
                licitacaoPersistida.setTermoReferencia(termoReferencia);
            }
        }
    }

    private void atualizarVencedores(LicitacaoVencedorDTO licitacaoVencedorDTO, Licitacao licitacaoPersistida) {
        licitacaoPersistida.getVencedores().removeIf(vencedorLicitacao -> !licitacaoVencedorDTO.getVencedoresLicitacao().contains(vencedorLicitacao));

        for (VencedorLicitacao vencedorLicitacao : licitacaoVencedorDTO.getVencedoresLicitacao()) {
            if (!licitacaoPersistida.getVencedores().contains(vencedorLicitacao)) {
                vencedorLicitacao.setLicitacao(licitacaoPersistida);
                VencedorLicitacao vencedorLicitacaoPersisted = vencedorLicitacaoRepository.save(vencedorLicitacao);
                licitacaoPersistida.getVencedores().add(vencedorLicitacaoPersisted);
            }
        }
    }

    private void atualizarLotesFracassados(LicitacaoVencedorDTO licitacaoVencedorDTO, Licitacao licitacaoPersistida) {
        licitacaoPersistida.getLotesFracassados().removeIf(loteFracassado -> !licitacaoVencedorDTO.getLotesFracassados().contains(loteFracassado));

        for (ItemLoteFracassado loteFracassado : licitacaoVencedorDTO.getLotesFracassados()) {
            if (!licitacaoPersistida.getLotesFracassados().contains(loteFracassado)) {
                loteFracassado.setLicitacao(licitacaoPersistida);
                ItemLoteFracassado lotePersistido = itemLoteFracassadoRepository.save(loteFracassado);
                licitacaoPersistida.getLotesFracassados().add(lotePersistido);
            }
        }
    }

    private void atualizarItensDesertos(LicitacaoVencedorDTO licitacaoVencedorDTO, Licitacao licitacaoPersistida) {
        licitacaoPersistida.getItensDesertos().removeIf(itemDeserto -> !licitacaoVencedorDTO.getItensDesertos().contains(itemDeserto));

        for (ItemDeserto itemDeserto : licitacaoVencedorDTO.getItensDesertos()) {
            if (!licitacaoPersistida.getItensDesertos().contains(itemDeserto)) {
                itemDeserto.setLicitacao(licitacaoPersistida);
                ItemDeserto itemPersistido = itemDesertoRepository.save(itemDeserto);
                licitacaoPersistida.getItensDesertos().add(itemPersistido);
            }
        }
    }

    private VencedorLicitacao salvarVencedorLicitacao(VencedorLicitacao vencedor) {
        return vencedorLicitacaoRepository.save(vencedor);
    }

    private void atualizarLicitantes(LicitacaoVencedorDTO licitacaoVencedorDTO, Licitacao licitacaoPersistida) {
        removerLicitantesNaoCorrespondentes(licitacaoVencedorDTO, licitacaoPersistida);
        adicionarNovosLicitantes(licitacaoVencedorDTO, licitacaoPersistida);
    }

    private void removerLicitantesNaoCorrespondentes(LicitacaoVencedorDTO licitacaoVencedorDTO, Licitacao licitacaoPersistida) {
        licitacaoPersistida.getLicitantes().removeIf(licitante -> !licitacaoVencedorDTO.getLicitantes().contains(licitante));
    }

    private void adicionarNovosLicitantes(LicitacaoVencedorDTO licitacaoVencedorDTO, Licitacao licitacaoPersistida) {
        for (Licitante licitante : licitacaoVencedorDTO.getLicitantes()) {
            if (!licitacaoPersistida.getLicitantes().contains(licitante)) {
                licitacaoPersistida.getLicitantes().add(licitante);
            }
        }
    }

    public void finalizar(LicitacaoDTO dto) throws AppException {
        Licitacao licitacao = getById(dto.getId());
        if (licitacao.getDataAbertura().isAfter(LocalDateTime.now())) {
            throw new AppException("A operação não pôde ser concluída, pois a data de abertura da licitação ainda não foi alcançada.", HttpStatus.UNPROCESSABLE_ENTITY);
        }

        if (licitacao.getFase() == null || licitacao.getFase().equals(FaseLicitacao.APRESENTACAO_PROPOSTAS_LANCES)) {
            licitacao.setFase(FaseLicitacao.FINALIZACAO);
            licitacao.setDataCadastroFinalizacao(LocalDateTime.now());
        }

        List<ArquivoLicitacaoDTO> arquivos = dto.getArquivosLicitacao();
        arquivos.forEach(arq -> {
            if (arq.getFase() == null && arq.getIdArquivo() == null) {
                arq.setFase(FaseLicitacao.FINALIZACAO);
            }
        });

        validarArquivos(arquivos, FaseLicitacao.FINALIZACAO, licitacao.getLei(), licitacao.getLei().equals("LEI_N_8666") ? licitacao.getSrp() != null && licitacao.getSrp() : licitacao.getTermoReferencia().getSrp() != null && licitacao.getTermoReferencia().getSrp(), false);

        licitacao = save(licitacao, FaseLicitacao.FINALIZACAO, licitacao.getUsuario());

        saveArquivos(arquivos, licitacao);
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() throws AppException {
        AdvancedSearchRequest filtro = new AdvancedSearchRequest();
        this.inicializaFiltro(filtro);
        AdvancedSearchParameter arquivoObjetoParam = new AdvancedSearchParameter("objeto", SearchOperator.EQUAL_TO, Objeto.LICITACAO.name());
        filtro.getAndParameters().add(arquivoObjetoParam);

        return obrigatoriedadeArquivoService.buscarAdvanced(filtro).getItems();
    }

    private List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios(FaseLicitacao fase, String lei, Boolean srp, Boolean naturezaObjObra) throws AppException {
        ArrayList<String> filtros = new ArrayList<>();
        filtros.add(lei);
        filtros.add(fase.name());
        if (srp) {
            filtros.add("SRP");
        }
        if (naturezaObjObra) {
            filtros.add("OBRAS");
        }
        List<ObrigatoriedadeArquivo> obrigatoriedadeArquivosFaseAtual = this.obrigatoriedadeArquivoService.getArquivosObrigatorios("LICITACAO", filtros);
        return obrigatoriedadeArquivosFaseAtual.stream().filter(ObrigatoriedadeArquivo::getObrigatorio).collect(Collectors.toList());
    }

    private Boolean checkXlsMimeTypes(String mimeType) {
        List<ContentType> types = Arrays.asList(ContentType.MICROSOFT_EXCEL,
                ContentType.MICROSOFT_EXCEL_XML,
                ContentType.ZIP,
                ContentType.PDF);
        return types.stream().anyMatch(type -> type.getMimeType().equals(mimeType));
    }

    public void validarArquivos(List<ArquivoLicitacaoDTO> arquivos, FaseLicitacao fase, String lei, Boolean srp, Boolean naturezaObjObra) {
        List<TipoArquivoLicitacao> tiposArquivosEnviados = extrairTiposArquivos(arquivos);
        this.validarArquivosTipos(tiposArquivosEnviados, fase, lei, srp, naturezaObjObra);
        this.verificarDescricaoTipoOutros(arquivos);

        if (arquivos.stream().anyMatch(arquivo -> arquivo.getTipo().equals(TipoArquivoLicitacao.MAPA_LANCES) && !checkXlsMimeTypes(arquivo.getArquivo().getTipoArquivo()))) {
            throw new AppException("O arquivo do tipo 'Mapa de Lances' deve ser uma planilha ou um PDF", HttpStatus.BAD_REQUEST);
        } else if (arquivos.stream().anyMatch(arquivo -> arquivo.getTipo().equals(TipoArquivoLicitacao.PROJETO_ENGENHARIA) && !checkTypeDWG(arquivo.getArquivo().getTipoArquivo()))) {
            throw new AppException("O arquivo do tipo 'Projetos de engenharia (básico, executivo e afins)' deve ser em formato DWG", HttpStatus.BAD_REQUEST);
        }

        List<ArquivoLicitacaoDTO> arquivoErro = arquivos.stream().filter(arquivo -> arquivo.getFase() != null
                && arquivo.getFase().equals(FaseLicitacao.APRESENTACAO_PROPOSTAS_LANCES)
                && !arquivo.getTipo().equals(TipoArquivoLicitacao.MAPA_LANCES)
                && !arquivo.getArquivo().getTipoArquivo().equals(ContentType.PDF.getMimeType())).collect(Collectors.toList());

        if (!arquivoErro.isEmpty()) {
            throw new AppException("O arquivo do tipo '" + arquivoErro.get(0).getTipo().getValor() + "' deve ser um PDF", HttpStatus.BAD_REQUEST);
        }
    }

    public void validarArquivosTipos(List<TipoArquivoLicitacao> tipos, FaseLicitacao fase, String lei, Boolean srp, Boolean naturezaObjObra) {
        this.verificarArquivosObrigatorios(tipos, this.getArquivosTiposObrigatorios(fase, lei, srp, naturezaObjObra));
    }

    @Override
    protected ArquivoLicitacao getNewArquivo() {
        return new ArquivoLicitacao();
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoLicitacao arquivoEntity, Licitacao entity, ArquivoLicitacaoDTO arquivoUpload) {
        arquivoEntity.setLicitacao(entity);
        arquivoEntity.setFase(arquivoUpload.getFase());
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    @Override
    public ArquivoLicitacaoDTO atualizarArquivo(Long idLicitacao, ArquivoLicitacaoDTO arquivoLicitacaoDTO) {
        return this.atualizarArquivo(idLicitacao, arquivoLicitacaoDTO, false);
    }

    @Override
    public List<ArquivoLicitacaoDTO> recuperarArquivos(Long idEntity) {
        return super.recuperarArquivos(idEntity);
    }

    @Override
    public UltimaAlteracaoDTO getUltimaAlteracao(Long idLicitacao) {
        List<Object[]> ultimasModificadores = repository.getUltimaAlteracao(idLicitacao);
        if (ultimasModificadores.size() > 0) {
            UltimaAlteracaoDTO response = new UltimaAlteracaoDTO();
            Object[] ultimaModificacao = ultimasModificadores.get(0);
            response.setData(((Timestamp) ultimaModificacao[1]).toLocalDateTime());
            response.setNome((String) ultimaModificacao[0]);
            return response;
        } else {
            return null;
        }
    }

    @Override
    public List<ItemContrato> itensEmContratos(Long idLicitacao) {
        return this.repository.itensEmContratos(idLicitacao);
    }

    @Override
    public List<FonteRecurso> getFontesDeRecurso(Long idLicitacao) {
        return getRepository().getFontesDeRecurso(idLicitacao);
    }

    @Override
    public List<NaturezaObjeto> getNaturezasObjeto(Long idLicitacao) {
        return getRepository().getNaturezasObjeto(idLicitacao);
    }

    @Override
    public Licitacao getById(Long idLicitacao) {
        Licitacao licitacao = super.getById(idLicitacao);
        licitacao.setTda(tdaRepository.getByIdLicitacao(idLicitacao));
        return licitacao;
    }

    @Override
    public Boolean getTresCasasDecimais(Long idLicitacao) {
        return getRepository().getTresCasasDecimais(idLicitacao);
    }

    public ArquivoBinarioDTO download(ArquivoDTO arquivoDTO, Boolean countDownloads) throws AppException {
        ArquivoBinarioDTO download = arquivoLicitacaoFileService.download(arquivoDTO);
        if (arquivoDTO.ehArquivoDefinitivoValido() && Boolean.TRUE.equals(countDownloads)) {
            Long idArquivoLicitacao = Long.parseLong(arquivoDTO.getLookupId());
            repository.updateNumeroDownloadsLicitacao(idArquivoLicitacao);
        }
        return download;
    }

    @Override
    public void setEmAnalise(Long idProcesso, Boolean emAnalise) {
        LocalDateTime dataAnalise = LocalDateTime.now();
        if (!emAnalise) {
            dataAnalise = null;
        }
        this.repository.updateEmAnalise(idProcesso, emAnalise, dataAnalise);
        this.repository.updateLastAuditTimestamp(idProcesso);
    }

    @Override
    public void desarquivarLicitacao(Long idProcesso) {
        this.repository.desarquivarLicitacao(idProcesso, LocalDateTime.now());
        this.repository.updateLastAuditTimestamp(idProcesso);
    }

    @Override
    public List<Integer> getAnosLicitacao() {
        List<Integer> years = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (int year = 2016; year <= now.getYear(); year++) {
            years.add(year);
        }
        if (now.getMonth().getValue() == 12) {
            years.add(now.getYear() + 1);
        }
        Collections.sort(years, Collections.reverseOrder());
        return years;
    }

    @Override
    public List<ArquivoLicitacao> saveArquivos(List<ArquivoLicitacaoDTO> arquivos, Licitacao entity) throws AppException {
        List<ArquivoLicitacao> result = new ArrayList<>();

        List<ArquivoLicitacaoDTO> arquivosPersistidos = recuperarArquivos(entity.getId());

        for (ArquivoLicitacaoDTO arquivoOriginal : arquivosPersistidos) {
            ArquivoLicitacaoDTO arquivoNovo = arquivos.stream().filter(arquivo -> arquivoOriginal.getIdArquivo().equals(arquivo.getIdArquivo())).findFirst().orElse(null);
            if (arquivoNovo == null) {
                removerArquivo(entity.getId(), arquivoOriginal.getIdArquivo());
            }
        }

        for (ArquivoLicitacaoDTO arquivoUpload : arquivos) {
            if (arquivoUpload.getArquivo().ehArquivoTemporarioValido()) {
                ArquivoDTO arquivoDTO = arquivoUpload.getArquivo();
                ArquivoLicitacao arquivoEntity = getNewArquivo();
                arquivoEntity.setDescricao(arquivoUpload.getDescricao());
                arquivoEntity.setFase(arquivoUpload.getFase());

                this.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);

                this.copiarParaLocalDefinitivo(entity, arquivoDTO, arquivoEntity);
                this.getArquivoService().save(arquivoEntity);
                result.add(arquivoEntity);
            } else {
                atualizarArquivo(entity.getId(), arquivoUpload);
            }
        }

        return result;
    }

    @Override
    public void verificarDescricaoTipoOutros(List<ArquivoLicitacaoDTO> arquivos) {
        List tipos = Arrays.asList("OUTROS_DOCUMENTOS_EXTERNO", "OUTROS_DOCUMENTOS_INTERNO", "OUTROS_DOCUMENTOS");
        arquivos.forEach(a -> {
            if (tipos.contains(a.getTipo().getTipo()) && (a.getDescricao() == null || a.getDescricao().isEmpty())) {
                throw new AppException("Arquivos do tipo 'Outros Documentos' devem possuir descrição.", HttpStatus.UNPROCESSABLE_ENTITY);
            }
        });
    }

    @Override
    protected void afterSave(boolean isNew, Licitacao entity) {
        if (isNew) {
            Usuario usuario = getCurrentUser();
            EmailDTO emailDTO = new EmailDTO();
            emailDTO.setUsuario(usuario.getLogin());
            emailDTO.setSistema(app);
            emailDTO.setAssunto("Confirmação de Cadastro");
            emailDTO.setDestinatarios(new HashSet<>(Collections.singletonList(usuario.getEmail())));
            String naturezas = "";
            if (entity.getNaturezasDoObjeto() != null) {
                naturezas = StringUtils.join(entity.getNaturezasDoObjeto().stream().map(n -> n.getValor()).collect(Collectors.toList()), ", ");
            }
            String mensagem = String.format(GeradorMensagemEmail.getMensagemCadastroLicitacao(), usuario.getNome(), entity.getDataCadastroPreparatoria().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")), entity.getNumero(), entity.getAno(), naturezas);
            emailDTO.setMensagem(mensagem);
            emailService.sendEmail(emailDTO);
        }
    }

    @Override
    public void arquivarProcesso(Long idLicitacao, StatusAuditoriaLicitacao status) {
        if (isUsuarioInspetor() || ThreadContext.get("groups").equals("Administrador")) {
            Optional<Licitacao> optLicitacao = getRepository().findById(idLicitacao);
            if (optLicitacao.isPresent()) {
                Licitacao licitacao = optLicitacao.get();
                licitacao.setStatusProcessoArquivado(status);
                getRepository().save(licitacao);
                getRepository().updateLastAuditTimestamp(idLicitacao);
            }
        } else {
            throw new AppException("O usuário não tem permissão para realizar a operação", HttpStatus.UNAUTHORIZED);
        }
    }

    @Override
    public void atualizaLicitacaoAssociadaAoTermo(TermoReferencia termoReferencia) {
        Licitacao licitacao = repository.findByTermoReferencia(termoReferencia);
        BigDecimal valorEstimado = BigDecimal.ZERO;
        if (termoReferencia != null && termoReferencia.getLotes() != null) {
            for (Lote lote : termoReferencia.getLotes()) {
                if (lote.getItens() != null) {
                    for (ItemLote item : lote.getItens()) {
                        if (item.getQuantidade() != null && item.getValorUnitarioEstimado() != null) {
                            valorEstimado = valorEstimado.add(item.getQuantidade().multiply(item.getValorUnitarioEstimado()));
                        }
                    }
                }
            }
        }
        licitacao.setValorEstimado(valorEstimado);
        repository.save(licitacao);
    }

    public Long getAlertaLicitacao(Long idProcesso, String tipoProcesso) {
        AnaliseProcessoView alerta = analiseProcessoViewService.getProcesso(idProcesso, tipoProcesso);
        if (alerta != null) {
            return alerta.getIdAlertaAnalise();
        }
        return null;
    }
}
