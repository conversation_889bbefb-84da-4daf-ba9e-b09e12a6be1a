package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.ArquivoDTO;
import br.gov.ac.tce.licon.dtos.mapper.ArquivoDispensaToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.*;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchParameter;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.SearchOperator;
import br.gov.ac.tce.licon.dtos.responses.BuscaResponse;
import br.gov.ac.tce.licon.entities.*;
import br.gov.ac.tce.licon.entities.enums.*;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.*;
import br.gov.ac.tce.licon.services.*;
import br.gov.ac.tce.licon.services.specs.DispensaSpecification;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class DispensaServiceImpl extends AbstractUploadTipoServiceImpl<Dispensa, DispensaFiltroRequest, DispensaRepository, ArquivoDispensaFileService, ArquivoDispensa, ArquivoDispensaFiltroRequest, ArquivoDispensaService, ArquivoDispensaDTO, ArquivoDispensaToDtoMapper, TipoArquivoDispensa> implements DispensaService {

    @Autowired
    private ArquivoDispensaService arquivoDispensaService;

    @Autowired
    private ArquivoDispensaFileService arquivoDispensaFileService;

    @Autowired
    private DispensaRepository repository;

    @Autowired
    private DispensaLicitanteRepository dispensaLicitanteRepository;

    @Inject
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Autowired
    private ArquivoDispensaToDtoMapper arquivoDispensaToDtoMapper;

    @Autowired
    private TdaDispensaRepository tdaRepository;

    @Autowired
    private AnaliseProcessoViewServiceImpl analiseProcessoViewService;

    @Override
    public DispensaRepository getRepository() {
        return repository;
    }

    @Autowired
    private TermoReferenciaService termoReferenciaService;

    @Autowired
    private TermoReferenciaRepository termoReferenciaRepository;

    @Autowired
    private ItemLoteFracassadoRepository itemLoteFracassadoRepository;

    @Autowired
    private ItemDesertoRepository itemDesertoRepository;

    @Autowired
    private ObraRepository obraRepository;

    @Autowired
    private AnulacaoRevogacaoService anulacaoRevogacaoService;

    @Override
    protected Example<Dispensa> obterExemploChecarSeJahExiste(Dispensa entity) throws AppException {
        Dispensa exemplo = Dispensa.builder().numeroProcesso(entity.getNumeroProcesso()).entidade(entity.getEntidade()).build();
        return Example.of(exemplo);
    }

    @Override
    protected void lancarErroEntidadeJahExistente(Dispensa entity) throws AppException {
        throw new AppException(String.format("Dispensa com dado número de processo já existe: %s para a entidade %s.", entity.getNumeroProcesso(), entity.getEntidade().getNome()), HttpStatus.UNPROCESSABLE_ENTITY);
    }

    @Override
    protected Specification<Dispensa> getSpecification(DispensaFiltroRequest filtro) {
        return new DispensaSpecification(filtro);
    }

    @Override
    protected void beforeSave(Dispensa entity) {
        if (entity.getId() == null) {
            entity.setDataCadastro(LocalDateTime.now());
            entity.setUsuario(getCurrentUser());
            entity.setOrigemLicon2(true);
            entity.setStatus(StatusLicitacao.PUBLICADA);
        }
    }

    @Override
    protected void validateCollectionsRemoval(Dispensa object, Dispensa persistedObject) {
        validateFornecedoresRemoval(object, persistedObject);
        validateObrasRemoval(object, persistedObject);
    }

    private void validateObrasRemoval(Dispensa object, Dispensa persistedObject) {
        if (persistedObject.getObra() != null && (object.getObra() == null || !persistedObject.getObra().equals(object.getObra()))) {
            obraRepository.deleteById(persistedObject.getObra().getId());
        }
    }

    private void validateFornecedoresRemoval(Dispensa object, Dispensa persistedObject) {
        if (!persistedObject.getFornecedores().equals(object.getFornecedores())) {
            List<Long> newIdList = object.getFornecedores().stream().map(AbstractIdentificavel::getId).collect(Collectors.toList());
            for (Long persistedId : persistedObject.getFornecedores().stream().map(AbstractIdentificavel::getId).collect(Collectors.toList())) {
                if (!newIdList.contains(persistedId)) {
                    dispensaLicitanteRepository.deleteById(persistedId);
                }
            }
        }
    }

    @Override
    public void remover(Long id) throws AppException {
        Optional<Dispensa> entidadeOpt = getRepository().findById(id);
        if (entidadeOpt.isPresent()) {
            Dispensa dis = entidadeOpt.get();
            validarRemover(dis);
            dis.setStatus(StatusLicitacao.REMOVIDA);
            getRepository().save(dis);
        }
    }

    @Override
    public BuscaResponse<Dispensa> buscarAdvanced(AdvancedSearchRequest filtro) {
        if (filtro.getAndParameters().isEmpty()) {
            setDefaultFilterParams(filtro);
        }
        return super.buscarAdvanced(filtro);
    }

    @Override
    protected void setDefaultFilterParams(AdvancedSearchRequest filtro) {
        AdvancedSearchParameter situacaoParam = new AdvancedSearchParameter("status", SearchOperator.NOT_EQUAL_TO, StatusLicitacao.REMOVIDA.name());
        String[] idEntidadesContexto = ThreadContext.get("entities").replace("[", "").replace("]", "").split(", ");
        for (String id : idEntidadesContexto) {
            if (!id.isEmpty()) {
                AdvancedSearchParameter entidadeParam = new AdvancedSearchParameter("entidade", SearchOperator.EQUAL_TO, Long.valueOf(id));
                filtro.getOrParameters().add(entidadeParam);
            }
        }
        filtro.getAndParameters().add(situacaoParam);
    }

    public void saveDispensa(DispensaDTO dto) throws AppException {
        Dispensa dispensa = dto.getDispensa();
        List<ArquivoDispensaDTO> arquivos = dto.getArquivosDispensa();
        this.validarArquivos(arquivos, dispensa.getLei());

        TermoReferencia termoReferencia = dispensa.getTermoReferencia();

        if (dispensa.getId() == null && termoReferencia != null && termoReferenciaRepository.checkAssociatedTerm(termoReferencia.getId())) {
            throw new AppException("A operação não pôde ser concluída, pois o Termo de Referência escolhido já está associado a algum processo.", HttpStatus.UNPROCESSABLE_ENTITY);
        }

        if (!dispensa.getLei().equals("LEI_N_8666") && (termoReferencia == null || termoReferencia.getIdentificadorProcesso() == null)) {
            throw new AppException("A operação não pôde ser concluída, pois o Termo de Referência escolhido deve possuir um identificador.", HttpStatus.UNPROCESSABLE_ENTITY);
        }

        if (termoReferencia != null && !termoReferencia.getIsFinalizado()) {
            termoReferencia = termoReferenciaService.finalizarTermo(termoReferencia, "Dispensa");
            dispensa.setTermoReferencia(termoReferencia);
        }

        Edificacao edificacao;
        Obra obra;

        if (dto.getObra() != null) {
            obra = dispensa.getObra();
            edificacao = Optional.ofNullable(obra.getEdificacao()).orElse(new Edificacao());
            edificacao.setGeometry(dto.getObra().getTipoCamada(), dto.getObra().getCoordenadas());
            edificacao.setObra(obra);
            obra.setEdificacao(edificacao);
            dispensa.setObra(obra);
            dispensa.getObra().setDispensa(dispensa);
        } else if (dto.getEdificacao() != null) {
            edificacao = dto.getEdificacao();
            obra = dispensa.getObra();
            obra.setEdificacao(edificacao);
            edificacao.setObra(obra);
            dispensa.setObra(obra);
            dispensa.getObra().setDispensa(dispensa);
        }
        dispensa = save(dispensa);
        if (dispensa.getLotesFracassados() != null) {
            salvarLotesFracassados(dispensa);
        }
        if (dispensa.getItensDesertos() != null) {
            salvarItensDesertos(dispensa);
        }

        saveArquivos(arquivos, dispensa);
    }

    @Override
    protected void afterSave(boolean isNew, Dispensa entity) {
        repository.updateLastModifiedTimestamp(entity.getId());
    }

    private void salvarLotesFracassados(Dispensa dispensa) {
        for (ItemLoteFracassado loteFracassado : dispensa.getLotesFracassados()) {
            loteFracassado.setDispensa(dispensa);
            itemLoteFracassadoRepository.save(loteFracassado);
        }
    }

    private void salvarItensDesertos(Dispensa dispensa) {
        for (ItemDeserto itemDeserto : dispensa.getItensDesertos()) {
            itemDeserto.setDispensa(dispensa);
            itemDesertoRepository.save(itemDeserto);
        }
    }

    @Override
    public ArquivoDispensaService getArquivoService() {
        return arquivoDispensaService;
    }

    @Override
    public ArquivoDispensaFileService getFileService() {
        return arquivoDispensaFileService;
    }

    @Override
    public ArquivoDispensaToDtoMapper getMapper() {
        return arquivoDispensaToDtoMapper;
    }

    @Override
    protected ArquivoDispensa getNewArquivo() {
        return new ArquivoDispensa();
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoDispensa arquivoEntity, Dispensa entity, ArquivoDispensaDTO arquivoUpload) {
        arquivoEntity.setDispensa(entity);
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    @Override
    public List<ItemContrato> itensEmContratos(Long idDispensa) {
        return this.repository.itensEmContratos(idDispensa);
    }

    @Override
    public Dispensa getById(Long idDispensa) {
        Dispensa dispensa = super.getById(idDispensa);
        dispensa.setTda(tdaRepository.getByIdDispensa(idDispensa));
        return dispensa;
    }

    @Override
    public UltimaAlteracaoDTO getUltimaAlteracao(Long id) {
        List<Object[]> ultimasModificadores = repository.getUltimaAlteracao(id);
        if (ultimasModificadores.size() > 0) {
            UltimaAlteracaoDTO response = new UltimaAlteracaoDTO();
            Object[] ultimaModificacao = ultimasModificadores.get(0);
            response.setData(((Timestamp) ultimaModificacao[1]).toLocalDateTime());
            response.setNome((String) ultimaModificacao[0]);
            return response;
        } else {
            return null;
        }
    }

    @Override
    public Boolean getTresCasasDecimais(Long idDispensa) {
        return getRepository().getTresCasasDecimais(idDispensa);
    }

    public ArquivoBinarioDTO download(ArquivoDTO arquivoDTO, Boolean countDownloads) throws AppException {
        ArquivoBinarioDTO download = arquivoDispensaFileService.download(arquivoDTO);
        if (arquivoDTO.ehArquivoDefinitivoValido()) {
            Long idArquivoDispensa = Long.parseLong(arquivoDTO.getLookupId());
            repository.updateNumeroDownloadsDispensa(idArquivoDispensa);
        }
        return download;
    }

    @Override
    public void setEmAnalise(Long idProcesso, Boolean emAnalise) {
        LocalDateTime dataAnalise = LocalDateTime.now();
        if (!emAnalise) {
            dataAnalise = null;
        }
        this.repository.updateEmAnalise(idProcesso, emAnalise, dataAnalise);
        this.repository.updateLastAuditTimestamp(idProcesso);
    }

    @Override
    public void desarquivarDispensa(Long idProcesso) {
        this.repository.desarquivarDispensa(idProcesso, LocalDateTime.now());
        this.repository.updateLastAuditTimestamp(idProcesso);
    }

    @Override
    public void arquivarProcesso(Long idDispensa, StatusAuditoriaLicitacao status) {
        if (isUsuarioInspetor() || ThreadContext.get("groups").equals("Administrador")) {
            Optional<Dispensa> optDispensa = getRepository().findById(idDispensa);
            if (optDispensa.isPresent()) {
                Dispensa dispensa = optDispensa.get();
                dispensa.setStatusProcessoArquivado(status);
                getRepository().save(dispensa);
                getRepository().updateLastAuditTimestamp(idDispensa);
            }
        } else {
            throw new AppException("O usuário não tem permissão para realizar a operação", HttpStatus.UNAUTHORIZED);
        }
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() throws AppException {
        AdvancedSearchRequest filtro = new AdvancedSearchRequest();
        this.inicializaFiltro(filtro);
        AdvancedSearchParameter arquivoObjetoParam = new AdvancedSearchParameter("objeto", SearchOperator.EQUAL_TO, Objeto.DISPENSA.name());
        filtro.getAndParameters().add(arquivoObjetoParam);

        return obrigatoriedadeArquivoService.buscarAdvanced(filtro).getItems();
    }

    private List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios(String lei) throws AppException {
        ArrayList<String> filtros = new ArrayList<>();
        filtros.add(lei);
        List<ObrigatoriedadeArquivo> obrigatoriedadeArquivos = this.obrigatoriedadeArquivoService.getArquivosObrigatorios("DISPENSA", filtros);
        return obrigatoriedadeArquivos.stream().filter(ObrigatoriedadeArquivo::getObrigatorio).collect(Collectors.toList());
    }

    @Override
    public void validarArquivos(List<ArquivoDispensaDTO> arquivos, String lei) {
        List<TipoArquivoDispensa> tiposArquivosEnviados = extrairTiposArquivos(arquivos);
        this.verificarDescricaoTipoOutros(arquivos);
        this.verificarArquivosObrigatorios(tiposArquivosEnviados, this.getArquivosTiposObrigatorios(lei));
        if (arquivos.stream().anyMatch(arquivo -> arquivo.getTipo().equals(TipoArquivoDispensa.PROJETO_ENGENHARIA) && !checkTypeDWG(arquivo.getArquivo().getTipoArquivo()))) {
            throw new AppException("O arquivo do tipo 'Projetos de engenharia (básico, executivo e afins)' deve ser em formato DWG", HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    public List<Integer> getAnosDispensa() {
        List<Integer> years = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (int year = 2002; year <= now.getYear(); year++) {
            years.add(year);
        }
        if (now.getMonth().getValue() == 12) {
            years.add(now.getYear() + 1);
        }
        years.sort(Collections.reverseOrder());
        return years;
    }

    public Long getAlertaDispensa(Long idProcesso, String tipoProcesso) {
        AnaliseProcessoView alerta = analiseProcessoViewService.getProcesso(idProcesso, tipoProcesso);
        if (alerta != null) {
            return alerta.getIdAlertaAnalise();
        }
        return null;
    }

    @Override
    public void anularRevogar(Long idProcesso, AnulacaoRevogacaoDTO dto) {
        Dispensa entity = repository.getById(idProcesso);
        AnulacaoRevogacao persistedAnulacaoRevogacao = anulacaoRevogacaoService.saveAnulacaoRevogacao(dto);
        entity.setAnulacaoRevogacao(persistedAnulacaoRevogacao);
        save(entity);
    }

}
