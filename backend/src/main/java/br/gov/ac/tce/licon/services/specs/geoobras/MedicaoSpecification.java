package br.gov.ac.tce.licon.services.specs.geoobras;

import br.gov.ac.tce.licon.dtos.requests.geoobras.MedicaoFiltroRequest;
import br.gov.ac.tce.licon.entities.geoobras.Medicao;
import br.gov.ac.tce.licon.services.specs.ISpecification;
import lombok.AllArgsConstructor;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public class MedicaoSpecification implements ISpecification<Medicao> {

    private static final long serialVersionUID = 8523481552346725843L;

    private final MedicaoFiltroRequest filtro;

    @Override
    public Predicate toPredicate(Root<Medicao> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
        List<Predicate> predicados = new ArrayList<>();

        return builder.and(predicados.toArray(new Predicate[0]));
    }

}
