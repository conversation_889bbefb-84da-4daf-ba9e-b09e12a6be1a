package br.gov.ac.tce.licon.services.specs;

import br.gov.ac.tce.licon.dtos.requests.ProcessoLicitacaoViewFiltroRequest;
import br.gov.ac.tce.licon.entities.ProcessoLicitacaoView;
import br.gov.ac.tce.licon.entities.ProcessoLicitacaoView_;
import lombok.AllArgsConstructor;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public class ProcessoLicitacaoViewSpecification implements ISpecification<ProcessoLicitacaoView> {

    private static final long serialVersionUID = 783351234654331L;

    private final ProcessoLicitacaoViewFiltroRequest filtro;

    @Override
    public Predicate toPredicate(Root<ProcessoLicitacaoView> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
        List<Predicate> predicados = new ArrayList<>();
        addIfExists(getPredicate(root.get(ProcessoLicitacaoView_.numero), filtro.getNumero(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(ProcessoLicitacaoView_.numeroAnoLicitacao), filtro.getNumeroAnoLicitacao(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(ProcessoLicitacaoView_.ano), filtro.getAno(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(ProcessoLicitacaoView_.objeto), filtro.getObjeto(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(ProcessoLicitacaoView_.entidade), filtro.getEntidade(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(ProcessoLicitacaoView_.fase), filtro.getFase(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(ProcessoLicitacaoView_.orgao), filtro.getOrgao(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(ProcessoLicitacaoView_.dataCadastro), filtro.getDataCadastro(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(ProcessoLicitacaoView_.valorEstimado), filtro.getValorEstimado(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(ProcessoLicitacaoView_.dataAbertura), filtro.getDataAbertura(), builder, filtro.getFilterType()), predicados);
        addIfExists(getPredicate(root.get(ProcessoLicitacaoView_.usuario), filtro.getUsuario(), builder, filtro.getFilterType()), predicados);
        return builder.and(predicados.toArray(new Predicate[0]));
    }

}
