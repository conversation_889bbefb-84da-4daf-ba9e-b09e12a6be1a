package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.mapper.geoobras.ArquivoMedicaoToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchParameter;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.SearchOperator;
import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivoMedicaoDTO;
import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivoMedicaoFiltroRequest;
import br.gov.ac.tce.licon.dtos.requests.geoobras.MedicaoDTO;
import br.gov.ac.tce.licon.dtos.requests.geoobras.MedicaoFiltroRequest;
import br.gov.ac.tce.licon.dtos.responses.BuscaResponse;
import br.gov.ac.tce.licon.entities.ObrigatoriedadeArquivo;
import br.gov.ac.tce.licon.entities.enums.Objeto;
import br.gov.ac.tce.licon.entities.enums.geoobras.FaseGeoobraObra;
import br.gov.ac.tce.licon.entities.enums.geoobras.TipoArquivoMedicao;
import br.gov.ac.tce.licon.entities.geoobras.ArquivoMedicao;
import br.gov.ac.tce.licon.entities.geoobras.GeoobraObra;
import br.gov.ac.tce.licon.entities.geoobras.Medicao;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.geoobras.MedicaoRepository;
import br.gov.ac.tce.licon.services.ObrigatoriedadeArquivoService;
import br.gov.ac.tce.licon.services.geoobras.ArquivoMedicaoFileService;
import br.gov.ac.tce.licon.services.geoobras.ArquivoMedicaoService;
import br.gov.ac.tce.licon.services.geoobras.GeoobraObraService;
import br.gov.ac.tce.licon.services.geoobras.MedicaoService;
import br.gov.ac.tce.licon.services.impl.AbstractUploadTipoServiceImpl;
import br.gov.ac.tce.licon.services.specs.geoobras.MedicaoSpecification;
import br.gov.ac.tce.licon.utils.UtilsKML;
import com.vividsolutions.jts.geom.Geometry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class MedicaoServiceImpl extends AbstractUploadTipoServiceImpl<Medicao, MedicaoFiltroRequest, MedicaoRepository, ArquivoMedicaoFileService, ArquivoMedicao, ArquivoMedicaoFiltroRequest, ArquivoMedicaoService, ArquivoMedicaoDTO, ArquivoMedicaoToDtoMapper, TipoArquivoMedicao> implements MedicaoService {

    @Autowired
    private ArquivoMedicaoService arquivoMedicaoService;

    @Autowired
    private ArquivoMedicaoFileService arquivoMedicaoFileService;

    @Autowired
    private MedicaoRepository repository;

    @Autowired
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Autowired
    private GeoobraObraService geoobraObraService;

    @Autowired
    private ArquivoMedicaoToDtoMapper arquivoMedicaoToDtoMapper;

    @Override
    public MedicaoRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<Medicao> getSpecification(MedicaoFiltroRequest filtro) {
        return new MedicaoSpecification(filtro);
    }

    @Override
    protected void beforeSave(Medicao entity) {
        if (entity.getId() == null) {
            entity.setDataCadastro(LocalDate.now());
        }
    }

    @Override
    public void remover(Long id) throws AppException {
        Optional<Medicao> entidadeOpt = getRepository().findById(id);
        if (entidadeOpt.isPresent()) {
            Medicao medicao = entidadeOpt.get();
            validarRemover(medicao);
            getRepository().save(medicao);
        }
    }

    @Override
    public BuscaResponse<Medicao> buscarAdvanced(AdvancedSearchRequest filtro) {
        if (filtro.getAndParameters().isEmpty()) {
            setDefaultFilterParams(filtro);
        }
        return super.buscarAdvanced(filtro);
    }

    public void saveMedicao(MedicaoDTO dto) throws AppException {
        Medicao medicao = dto.getMedicao();
        List<ArquivoMedicaoDTO> arquivos = dto.getArquivosMedicao();
        medicao.getObra().setFase(FaseGeoobraObra.MEDICAO);

        GeoobraObra obra = geoobraObraService.getById(medicao.getObra().getId());
        obra.setFase(FaseGeoobraObra.MEDICAO);
        geoobraObraService.save(obra);

        this.validarArquivos(dto, obra.getId());
        medicao = save(medicao);

        saveArquivos(arquivos, medicao);
    }

    @Override
    public ArquivoMedicaoService getArquivoService() {
        return arquivoMedicaoService;
    }

    @Override
    public ArquivoMedicaoFileService getFileService() {
        return arquivoMedicaoFileService;
    }

    @Override
    public ArquivoMedicaoToDtoMapper getMapper() {
        return arquivoMedicaoToDtoMapper;
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() {
        AdvancedSearchRequest filtro = new AdvancedSearchRequest();
        this.inicializaFiltro(filtro);
        AdvancedSearchParameter arquivoObjetoParam = new AdvancedSearchParameter("objeto", SearchOperator.EQUAL_TO, Objeto.MEDICAO.name());
        AdvancedSearchParameter arquivoObrigatorioParam = new AdvancedSearchParameter("obrigatorio", SearchOperator.EQUAL_TO, true);
        filtro.getAndParameters().add(arquivoObjetoParam);
        filtro.getAndParameters().add(arquivoObrigatorioParam);

        return obrigatoriedadeArquivoService.buscarAdvanced(filtro).getItems();
    }

    @Override
    protected ArquivoMedicao getNewArquivo() {
        return new ArquivoMedicao();
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoMedicao arquivoEntity, Medicao entity, ArquivoMedicaoDTO arquivoUpload) {
        arquivoEntity.setMedicao(entity);
        if (TipoArquivoMedicao.GEORREFERENCIAMENTO.equals(arquivoUpload.getTipo())) {
            ArquivoBinarioDTO arquivoBinarioDTO = this.download(arquivoUpload.getArquivo());
            InputStream inputStream = new ByteArrayInputStream(arquivoBinarioDTO.getBinario());
            Geometry geom = UtilsKML.extractGeomFromKML(inputStream);
            arquivoEntity.setGeom(geom);
            arquivoEntity.setCentroid(geom.getCentroid());
        }
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    @Override
    public void validarArquivos(MedicaoDTO dto, Long idObra) {
        super.validarArquivos(dto.getArquivosMedicao());
        GeoobraObra obra = geoobraObraService.getById(idObra);
        for (ArquivoMedicaoDTO arquivoDTO : dto.getArquivosMedicao()) {
            if (TipoArquivoMedicao.GEORREFERENCIAMENTO.equals(arquivoDTO.getTipo())) {
                getFileService().validaArquivoKML(arquivoDTO.getArquivo(), dto.getEntidade(), obra);
            }
        }
    }

    @Override
    public List<Medicao> getByObra(Long idObra) {
        return this.repository.getByObra(idObra);
    }
}
