package br.gov.ac.tce.licon.services.impl.geoobras;

import br.gov.ac.tce.licon.dtos.requests.geoobras.ArquivoMedicaoFiltroRequest;
import br.gov.ac.tce.licon.entities.geoobras.ArquivoMedicao;
import br.gov.ac.tce.licon.repositories.geoobras.ArquivoMedicaoRepository;
import br.gov.ac.tce.licon.services.geoobras.ArquivoMedicaoService;
import br.gov.ac.tce.licon.services.impl.AbstractService;
import br.gov.ac.tce.licon.services.specs.geoobras.ArquivoMedicaoSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Transactional
public class ArquivoMedicaoServiceImpl
        extends AbstractService<ArquivoMedicao, ArquivoMedicaoFiltroRequest, ArquivoMedicaoRepository>
        implements ArquivoMedicaoService {

    @Autowired
    private ArquivoMedicaoRepository repository;

    @Override
    public ArquivoMedicaoRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<ArquivoMedicao> getSpecification(ArquivoMedicaoFiltroRequest filtro) {
        return new ArquivoMedicaoSpecification(filtro);
    }

    @Override
    protected void beforeSave(ArquivoMedicao entity) {
        if (entity.getId() == null) {
            entity.setDataEnvio(LocalDateTime.now());
        }
    }

    @Override
    public List<ArquivoMedicao> buscarPor(Long idMedicao) {
        return repository.buscarPor(idMedicao);
    }
}
