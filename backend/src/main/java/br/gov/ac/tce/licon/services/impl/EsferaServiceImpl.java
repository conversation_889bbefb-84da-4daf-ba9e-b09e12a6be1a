package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.requests.EsferaFiltroRequest;
import br.gov.ac.tce.licon.entities.Esfera;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.EsferaRepository;
import br.gov.ac.tce.licon.services.EsferaService;
import br.gov.ac.tce.licon.services.specs.EsferaSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class EsferaServiceImpl extends AbstractService<Esfera, EsferaFiltroRequest, EsferaRepository> implements EsferaService {

	@Autowired
	private EsferaRepository repository;
	
	@Override
	public EsferaRepository getRepository() {
		return repository;
	}
	
	@Override
	protected Example<Esfera> obterExemploChecarSeJahExiste(Esfera entity) throws AppException {
		Esfera exemplo = Esfera.builder()
								.codigo(entity.getCodigo())
								.build();
		return Example.of(exemplo);
	}

	@Override
	protected void lancarErroEntidadeJahExistente(Esfera entity) throws AppException {
		throw new AppException(String.format("Esfera com dado código já existe: %s", entity.getCodigo()),
				HttpStatus.UNPROCESSABLE_ENTITY);
	}

	@Override
	protected Specification<Esfera> getSpecification(EsferaFiltroRequest filtro) {
		return new EsferaSpecification(filtro);
	}

	@Override
	public Esfera getEsferaByCodigoCjur(Integer codigo) {
		return this.repository.getByCodigoCjur(codigo);
	}

}
