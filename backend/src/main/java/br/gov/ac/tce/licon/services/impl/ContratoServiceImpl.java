package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.EmailDTO;
import br.gov.ac.tce.licon.dtos.mapper.ArquivoContratoToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.*;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.responses.AditivoValorResponse;
import br.gov.ac.tce.licon.dtos.responses.BuscaResponse;
import br.gov.ac.tce.licon.dtos.responses.VencedorDisponivelResponseDTO;
import br.gov.ac.tce.licon.dtos.responses.cjurApi.PessoaResponsavelEntidadeResponseDTO;
import br.gov.ac.tce.licon.entities.*;
import br.gov.ac.tce.licon.entities.enums.*;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.*;
import br.gov.ac.tce.licon.services.*;
import br.gov.ac.tce.licon.services.cjurApi.CjurApiService;
import br.gov.ac.tce.licon.services.specs.ContratoSpecification;
import br.gov.ac.tce.licon.utils.GeradorMensagemEmail;
import com.j256.simplemagic.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Transactional
public class ContratoServiceImpl extends AbstractUploadTipoServiceImpl<Contrato, ContratoFiltroRequest, ContratoRepository, ArquivoContratoFileService, ArquivoContrato, ArquivoContratoFiltroRequest, ArquivoContratoService, ArquivoContratoDTO, ArquivoContratoToDtoMapper, TipoArquivoContrato> implements ContratoService {

    @Inject
    private ArquivoContratoService arquivoContratoService;

    @Inject
    private ObrigatoriedadeArquivoRepository obrigatoriedadeArquivoRepository;

    @Inject
    private LicitacaoRepository licitacaoRepository;

    @Inject
    private DispensaRepository dispensaRepository;

    @Inject
    private CredenciamentoRepository credenciamentoRepository;

    @Inject
    private InexigibilidadeRepository inexigibilidadeRepository;

    @Inject
    private CaronaRepository caronaRepository;

    @Inject
    private ArquivoContratoFileService arquivoContratoFileService;

    @Inject
    private ArquivoContratoToDtoMapper arquivoContratoToDtoMapper;

    @Autowired
    private ContratoRepository repository;

    @Autowired
    private ItemContratoRepository itemContratoRepository;

    @Autowired
    private LicitacaoService licitacaoService;

    @Autowired
    private CaronaService caronaService;

    @Autowired
    private DispensaService dispensaService;

    @Autowired
    private InexigibilidadeService inexigibilidadeService;

    @Autowired
    private CredenciamentoService credenciamentoService;

    @Autowired
    private CredenciadoService credenciadoService;

    @Autowired
    private CredenciadoItemService credenciadoItemService;

    @Autowired
    private IEmailService emailService;

    @Autowired
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Autowired
    private AditivoContratoRepository aditivoContratoRepository;

    @Autowired
    private CjurApiService cjurApiService;

    @Override
    public ContratoRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<Contrato> getSpecification(ContratoFiltroRequest filtro) {
        return new ContratoSpecification(filtro);
    }

    @Override
    protected Example<Contrato> obterExemploChecarSeJahExiste(Contrato entity) throws AppException {
        Contrato exemplo;
        if (entity.getFormaContrato() == FormaContrato.CONTRATO) {
            exemplo = Contrato.builder().numero(entity.getNumero()).entidade(entity.getEntidade()).anoCadastro(entity.getDataCadastro().getYear()).anoContrato(entity.getAnoContrato()).build();
        } else {
            exemplo = Contrato.builder().numero(entity.getNumero()).entidade(entity.getEntidade()).anoCadastro(entity.getDataCadastro().getYear()).build();
        }
        return Example.of(exemplo);
    }

    @Override
    protected void lancarErroEntidadeJahExistente(Contrato entity) throws AppException {
        if (entity.getFormaContrato() == FormaContrato.CONTRATO) {
            throw new AppException(String.format("Contrato com dado número já existe: %s / %s para a entidade %s.", entity.getNumero(), entity.getAnoContrato(), entity.getEntidade().getNome()), HttpStatus.UNPROCESSABLE_ENTITY);
        } else {
            throw new AppException(String.format("Contrato com dado número já existe: %s para a entidade %s.", entity.getNumero(), entity.getEntidade().getNome()), HttpStatus.UNPROCESSABLE_ENTITY);
        }
    }

    @Override
    protected void beforeSave(Contrato entity) {
        if (entity.getId() == null) {
            entity.setUsuario(getCurrentUser());
        }
    }

    @Override
    protected void afterSave(boolean isNew, Contrato entity) {
        if (isNew) {
            Usuario usuario = getCurrentUser();
            EmailDTO emailDTO = new EmailDTO();
            emailDTO.setUsuario(usuario.getLogin());
            emailDTO.setSistema(app);
            emailDTO.setAssunto("Confirmação de Cadastro");
            emailDTO.setDestinatarios(new HashSet<>(Collections.singletonList(usuario.getEmail())));
            String mensagem = String.format(GeradorMensagemEmail.getMensagemCadastroContrato(), usuario.getNome(), entity.getDataCadastro().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")), entity.getNumero(), entity.getObjeto());
            emailDTO.setMensagem(mensagem);
            emailService.sendEmail(emailDTO);
        }
    }

    @Override
    protected void validateCollectionsRemoval(Contrato object, Contrato persistedObject) {
        validateItensRemoval(object, persistedObject);
    }

    private void validateItensRemoval(Contrato object, Contrato persistedObject) {
        if (!persistedObject.getContratoLicitante().getItens().equals(object.getContratoLicitante().getItens()) && persistedObject.getContratoLicitante().getItens() != null) {
            List<Long> newIdList = new ArrayList<>();
            if (object.getContratoLicitante().getItens() != null) {
                newIdList.addAll(object.getContratoLicitante().getItens().stream().map(AbstractIdentificavel::getId).collect(Collectors.toList()));
            }
            for (Long persistedId : persistedObject.getContratoLicitante().getItens().stream().map(AbstractIdentificavel::getId).collect(Collectors.toList())) {
                if (!newIdList.contains(persistedId)) {
                    itemContratoRepository.deleteById(persistedId);
                }
            }

        }
    }

    public void criarContrato(ContratoDTO dto) throws AppException {
        Contrato entity = dto.getContrato();
        List<ArquivoContratoDTO> arquivos = dto.getArquivosContrato();
        validarArquivos(arquivos, dto.getFiltros());
        Contrato contrato = save(entity);
        getByIdSetAssociatedProcess(entity.getId());
        saveArquivos(arquivos, contrato);
    }

    public List<VencedorDisponivelResponseDTO> itensDisponiveisContrato(Long idProcesso, String tipoProcesso) {
        List<ItemContrato> itensContratados = getItensEmContratos(idProcesso, tipoProcesso);
        List<Vencedor> vencedoresProcesso = getVencedoresProcesso(idProcesso, tipoProcesso);
        String legislacao = getLegislacao(tipoProcesso, idProcesso);

        if (legislacao.equals("OUTRA")) {
            return handleVencedoresLeiNova(vencedoresProcesso, itensContratados, tipoProcesso);
        } else {
            return handleVencedoresLeiAntiga(vencedoresProcesso, idProcesso, tipoProcesso);
        }
    }

    private List<VencedorDisponivelResponseDTO> handleVencedoresLeiNova(List<Vencedor> vencedoresProcesso, List<ItemContrato> itensContratados, String tipoProcesso) {
        List<VencedorDisponivelResponseDTO> vencedoresDisponiveis = new ArrayList<>();
        for (Vencedor vencedor : vencedoresProcesso) {
            BigDecimal vencedorQuantidade = vencedor.getQuantidade();
            VencedorDisponivelResponseDTO vencedorDisponivelDTO = new VencedorDisponivelResponseDTO();
            vencedorDisponivelDTO.setVencedor(vencedor);

            if (!tipoProcesso.equals("CR")) {
                if (itensContratados.size() > 0) {
                    for (ItemContrato itemContratado : itensContratados) {
                        if (itemContratado.getVencedor(tipoProcesso).getId().equals(vencedor.getId()) &&
                                itemContratado.getVencedor(tipoProcesso).getItemLote().getId().equals(vencedor.getItemLote().getId())) {
                            vencedorQuantidade = vencedorQuantidade.subtract(itemContratado.getQuantidade());
                            vencedorDisponivelDTO.setQuantidadeDisponivel(vencedorQuantidade);
                            vencedoresDisponiveis.add(vencedorDisponivelDTO);
                        }
                    }
                } else {
                    vencedorDisponivelDTO.setQuantidadeDisponivel(vencedorQuantidade);
                    vencedoresDisponiveis.add(vencedorDisponivelDTO);
                }
            } else {
                vencedoresDisponiveis.add(vencedorDisponivelDTO);
            }
        }
        return vencedoresDisponiveis;
    }

    private List<VencedorDisponivelResponseDTO> handleVencedoresLeiAntiga(List<Vencedor> vencedoresProcesso, Long idProcesso, String tipoProcesso) {
        List<VencedorDisponivelResponseDTO> vencedoresDisponiveis = new ArrayList<>();
        for (Vencedor vencedor : vencedoresProcesso) {
            VencedorDisponivelResponseDTO vencedorDisponivelDTO = new VencedorDisponivelResponseDTO();
            vencedorDisponivelDTO.setVencedor(vencedor);
            BigDecimal valorContratado = this.getValorContratado(idProcesso, vencedor.getLicitante().getId(), tipoProcesso);
            if (valorContratado != null && vencedor.getValor() != null) {
                BigDecimal valorDisponivel = vencedor.getValor().subtract(valorContratado);
                vencedorDisponivelDTO.setValorContratado(valorContratado);
                vencedorDisponivelDTO.setValorDisponivel(valorDisponivel);
            } else if (valorContratado == null && vencedor.getValor() != null) {
                vencedorDisponivelDTO.setValorDisponivel(vencedor.getValor());
            }

            vencedoresDisponiveis.add(vencedorDisponivelDTO);
        }
        return vencedoresDisponiveis;
    }

    private String getLegislacao(String tipoProcesso, Long idProcesso) throws AppException {
        switch (tipoProcesso) {
            case "L":
                Licitacao licitacao = licitacaoService.getById(idProcesso);
                return licitacao.getLei();
            case "I":
                Inexigibilidade inexigibilidade = inexigibilidadeService.getById(idProcesso);
                return inexigibilidade.getLei();
            case "C":
                Carona carona = caronaService.getById(idProcesso);
                return carona.getLei();
            case "D":
                Dispensa dispensa = dispensaService.getById(idProcesso);
                return dispensa.getLei();
            case "CR":
                Credenciamento credenciamento = credenciamentoService.getById(idProcesso);
                return credenciamento.getLei();
            default:
                throw new AppException("Tipo de processo inválido: " + tipoProcesso);
        }
    }

    private List<Vencedor> getVencedoresProcesso(Long idProcesso, String tipoProcesso) {
        switch (tipoProcesso) {
            case "L":
                Licitacao licitacao = licitacaoService.getById(idProcesso);
                return licitacao.getVencedores().stream()
                        .map(vencedor -> (Vencedor) vencedor)
                        .collect(Collectors.toList());
            case "I":
                Inexigibilidade inexigibilidade = inexigibilidadeService.getById(idProcesso);
                return inexigibilidade.getFornecedores().stream()
                        .map(vencedor -> (Vencedor) vencedor)
                        .collect(Collectors.toList());
            case "C":
                Carona carona = caronaService.getById(idProcesso);
                return carona.getDetentores().stream()
                        .map(vencedor -> (Vencedor) vencedor)
                        .collect(Collectors.toList());
            case "D":
                Dispensa dispensa = dispensaService.getById(idProcesso);
                return dispensa.getFornecedores().stream()
                        .map(vencedor -> (Vencedor) vencedor)
                        .collect(Collectors.toList());
            case "CR":
                List<Credenciado> credenciados = credenciadoService.getAllByCredenciamento(idProcesso);
                credenciados = credenciados.stream().filter((credenciado -> credenciado.getSituacao().equals("VIGENTE"))).collect(Collectors.toList());

                List<CredenciadoItem> itensCredenciados = new ArrayList<>();
                credenciados.forEach((credenciado -> {
                    credenciado.getCredenciadoItems().forEach((credenciadoItem -> {
                        itensCredenciados.add(credenciadoItem);
                    }));
                }));
                return itensCredenciados.stream()
                        .map(vencedor -> (Vencedor) vencedor)
                        .collect(Collectors.toList());
            default:
                throw new AppException("Tipo de processo inválido: " + tipoProcesso);
        }
    }

    private List<ItemContrato> getItensEmContratos(Long idProcesso, String tipoProcesso) {
        switch (tipoProcesso) {
            case "L":
                return licitacaoService.itensEmContratos(idProcesso);
            case "I":
                return inexigibilidadeService.itensEmContratos(idProcesso);
            case "C":
                return caronaService.itensEmContratos(idProcesso);
            case "D":
                return dispensaService.itensEmContratos(idProcesso);
            case "CR":
                return  credenciadoItemService.itensEmContratos(idProcesso);
            default:
                throw new AppException("Tipo de processo inválido: " + tipoProcesso);
        }
    }

    private BigDecimal getValorContratado(Long idProcesso, Long idLicitante, String tipoProcesso) {
        switch (tipoProcesso) {
            case "L":
                return this.repository.getValorContratadoByIdLicitacaoAndIdLicitante(idProcesso, idLicitante);
            case "I":
                return this.repository.getValorContratadoByIdInexigibilidadeAndIdLicitante(idProcesso, idLicitante);
            case "C":
                return this.repository.getValorContratadoByIdCaronaAndIdLicitante(idProcesso, idLicitante);
            case "D":
                return this.repository.getValorContratadoByIdDispensaAndIdLicitante(idProcesso, idLicitante);
            default:
                return null;
        }
    }

    @Override
    public Contrato getByIdSetAssociatedProcess(Long id) throws AppException {
        Optional<Contrato> entidadeOpt = getRepository().findById(id);
        if (entidadeOpt.isPresent()) {
            Contrato entity = entidadeOpt.get();
            if (entity.getTipo().equals("L") && entity.getIdLicitacao() != null) {
                Optional<Licitacao> licitacaoOpt = licitacaoRepository.findById(entity.getIdLicitacao());
                if (licitacaoOpt.isPresent()) {
                    entity.setProcesso(licitacaoOpt.get());
                    entity.setNumeroLicitacao(licitacaoOpt.get().getNumeroLicitacao());
                }
            } else if (entity.getTipo().equals("D") && entity.getIdDispensa() != null) {
                Optional<Dispensa> dispensaOpt = dispensaRepository.findById(entity.getIdDispensa());
                if (dispensaOpt.isPresent()) {
                    entity.setProcesso(dispensaOpt.get());
                    entity.setNumeroDispensa(dispensaOpt.get().getNumeroProcesso());
                }
            } else if (entity.getTipo().equals("I") && entity.getIdInexigibilidade() != null) {
                Optional<Inexigibilidade> inexigibilidadeOpt = inexigibilidadeRepository.findById(entity.getIdInexigibilidade());
                if (inexigibilidadeOpt.isPresent()) {
                    entity.setProcesso(inexigibilidadeOpt.get());
                    entity.setNumeroInexigibilidade(inexigibilidadeOpt.get().getNumeroProcesso());
                }
            } else if (entity.getTipo().equals("C") && entity.getIdCarona() != null) {
                Optional<Carona> caronaOpt = caronaRepository.findById(entity.getIdCarona());
                if (caronaOpt.isPresent()) {
                    entity.setProcesso(caronaOpt.get());
                    entity.setNumeroCarona(caronaOpt.get().getNumeroProcessoAdministrativo());
                }
            } else if (entity.getTipo().equals("CR") && entity.getIdCredenciamento() != null) {
                Optional<Credenciamento> credenciamentoOpt = credenciamentoRepository.findById(entity.getIdCredenciamento());
                if (credenciamentoOpt.isPresent()) {
                    entity.setProcesso(credenciamentoOpt.get());
                }
            }
            return entity;
        } else {
            throw new AppException(String.format("Entidade '%s' com ID '%d' não encontrada.", getEntityName(), id), HttpStatus.NOT_FOUND);
        }
    }

    @Override
    public void criarRescisaoContrato(ContratoDTO dto) throws AppException {
        Contrato entity = dto.getContrato();
        Contrato contratoCompleto = super.getById(entity.getId());
        List<ArquivoContratoDTO> arquivos = dto.getArquivosContrato();

        List<TipoArquivoContrato> tipoArquivosEnviados = new ArrayList<>();
        arquivos.forEach(arquivo -> tipoArquivosEnviados.add(arquivo.getTipo()));

        contratoCompleto.setPermiteAditivo(entity.getPermiteAditivo());
        contratoCompleto.setDataAvisoRescisao(entity.getDataAvisoRescisao());
        contratoCompleto.setMotivoRescisao(entity.getMotivoRescisao());
        contratoCompleto.setOutroMotivoRescisao(entity.getOutroMotivoRescisao());
        contratoCompleto.setDescricaoRescisao(entity.getDescricaoRescisao());
        contratoCompleto.setFormaExtincao(entity.getFormaExtincao());
        contratoCompleto.setDataCadastroRescisao(LocalDateTime.now());

        Contrato contrato = super.save(contratoCompleto);

        saveArquivos(arquivos, contrato);
    }

    @Override
    public ArquivoContratoService getArquivoService() {
        return arquivoContratoService;
    }

    @Override
    public ArquivoContratoFileService getFileService() {
        return arquivoContratoFileService;
    }

    @Override
    public ArquivoContratoToDtoMapper getMapper() {
        return arquivoContratoToDtoMapper;
    }

    @Override
    protected ArquivoContrato getNewArquivo() {
        return new ArquivoContrato();
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoContrato arquivoEntity, Contrato entity, ArquivoContratoDTO arquivoUpload) {
        arquivoEntity.setContrato(entity);
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    private List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios(ArquivoFiltroContrato filtros) {
        List<String> filtrosArquivos = Stream.of(
                filtros.getLei(),
                Optional.ofNullable(filtros.getFormaContrato()).map(Enum::name).orElse(null),
                Optional.ofNullable(filtros.getTipoOrigemContrato()).map(Enum::name).orElse(null),
                filtros.getGarantiaContratual(),
                filtros.getProcesso()
        ).filter(Objects::nonNull).collect(Collectors.toList());
        List<ObrigatoriedadeArquivo> obrigatoriedadeArquivos = this.obrigatoriedadeArquivoService.getArquivosObrigatorios("CONTRATO", filtrosArquivos);
        return obrigatoriedadeArquivos.stream().filter(ObrigatoriedadeArquivo::getObrigatorio).collect(Collectors.toList());
    }
    private Boolean checkXlsMimeTypes(String mimeType) {
        List<ContentType> types = Arrays.asList(ContentType.MICROSOFT_EXCEL,
                ContentType.MICROSOFT_EXCEL_XML,
                ContentType.CSV,
                ContentType.PDF);
        return types.stream().anyMatch(type -> type.getMimeType().equals(mimeType));
    }

    @Override
    public void validarArquivos(List<ArquivoContratoDTO> arquivos, ArquivoFiltroContrato filtros) {
        List<TipoArquivoContrato> tiposArquivosEnviados = extrairTiposArquivos(arquivos);
        this.validarArquivosTipos(tiposArquivosEnviados, filtros);
        this.verificarDescricaoTipoOutros(arquivos);

        if (arquivos.stream().anyMatch(arquivo -> arquivo.getTipo().equals(TipoArquivoContrato.MAPA_PRECOS) && !checkXlsMimeTypes(arquivo.getArquivo().getTipoArquivo()))) {
            throw new AppException("O arquivo do tipo 'Mapa de Lances' deve ser uma planilha ou um PDF", HttpStatus.BAD_REQUEST);
        }

        List<ArquivoContratoDTO> arquivoErro = arquivos.stream()
                .filter(arquivo -> !arquivo.getTipo().equals(TipoArquivoContrato.MAPA_PRECOS)
                        && !arquivo.getArquivo().getTipoArquivo().equals(ContentType.PDF.getMimeType()))
                .collect(Collectors.toList());

        if (!arquivoErro.isEmpty()) {
            throw new AppException("O arquivo do tipo '" + arquivoErro.get(0).getTipo().getValor() + "' deve ser um PDF", HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    public void validarArquivosTipos(List<TipoArquivoContrato> tipos, ArquivoFiltroContrato filtros) throws AppException {
        this.verificarArquivosObrigatorios(tipos, this.getArquivosTiposObrigatorios(filtros));
    }

    @Override
    public UltimaAlteracaoDTO getUltimaAlteracao(Long id) {
        List<Object[]> ultimasModificadores = repository.getUltimaAlteracao(id);
        if (ultimasModificadores.size() > 0) {
            UltimaAlteracaoDTO response = new UltimaAlteracaoDTO();
            Object[] ultimaModificacao = ultimasModificadores.get(0);
            response.setData(((Timestamp) ultimaModificacao[1]).toLocalDateTime());
            response.setNome((String) ultimaModificacao[0]);
            return response;
        } else {
            return null;
        }
    }

    @Override
    public void remover(Long id) throws AppException {
        Optional<Contrato> entidadeOpt = getRepository().findById(id);
        if (entidadeOpt.isPresent()) {
            Contrato contrato = entidadeOpt.get();
            validarRemover(contrato);
            contrato.setStatus(StatusLicitacao.REMOVIDA);
            getRepository().save(contrato);
        }
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() {
        return Collections.emptyList();
    }

    @Override
    public List<Contrato> getContratosByIdLicitacao(Long id) throws AppException {
        List<Contrato> contratos = getRepository().findAllByIdLicitacao(id);
        if (!contratos.isEmpty()) {
            return contratos;
        } else {
            throw new AppException(String.format("Entidade '%s' com ID '%d' não encontrada.", getEntityName(), id), HttpStatus.NOT_FOUND);
        }
    }

    @Override
    public BuscaResponse<Contrato> buscarAdvanced(AdvancedSearchRequest filtro) {
        BuscaResponse<Contrato> result = super.buscarAdvanced(filtro);
        result.getItems().forEach((contrato -> {
            if (contrato.getTipo().equals("L") && contrato.getIdLicitacao() != null) {
                Optional<Licitacao> licitacaoOpt = licitacaoRepository.findById(contrato.getIdLicitacao());
                if (licitacaoOpt.isPresent()) {
                    contrato.setProcesso(licitacaoOpt.get());
                }
            } else if (contrato.getTipo().equals("D") && contrato.getIdDispensa() != null) {
                Optional<Dispensa> dispensaOpt = dispensaRepository.findById(contrato.getIdDispensa());
                if (dispensaOpt.isPresent()) {
                    contrato.setProcesso(dispensaOpt.get());
                }
            } else if (contrato.getTipo().equals("I") && contrato.getIdInexigibilidade() != null) {
                Optional<Inexigibilidade> inexigibilidadeOpt = inexigibilidadeRepository.findById(contrato.getIdInexigibilidade());
                if (inexigibilidadeOpt.isPresent()) {
                    contrato.setProcesso(inexigibilidadeOpt.get());
                }
            } else if (contrato.getTipo().equals("C") && contrato.getIdCarona() != null) {
                Optional<Carona> caronaOpt = caronaRepository.findById(contrato.getIdCarona());
                if (caronaOpt.isPresent()) {
                    contrato.setProcesso(caronaOpt.get());
                }
            }
        }));
        return result;

    }

    @Override
    public List<Integer> getAnosContrato() {
        List<Integer> years = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (int year = 1998; year <= now.getYear(); year++) {
            years.add(year);
        }
        if (now.getMonth().getValue() == 12) {
            years.add(now.getYear() + 1);
        }
        Collections.sort(years, Collections.reverseOrder());
        return years;
    }

    @Override
    public Integer getQuantidadeAditivosContrato(Long idContrato) {
        return aditivoContratoRepository.getQuantidadeAditivosContrato(idContrato);
    }

    @Override
    public void handleUpdateAlteracoesValor(Contrato contrato) {
        List<AditivoValorResponse> aditivos = aditivoContratoRepository.getAditivosContrato(contrato.getId());

        if (!aditivos.isEmpty()) {
            BigDecimal valorVigente = contrato.getValorGlobal();
            BigDecimal somaAditivados = new BigDecimal(0);
            BigDecimal somaSuprimidos = new BigDecimal(0);

            for (AditivoValorResponse aditivo : aditivos) {
                BigDecimal valor = aditivo.getValor();
                if ((aditivo.getTipoAlteracao()).equals(TipoAlteracao.ALTERACAO_VALOR)) {
                    BigDecimal aditivado = valor.subtract(valorVigente);
                    somaAditivados = somaAditivados.add(aditivado);
                    valorVigente = valorVigente.add(aditivado);
                } else if (aditivo.getTipoAlteracao().equals(TipoAlteracao.SUPRESSAO_VALOR)) {
                    BigDecimal suprimido = valorVigente.subtract(valor);
                    somaSuprimidos = somaSuprimidos.add(suprimido);
                    valorVigente = valorVigente.subtract(suprimido);
                }
            }
            if (somaAditivados.compareTo(new BigDecimal(0)) > 0) {
                contrato.setAditivado(somaAditivados);
            }

            if (somaSuprimidos.compareTo(new BigDecimal(0)) > 0) {
                contrato.setSuprimido(somaSuprimidos);
            }
        } else {
            contrato.setAditivado(null);
            contrato.setSuprimido(null);
        }
        save(contrato);
    }

    @Override
    public void handleUpdateAlteracoesPrazo(Contrato contrato) {
        Optional<LocalDate> aditivoDePrazo = aditivoContratoRepository.getUltimoAditivoPrazoContrato(contrato.getId());

        if (aditivoDePrazo.isPresent()) {
            contrato.setDataFinalVigente(aditivoDePrazo.get());
        } else {
            contrato.setDataFinalVigente(contrato.getDataVigenciaFinal());
        }

        save(contrato);
    }

    @Override
    public List<PessoaResponsavelEntidadeResponseDTO> getPessoasResponsaveisEntidadeByIdEntidade(Long idEntidade) {
        return this.cjurApiService.getPessoasResponsaveisEntidadeByIdEntidade(idEntidade);
    }

}
