package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.requests.AlertaResumoDTO;
import br.gov.ac.tce.licon.dtos.requests.AlertasAuditoriaViewFiltroRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.responses.BuscaResponse;
import br.gov.ac.tce.licon.entities.AlertasAuditoriaView;
import br.gov.ac.tce.licon.entities.GrupoUsuario;
import br.gov.ac.tce.licon.entities.Usuario;
import br.gov.ac.tce.licon.entities.enums.StatusAlertaAnalise;
import br.gov.ac.tce.licon.repositories.AlertasAuditoriaViewRepository;
import br.gov.ac.tce.licon.services.AlertasAuditoriaViewService;
import br.gov.ac.tce.licon.services.GrupoUsuarioService;
import br.gov.ac.tce.licon.services.specs.AlertasAuditoriaViewSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Objects;

@Service
@Transactional
public class AlertasAuditoriaViewServiceImpl extends AbstractViewService<AlertasAuditoriaView, AlertasAuditoriaViewFiltroRequest, AlertasAuditoriaViewRepository> implements AlertasAuditoriaViewService {

    @Autowired
    private AlertasAuditoriaViewRepository repository;

    @Autowired
    private GrupoUsuarioService grupoUsuarioService;

    @Override
    public AlertasAuditoriaViewRepository getRepository() {
        return repository;
    }

    @Override
    public BuscaResponse<AlertasAuditoriaView> buscarAdvanced(AdvancedSearchRequest filtro) {
        setDefaultFilterParams(filtro);
        return super.buscarAdvanced(filtro);
    }

    @Override
    protected void setDefaultFilterParams(AdvancedSearchRequest filtro) {
    }

    @Override
    protected Specification<AlertasAuditoriaView> getSpecification(AlertasAuditoriaViewFiltroRequest filtro) {
        return new AlertasAuditoriaViewSpecification(filtro);
    }

    public AlertaResumoDTO getAlertas(Long idEntidade) {
        Usuario usuario = this.getCurrentUser();
        List<GrupoUsuario> groupUsers = grupoUsuarioService.carregarGrupos(usuario.getId());

        List<AlertasAuditoriaView> alertas;
        if (Objects.isNull(idEntidade)) {
            alertas = this.repository.findAll();
        } else {
            alertas = this.repository.findAllByEntidadeId(idEntidade);
        }

        int alertasRespondidos;
        int alertasNovos;
        if (groupUsers.stream().anyMatch((grupo) -> grupo.getNome().equals("Auditor"))) {
            alertasRespondidos = alertas.stream()
                    .filter((alerta) -> alerta.getStatus().equals(StatusAlertaAnalise.JURISDICIONADO))
                    .mapToInt(AlertasAuditoriaView::getQuantidadeAlertas)
                    .sum();

            alertasNovos = (alertas.stream()
                    .filter((alerta) -> alerta.getStatus().equals(StatusAlertaAnalise.REJEITADO_INSPETOR) ||
                            alerta.getStatus().equals(StatusAlertaAnalise.RESPONDIDO) ||
                            alerta.getStatus().equals(StatusAlertaAnalise.DEVOLVIDO) ||
                            alerta.getStatus().equals(StatusAlertaAnalise.INSPETOR))
                    .mapToInt(AlertasAuditoriaView::getQuantidadeAlertas)
                    .sum());

        } else if (groupUsers.stream().anyMatch((grupo) -> grupo.getNome().equals("DAFO"))) {
            alertasRespondidos = (alertas.stream()
                    .filter((alerta) -> alerta.getStatus().equals(StatusAlertaAnalise.DEVOLVIDO) ||
                            alerta.getStatus().equals(StatusAlertaAnalise.JURISDICIONADO) ||
                            alerta.getStatus().equals(StatusAlertaAnalise.INSPETOR) ||
                            alerta.getStatus().equals(StatusAlertaAnalise.REJEITADO_INSPETOR) ||
                            alerta.getStatus().equals(StatusAlertaAnalise.RESPONDIDO))
                    .mapToInt(AlertasAuditoriaView::getQuantidadeAlertas)
                    .sum());
            alertasNovos = alertas.stream()
                    .filter((alerta) -> alerta.getStatus().equals(StatusAlertaAnalise.ENCAMINHADO))
                    .mapToInt(AlertasAuditoriaView::getQuantidadeAlertas)
                    .sum();
        } else {
            alertasRespondidos = alertas.stream()
                    .filter((alerta) -> alerta.getStatus().equals(StatusAlertaAnalise.RESPONDIDO))
                    .mapToInt(AlertasAuditoriaView::getQuantidadeAlertas)
                    .sum();

            alertasNovos = alertas.stream()
                    .filter((alerta) -> alerta.getStatus().equals(StatusAlertaAnalise.JURISDICIONADO))
                    .mapToInt(AlertasAuditoriaView::getQuantidadeAlertas)
                    .sum();
        }
        return new AlertaResumoDTO(alertasRespondidos, alertasNovos);
    }
}
