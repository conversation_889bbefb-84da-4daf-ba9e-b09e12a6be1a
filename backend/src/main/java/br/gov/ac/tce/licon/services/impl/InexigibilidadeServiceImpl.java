package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.ArquivoDTO;
import br.gov.ac.tce.licon.dtos.EmailDTO;
import br.gov.ac.tce.licon.dtos.mapper.ArquivoInexigibilidadeToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.*;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchParameter;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.SearchOperator;
import br.gov.ac.tce.licon.dtos.responses.BuscaResponse;
import br.gov.ac.tce.licon.entities.*;
import br.gov.ac.tce.licon.entities.enums.*;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.*;
import br.gov.ac.tce.licon.services.*;
import br.gov.ac.tce.licon.services.specs.InexigibilidadeSpecification;
import br.gov.ac.tce.licon.utils.GeradorMensagemEmail;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class InexigibilidadeServiceImpl extends AbstractUploadTipoServiceImpl<Inexigibilidade, InexigibilidadeFiltroRequest, InexigibilidadeRepository, ArquivoInexigibilidadeFileService, ArquivoInexigibilidade, ArquivoInexigibilidadeFiltroRequest, ArquivoInexigibilidadeService, ArquivoInexigibilidadeDTO, ArquivoInexigibilidadeToDtoMapper, TipoArquivoInexigibilidade> implements InexigibilidadeService {

    @Inject
    private ArquivoInexigibilidadeService arquivoInexigibilidadeService;

    @Inject
    private ArquivoInexigibilidadeFileService arquivoInexigibilidadeFileService;

    @Autowired
    private InexigibilidadeRepository repository;

    @Autowired
    private InexigibilidadeLicitanteRepository inexigibilidadeLicitanteRepository;

    @Autowired
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Autowired
    private ArquivoInexigibilidadeToDtoMapper arquivoInexigibilidadeToDtoMapper;

    @Autowired
    private TdaInexigibilidadeRepository tdaRepository;

    @Autowired
    private IEmailService emailService;

    @Autowired
    private TermoReferenciaService termoReferenciaService;

    @Autowired
    private TermoReferenciaRepository termoReferenciaRepository;

    @Autowired
    private ObraRepository obraRepository;

    @Autowired
    private AnaliseProcessoViewServiceImpl analiseProcessoViewService;

    @Autowired
    private AnulacaoRevogacaoService anulacaoRevogacaoService;

    @Override
    public InexigibilidadeRepository getRepository() {
        return repository;
    }

    @Override
    protected Example<Inexigibilidade> obterExemploChecarSeJahExiste(Inexigibilidade entity) throws AppException {
        Inexigibilidade exemplo = Inexigibilidade.builder().numeroProcesso(entity.getNumeroProcesso()).build();
        return Example.of(exemplo);
    }

    @Override
    protected void lancarErroEntidadeJahExistente(Inexigibilidade entity) throws AppException {
        throw new AppException(String.format("Inexigibilidade com dado número de processo já existe: %s para a entidade %s.", entity.getNumeroProcesso(), entity.getEntidade().getNome()), HttpStatus.UNPROCESSABLE_ENTITY);
    }

    @Override
    protected Specification<Inexigibilidade> getSpecification(InexigibilidadeFiltroRequest filtro) {
        return new InexigibilidadeSpecification(filtro);
    }

    @Override
    protected void beforeSave(Inexigibilidade entity) {
        if (entity.getId() == null) {
            entity.setDataCadastro(LocalDateTime.now());
            entity.setUsuario(getCurrentUser());
            entity.setStatus(StatusLicitacao.PUBLICADA);
        }
    }

    public void saveInexigibilidade(InexigibilidadeDTO dto) throws AppException {
        Inexigibilidade inexigibilidade = dto.getInexigibilidade();
        List<ArquivoInexigibilidadeDTO> arquivos = dto.getArquivosInexigibilidade();
        this.validarArquivos(arquivos, inexigibilidade.getLei());
        TermoReferencia termoReferencia = inexigibilidade.getTermoReferencia();
        if (inexigibilidade.getId() == null && termoReferencia != null && termoReferenciaRepository.checkAssociatedTerm(termoReferencia.getId())) {
            throw new AppException("A operação não pôde ser concluída, pois o Termo de Referência escolhido já está associado a algum processo.", HttpStatus.UNPROCESSABLE_ENTITY);
        }

        if (!inexigibilidade.getLei().equals("LEI_N_8666") && (termoReferencia == null || termoReferencia.getIdentificadorProcesso() == null)) {
            throw new AppException("A operação não pôde ser concluída, pois o Termo de Referência escolhido deve possuir um identificador.", HttpStatus.UNPROCESSABLE_ENTITY);
        }

        if (termoReferencia != null && !termoReferencia.getIsFinalizado()) {
            termoReferencia = termoReferenciaService.finalizarTermo(termoReferencia, "Inexigibilidade");
            inexigibilidade.setTermoReferencia(termoReferencia);
        }

        Edificacao edificacao;
        Obra obra;

        if (dto.getObra() != null) {
            obra = inexigibilidade.getObra();
            edificacao = Optional.ofNullable(obra.getEdificacao()).orElse(new Edificacao());
            edificacao.setGeometry(dto.getObra().getTipoCamada(), dto.getObra().getCoordenadas());
            edificacao.setObra(obra);
            obra.setEdificacao(edificacao);
            inexigibilidade.setObra(obra);
            inexigibilidade.getObra().setInexigibilidade(inexigibilidade);
        } else if (dto.getEdificacao() != null) {
            edificacao = dto.getEdificacao();
            obra = inexigibilidade.getObra();
            obra.setEdificacao(edificacao);
            edificacao.setObra(obra);
            inexigibilidade.setObra(obra);
            inexigibilidade.getObra().setInexigibilidade(inexigibilidade);
        }
        inexigibilidade = save(inexigibilidade);
        saveArquivos(arquivos, inexigibilidade);
    }

    @Override
    public void remover(Long id) throws AppException {
        Optional<Inexigibilidade> entidadeOpt = getRepository().findById(id);
        if (entidadeOpt.isPresent()) {
            Inexigibilidade inx = entidadeOpt.get();
            validarRemover(inx);
            inx.setStatus(StatusLicitacao.REMOVIDA);
            getRepository().save(inx);
        }
    }

    @Override
    public BuscaResponse<Inexigibilidade> buscarAdvanced(AdvancedSearchRequest filtro) {
        if (filtro.getAndParameters().size() == 0) {
            setDefaultFilterParams(filtro);
        }
        return super.buscarAdvanced(filtro);
    }

    @Override
    protected void setDefaultFilterParams(AdvancedSearchRequest filtro) {
        AdvancedSearchParameter situacaoParam = new AdvancedSearchParameter("status", SearchOperator.NOT_EQUAL_TO, StatusLicitacao.REMOVIDA.name());
        String[] idEntidadesContexto = ThreadContext.get("entities").replace("[", "").replace("]", "").split(", ");
        for (String id : idEntidadesContexto) {
            if (!id.isEmpty()) {
                AdvancedSearchParameter entidadeParam = new AdvancedSearchParameter("entidade", SearchOperator.EQUAL_TO, Long.valueOf(id));
                filtro.getOrParameters().add(entidadeParam);
            }
        }
        filtro.getAndParameters().add(situacaoParam);
    }

    @Override
    public ArquivoInexigibilidadeService getArquivoService() {
        return arquivoInexigibilidadeService;
    }

    @Override
    public ArquivoInexigibilidadeFileService getFileService() {
        return arquivoInexigibilidadeFileService;
    }

    @Override
    public ArquivoInexigibilidadeToDtoMapper getMapper() {
        return arquivoInexigibilidadeToDtoMapper;
    }

    @Override
    protected ArquivoInexigibilidade getNewArquivo() {
        return new ArquivoInexigibilidade();
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoInexigibilidade arquivoEntity, Inexigibilidade entity, ArquivoInexigibilidadeDTO arquivoUpload) {
        arquivoEntity.setInexigibilidade(entity);
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    @Override
    public List<ItemContrato> itensEmContratos(Long idInexigibilidade) {
        return this.repository.itensEmContratos(idInexigibilidade);
    }

    @Override
    public Inexigibilidade getById(Long idInexigibilidade) {
        Inexigibilidade inexigibilidade = super.getById(idInexigibilidade);
        inexigibilidade.setTda(tdaRepository.getByIdInexigibilidade(idInexigibilidade));
        return inexigibilidade;
    }

    @Override
    public UltimaAlteracaoDTO getUltimaAlteracao(Long id) {
        List<Object[]> ultimasModificadores = repository.getUltimaAlteracao(id);
        if (ultimasModificadores.size() > 0) {
            UltimaAlteracaoDTO response = new UltimaAlteracaoDTO();
            Object[] ultimaModificacao = ultimasModificadores.get(0);
            response.setData(((Timestamp) ultimaModificacao[1]).toLocalDateTime());
            response.setNome((String) ultimaModificacao[0]);
            return response;
        } else {
            return null;
        }
    }

    @Override
    public Boolean getTresCasasDecimais(Long idInexigibilidade) {
        return getRepository().getTresCasasDecimais(idInexigibilidade);
    }

    public ArquivoBinarioDTO download(ArquivoDTO arquivoDTO, Boolean countDownloads) throws AppException {
        ArquivoBinarioDTO download = arquivoInexigibilidadeFileService.download(arquivoDTO);
        if (arquivoDTO.ehArquivoDefinitivoValido() && Boolean.TRUE.equals(countDownloads)) {
            Long idArquivoInexigibilidade = Long.parseLong(arquivoDTO.getLookupId());
            repository.updateNumeroDownloadsInexigibilidade(idArquivoInexigibilidade);
        }
        return download;

    }

    @Override
    public void setEmAnalise(Long idProcesso, Boolean emAnalise) {
        LocalDateTime dataAnalise = LocalDateTime.now();
        if (!emAnalise) {
            dataAnalise = null;
        }
        this.repository.updateEmAnalise(idProcesso, emAnalise, dataAnalise);
        this.repository.updateLastAuditTimestamp(idProcesso);
    }

    @Override
    public void desarquivarInexigibilidade(Long idProcesso) {
        this.repository.desarquivarInexigibilidade(idProcesso, LocalDateTime.now());
        this.repository.updateLastAuditTimestamp(idProcesso);
    }

    @Override
    public void validarArquivos(List<ArquivoInexigibilidadeDTO> arquivos, String lei) {
        List<TipoArquivoInexigibilidade> tiposArquivosEnviados = extrairTiposArquivos(arquivos);
        this.verificarDescricaoTipoOutros(arquivos);
        this.verificarArquivosObrigatorios(tiposArquivosEnviados, this.getArquivosTiposObrigatorios(lei));
        if (arquivos.stream().anyMatch(arquivo -> arquivo.getTipo().equals(TipoArquivoInexigibilidade.PROJETO_ENGENHARIA) && !checkTypeDWG(arquivo.getArquivo().getTipoArquivo()))) {
            throw new AppException("O arquivo do tipo 'Projetos de engenharia (básico, executivo e afins)' deve ser em formato DWG", HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() throws AppException {
        AdvancedSearchRequest filtro = new AdvancedSearchRequest();
        this.inicializaFiltro(filtro);
        AdvancedSearchParameter arquivoObjetoParam = new AdvancedSearchParameter("objeto", SearchOperator.EQUAL_TO, Objeto.INEXIGIBILIDADE.name());
        filtro.getAndParameters().add(arquivoObjetoParam);

        return obrigatoriedadeArquivoService.buscarAdvanced(filtro).getItems();
    }

    @Override
    protected void validateCollectionsRemoval(Inexigibilidade object, Inexigibilidade persistedObject) {
        validateObrasRemoval(object, persistedObject);
    }

    private void validateObrasRemoval(Inexigibilidade object, Inexigibilidade persistedObject) {
        if (persistedObject.getObra() != null && (object.getObra() == null || !persistedObject.getObra().equals(object.getObra()))) {
            obraRepository.deleteById(persistedObject.getObra().getId());
        }
    }

    private List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios(String lei) throws AppException {
        ArrayList<String> filtros = new ArrayList<>();
        filtros.add(lei);
        List<ObrigatoriedadeArquivo> obrigatoriedadeArquivos = this.obrigatoriedadeArquivoService.getArquivosObrigatorios("INEXIGIBILIDADE", filtros);
        return obrigatoriedadeArquivos.stream().filter(ObrigatoriedadeArquivo::getObrigatorio).collect(Collectors.toList());
    }

    @Override
    protected void afterSave(boolean isNew, Inexigibilidade entity) {
        if (isNew) {
            Usuario usuario = getCurrentUser();
            EmailDTO emailDTO = new EmailDTO();
            emailDTO.setUsuario(usuario.getLogin());
            emailDTO.setSistema(app);
            emailDTO.setAssunto("Confirmação de Cadastro");
            emailDTO.setDestinatarios(new HashSet<>(Collections.singletonList(usuario.getEmail())));
            String naturezas = "";
            if (entity.getNaturezasDoObjeto() != null) {
                naturezas = StringUtils.join(entity.getNaturezasDoObjeto().stream().map(n -> n.getValor()).collect(Collectors.toList()), ", ");
            }
            String mensagem = String.format(GeradorMensagemEmail.getMensagemCadastroInexigibilidade(), usuario.getNome(), entity.getDataCadastro().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")), entity.getNumeroProcesso(), naturezas, entity.getObjeto());
            emailDTO.setMensagem(mensagem);
            emailService.sendEmail(emailDTO);
        }
        repository.updateLastModifiedTimestamp(entity.getId());
    }

    @Override
    public void arquivarProcesso(Long idInexigibilidade, StatusAuditoriaLicitacao status) {
        if (isUsuarioInspetor() || ThreadContext.get("groups").equals("Administrador")) {
            Optional<Inexigibilidade> optInexigibilidade = getRepository().findById(idInexigibilidade);
            if (optInexigibilidade.isPresent()) {
                Inexigibilidade inexigibilidade = optInexigibilidade.get();
                inexigibilidade.setStatusProcessoArquivado(status);
                getRepository().save(inexigibilidade);
                getRepository().updateLastAuditTimestamp(idInexigibilidade);
            }
        } else {
            throw new AppException("O usuário não tem permissão para realizar a operação", HttpStatus.UNAUTHORIZED);
        }
    }

    @Override
    public List<Integer> getAnosInexigibilidade() {
        List<Integer> years = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (int year = 2002; year <= now.getYear(); year++) {
            years.add(year);
        }
        if (now.getMonth().getValue() == 12) {
            years.add(now.getYear() + 1);
        }
        years.sort(Collections.reverseOrder());
        return years;
    }

    public Long getAlertaInexigibilidade(Long idProcesso, String tipoProcesso) {
        AnaliseProcessoView alerta = analiseProcessoViewService.getProcesso(idProcesso, tipoProcesso);
        if (alerta != null) {
            return alerta.getIdAlertaAnalise();
        }
        return null;
    }

    @Override
    public void anularRevogar(Long idProcesso, AnulacaoRevogacaoDTO dto) {
        Inexigibilidade entity = repository.getById(idProcesso);
        AnulacaoRevogacao persistedAnulacaoRevogacao = anulacaoRevogacaoService.saveAnulacaoRevogacao(dto);
        entity.setAnulacaoRevogacao(persistedAnulacaoRevogacao);
        save(entity);
    }
}
