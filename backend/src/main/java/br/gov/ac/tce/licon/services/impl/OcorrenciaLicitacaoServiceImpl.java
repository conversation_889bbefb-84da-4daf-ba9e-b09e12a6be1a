package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.ArquivoDTO;
import br.gov.ac.tce.licon.dtos.mapper.ArquivoOcorrenciaLicitacaoToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.*;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchParameter;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.SearchOperator;
import br.gov.ac.tce.licon.entities.*;
import br.gov.ac.tce.licon.entities.enums.*;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.CaronaRepository;
import br.gov.ac.tce.licon.repositories.ArquivoOcorrenciaLicitacaoRepository;
import br.gov.ac.tce.licon.repositories.OcorrenciaLicitacaoRepository;
import br.gov.ac.tce.licon.services.*;
import br.gov.ac.tce.licon.services.specs.OcorrenciaLicitacaoSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Objects;

@Service
@Transactional
public class OcorrenciaLicitacaoServiceImpl
        extends AbstractUploadTipoServiceImpl<OcorrenciaLicitacao, OcorrenciaLicitacaoFiltroRequest, OcorrenciaLicitacaoRepository, ArquivoOcorrenciaLicitacaoFileService, ArquivoOcorrenciaLicitacao, ArquivoOcorrenciaLicitacaoFiltroResquest, ArquivoOcorrenciaLicitacaoService, ArquivoOcorrenciaLicitacaoDTO, ArquivoOcorrenciaLicitacaoToDtoMapper, TipoArquivoOcorrenciaLicitacao>
        implements OcorrenciaLicitacaoService {

    @Inject
    private ArquivoOcorrenciaLicitacaoFileService arquivoOcorrenciaLicitacaoFileService;

    @Inject
    private ArquivoOcorrenciaLicitacaoService arquivoOcorrenciaLicitacaoService;

    @Inject
    private LicitacaoService licitacaoService;

    @Autowired
    private ArquivoLicitacaoService arquivoLicitacaoService;

    @Autowired
    private OcorrenciaLicitacaoRepository repository;

    @Autowired
    private ArquivoOcorrenciaLicitacaoRepository arquivoOcorrenciaLicitacaoRepository;

    @Autowired
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Inject
    ArquivoOcorrenciaLicitacaoToDtoMapper arquivoOcorrenciaLicitacaoToDtoMapper;

    @Autowired
    private CaronaRepository caronaRepository;

    @Override
    public OcorrenciaLicitacaoRepository getRepository() {
        return repository;
    }

    @Override
    protected Specification<OcorrenciaLicitacao> getSpecification(OcorrenciaLicitacaoFiltroRequest filtro) {
        return new OcorrenciaLicitacaoSpecification(filtro);
    }

    @Override
    public OcorrenciaLicitacao save(OcorrenciaLicitacao entity) {
        if (entity.getId() == null) {
            entity.setDataCadastro(LocalDateTime.now());
        }

        entity.setResponsavel(getCurrentUser());

        return super.save(entity);
    }

    public void suspender(OcorrenciaLicitacaoDTO dto) throws AppException {
        Licitacao licitacao = licitacaoService.getById(dto.getIdLicitacao());
        licitacao.setNaturezaOcorrencia(TipoOcorrencia.SUSPENDER);
        licitacao.setStatusOcorrenciaAtual(dto.getTipoOcorrencia());

        licitacaoService.save(licitacao, licitacao.getFase(), getCurrentUser());

        OcorrenciaLicitacao ocorrenciaEntity = new OcorrenciaLicitacao();
        ocorrenciaEntity.setLicitacao(licitacao);
        ocorrenciaEntity.setDataAviso(dto.getDataAviso());
        ocorrenciaEntity.setMotivoOcorrencia(dto.getMotivoOcorrencia());
        ocorrenciaEntity.setTipoOcorrencia(dto.getTipoOcorrencia());
        ocorrenciaEntity.setNatureza(TipoOcorrencia.SUSPENDER);

        save(ocorrenciaEntity);

        dto.setIdOcorrencia(ocorrenciaEntity.getId());

        saveArquivos(dto.getArquivos(), ocorrenciaEntity);
    }

    public void prorrogar(OcorrenciaLicitacaoDTO dto) throws AppException {
        Licitacao licitacao = licitacaoService.getById(dto.getIdLicitacao());
        OcorrenciaLicitacao ocorrenciaEntity = new OcorrenciaLicitacao();
        ocorrenciaEntity.setDataInicialAberturaLicitacao(licitacao.getDataAbertura());

        licitacao.setNaturezaOcorrencia(TipoOcorrencia.PRORROGAR);
        licitacao.setStatusOcorrenciaAtual(dto.getTipoOcorrencia());
        licitacao.setDataAbertura(dto.getDataProrrogada());

        licitacaoService.save(licitacao, licitacao.getFase(), getCurrentUser());

        ocorrenciaEntity.setLicitacao(licitacao);
        ocorrenciaEntity.setDataAviso(dto.getDataAviso());
        ocorrenciaEntity.setDataProrrogada(dto.getDataProrrogada());
        ocorrenciaEntity.setMotivoOcorrencia(dto.getMotivoOcorrencia());
        ocorrenciaEntity.setTipoOcorrencia(dto.getTipoOcorrencia());
        ocorrenciaEntity.setNatureza(TipoOcorrencia.PRORROGAR);

        save(ocorrenciaEntity);

        dto.setIdOcorrencia(ocorrenciaEntity.getId());

        saveArquivos(dto.getArquivos(), ocorrenciaEntity);
    }

    @Transactional
    public void reabrir(OcorrenciaLicitacaoDTO dto) throws AppException {
        Licitacao licitacao = licitacaoService.getById(dto.getIdLicitacao());
        licitacao.setNaturezaOcorrencia(TipoOcorrencia.REABRIR);
        licitacao.setStatusOcorrenciaAtual(StatusOcorrenciaLicitacao.REABRIR);
        licitacao.setDataAbertura(dto.getDataProrrogada());
        licitacao.setValorAdjudicado(null);
        this.handleCaronasAssociadas(licitacao);

        OcorrenciaLicitacao ocorrenciaEntity = new OcorrenciaLicitacao();
        ocorrenciaEntity.setLicitacao(licitacao);
        ocorrenciaEntity.setDataProrrogada(dto.getDataProrrogada());
        ocorrenciaEntity.setDataAviso(dto.getDataAviso());
        ocorrenciaEntity.setMotivoOcorrencia(dto.getMotivoOcorrencia());
        ocorrenciaEntity.setTipoOcorrencia(StatusOcorrenciaLicitacao.REABRIR);
        ocorrenciaEntity.setNatureza(TipoOcorrencia.REABRIR);

        ocorrenciaEntity.setLicitantesLicitacao(new ArrayList<>());
        for (Licitante licitante : licitacao.getLicitantes()) {
            ocorrenciaEntity.getLicitantesLicitacao().add(licitante);
        }

        ocorrenciaEntity.setVencedores(new ArrayList<>());

        for (VencedorLicitacao vencedorLicitacao : licitacao.getVencedores()) {
            OcorrenciaLicitacaoVencedor ocorrenciaLicitacaoVencedor;
            if (vencedorLicitacao.getItemLote() != null) {
                ocorrenciaLicitacaoVencedor = getOcorrenciaLicitacaoVencedor(vencedorLicitacao, ocorrenciaEntity);
            } else {
                ocorrenciaLicitacaoVencedor = new OcorrenciaLicitacaoVencedor(
                        new OcorrenciaItemLote(),
                        "",
                        vencedorLicitacao.getLicitante(),
                        ocorrenciaEntity,
                        vencedorLicitacao.getLicitacao(),
                        vencedorLicitacao.getValor(),
                        BigDecimal.ZERO,
                        vencedorLicitacao.getValorUnitario(),
                        "",
                        "",
                        "",
                        BigDecimal.ZERO);
            }
            ocorrenciaEntity.getVencedores().add(ocorrenciaLicitacaoVencedor);
        }

        ocorrenciaEntity.setItensLotesFracassados(new ArrayList<>());
        for (ItemLoteFracassado itemLoteFracassado : licitacao.getLotesFracassados()) {
            OcorrenciaItemLoteFracassado ocorrenciaItemLoteFracassado = getOcorrenciaItemLoteFracassado(itemLoteFracassado, ocorrenciaEntity);

            ocorrenciaEntity.getItensLotesFracassados().add(ocorrenciaItemLoteFracassado);
        }

        ocorrenciaEntity.setItensLotesDesertos(new ArrayList<>());
        for (ItemDeserto itemDeserto : licitacao.getItensDesertos()) {
            OcorrenciaItemLote ocorrenciaItemLote = new OcorrenciaItemLote(
                    itemDeserto.getItemLote().getMaterialDetalhamento(),
                    itemDeserto.getQuantidade(),
                    itemDeserto.getItemLote().getDescricaoComplementar(),
                    itemDeserto.getItemLote().getUsuario(),
                    itemDeserto.getItemLote().getDataCadastro(),
                    itemDeserto.getItemLote().getValorUnitarioEstimado(),
                    itemDeserto.getItemLote().getQuantidadeConsumo(),
                    itemDeserto.getItemLote().getNumero(),
                    itemDeserto.getItemLote().getFracionario(),
                    itemDeserto.getItemLote().getUnidadeMedida()
            );

            OcorrenciaItemLoteDeserto ocorrenciaItemLoteDeserto = new OcorrenciaItemLoteDeserto(ocorrenciaItemLote, itemDeserto.getItemLote().getLote().getNome(), ocorrenciaEntity);
            ocorrenciaEntity.getItensLotesDesertos().add(ocorrenciaItemLoteDeserto);
        }

        List<ArquivoLicitacao> arquivosLicitacao = arquivoLicitacaoService.buscarPor(dto.getIdLicitacao());
        List<ArquivoOcorrenciaLicitacao> arquivosOcorrenciaLicitacao;

        if (licitacao.getLei().equals("LEI_N_8666") && licitacao.getProcessoMigrado()) {
            arquivosOcorrenciaLicitacao = this.handleArquivosLeiAntiga(licitacao, arquivosLicitacao);
        } else {
            arquivosOcorrenciaLicitacao = arquivosLicitacao
                    .stream()
                    .filter((arquivo) -> (arquivo.getFase() != null && arquivo.getFase().equals(FaseLicitacao.APRESENTACAO_PROPOSTAS_LANCES)) ||
                            arquivo.getFase() != null && arquivo.getFase().equals(FaseLicitacao.FINALIZACAO))
                    .map((arquivo) -> {
                        ArquivoOcorrenciaLicitacao arquivoOcorrenciaLicitacao = new ArquivoOcorrenciaLicitacao();
                        arquivoOcorrenciaLicitacao.setTipo(TipoArquivoOcorrenciaLicitacao.valueOf(arquivo.getTipo().name()));
                        arquivoOcorrenciaLicitacao.setDiretorio(arquivo.getDiretorio());
                        arquivoOcorrenciaLicitacao.setDescricao(arquivo.getDescricao());
                        arquivoOcorrenciaLicitacao.setDataEnvio(arquivo.getDataEnvio());
                        arquivoOcorrenciaLicitacao.setFase(arquivo.getFase());
                        arquivoOcorrenciaLicitacao.setNome(arquivo.getNome());
                        arquivoOcorrenciaLicitacao.setTipoArquivo(arquivo.getTipoArquivo());
                        return arquivoOcorrenciaLicitacao;
                    }).collect(Collectors.toList());
        }

        ocorrenciaEntity = save(ocorrenciaEntity);

        licitacao.getLicitantes().clear();
        licitacao.getLotesFracassados().clear();
        licitacao.getVencedores().clear();
        licitacao.getItensDesertos().clear();
        licitacao.setFase(FaseLicitacao.DIVULGACAO_PUBLICACAO_LICITACAO);
        licitacaoService.save(licitacao, licitacao.getFase(), getCurrentUser());

        dto.setIdOcorrencia(ocorrenciaEntity.getId());

        saveArquivos(dto.getArquivos(), ocorrenciaEntity);

        for (ArquivoOcorrenciaLicitacao arquivoLicitacao : arquivosOcorrenciaLicitacao) {
            arquivoLicitacao.setOcorrencia(ocorrenciaEntity);
            this.arquivoOcorrenciaLicitacaoRepository.save(arquivoLicitacao);
        }

        if (licitacao.getLei().equals("LEI_N_8666") && licitacao.getProcessoMigrado()) {
            List<String> arquivosFaseApresentacaoFinalizacao = this.getTiposArquivosApresentacaoFinalizacao(licitacao);

            for (ArquivoLicitacao arquivo : arquivoLicitacaoService.buscarPor(dto.getIdLicitacao())) {
                if (arquivosFaseApresentacaoFinalizacao.contains(arquivo.getTipo().name()) && !arquivo.getTipo().equals(TipoArquivoLicitacao.OUTROS_DOCUMENTOS.name())) {
                    arquivoLicitacaoService.remover(arquivo.getId());
                }
            }
        } else {
            for (ArquivoLicitacao arquivo : arquivoLicitacaoService.buscarPor(dto.getIdLicitacao())) {
                if ((arquivo.getFase() != null && arquivo.getFase().equals(FaseLicitacao.APRESENTACAO_PROPOSTAS_LANCES)) ||
                        (arquivo.getFase() != null && arquivo.getFase().equals(FaseLicitacao.FINALIZACAO))) {
                    arquivoLicitacaoService.remover(arquivo.getId());
                }
            }
        }
    }

    private List<ArquivoOcorrenciaLicitacao> handleArquivosLeiAntiga(Licitacao licitacao, List<ArquivoLicitacao> arquivosLicitacao) {
        List<String> tiposArquivosValidos = this.getTiposArquivosApresentacaoFinalizacao(licitacao);

        return arquivosLicitacao
                .stream()
                .filter((arquivo) -> (tiposArquivosValidos.contains(arquivo.getTipo().name()) && !arquivo.getTipo().equals(TipoArquivoLicitacao.OUTROS_DOCUMENTOS.name())))
                .map((arquivo) -> {
                    ArquivoOcorrenciaLicitacao arquivoOcorrenciaLicitacao = new ArquivoOcorrenciaLicitacao();
                    arquivoOcorrenciaLicitacao.setTipo(TipoArquivoOcorrenciaLicitacao.valueOf(arquivo.getTipo().name()));
                    arquivoOcorrenciaLicitacao.setDiretorio(arquivo.getDiretorio());
                    arquivoOcorrenciaLicitacao.setDescricao(arquivo.getDescricao());
                    arquivoOcorrenciaLicitacao.setDataEnvio(arquivo.getDataEnvio());
                    arquivoOcorrenciaLicitacao.setNome(arquivo.getNome());
                    arquivoOcorrenciaLicitacao.setTipoArquivo(arquivo.getTipoArquivo());
                    return arquivoOcorrenciaLicitacao;
                }).collect(Collectors.toList());
    }

    private List<String> getTiposArquivosApresentacaoFinalizacao(Licitacao licitacao) {
        ArrayList<String> filtrosApresentacao = new ArrayList<>(Arrays.asList("LEI_N_8666", "APRESENTACAO_PROPOSTAS_LANCES"));
        ArrayList<String> filtrosFinalizacao = new ArrayList<>(Arrays.asList("LEI_N_8666", "FINALIZACAO"));

        if (licitacao.getSrp()) {
            filtrosFinalizacao.add("SRP");
        }

        List<ObrigatoriedadeArquivo> arquivosFaseApresentacao = this.obrigatoriedadeArquivoService.getArquivosObrigatorios("LICITACAO", filtrosApresentacao);
        List<ObrigatoriedadeArquivo> arquivosFaseFinalizacao = this.obrigatoriedadeArquivoService.getArquivosObrigatorios("LICITACAO", filtrosFinalizacao);

        List<ObrigatoriedadeArquivo> arquivos = new ArrayList<>();
        arquivos.addAll(arquivosFaseApresentacao);
        arquivos.addAll(arquivosFaseFinalizacao);

        List<String> tiposArquivosValidos = arquivos.stream().map((arquivo) -> arquivo.getArquivoEnum()).collect(Collectors.toList());

        return tiposArquivosValidos;
    }

    private static OcorrenciaItemLoteFracassado getOcorrenciaItemLoteFracassado(ItemLoteFracassado itemLoteFracassado, OcorrenciaLicitacao ocorrenciaEntity) {
        OcorrenciaItemLote ocorrenciaItemLote = new OcorrenciaItemLote(
                itemLoteFracassado.getItemLote().getMaterialDetalhamento(),
                itemLoteFracassado.getItemLote().getQuantidade(),
                itemLoteFracassado.getItemLote().getDescricaoComplementar(),
                itemLoteFracassado.getItemLote().getUsuario(),
                itemLoteFracassado.getItemLote().getDataCadastro(),
                itemLoteFracassado.getItemLote().getValorUnitarioEstimado(),
                itemLoteFracassado.getItemLote().getQuantidadeConsumo(),
                itemLoteFracassado.getItemLote().getNumero(),
                itemLoteFracassado.getItemLote().getFracionario(),
                itemLoteFracassado.getItemLote().getUnidadeMedida()
        );

        return new OcorrenciaItemLoteFracassado(
                ocorrenciaItemLote,
                itemLoteFracassado.getItemLote().getLote().getNome(),
                ocorrenciaEntity
        );
    }

    private static OcorrenciaLicitacaoVencedor getOcorrenciaLicitacaoVencedor(VencedorLicitacao vencedorLicitacao, OcorrenciaLicitacao ocorrenciaEntity) {
        OcorrenciaItemLote ocorrenciaItemLote = new OcorrenciaItemLote(
                vencedorLicitacao.getItemLote().getMaterialDetalhamento(),
                vencedorLicitacao.getItemLote().getQuantidade(),
                vencedorLicitacao.getItemLote().getDescricaoComplementar(),
                vencedorLicitacao.getItemLote().getUsuario(),
                vencedorLicitacao.getItemLote().getDataCadastro(),
                vencedorLicitacao.getItemLote().getValorUnitarioEstimado(),
                vencedorLicitacao.getItemLote().getQuantidadeConsumo(),
                vencedorLicitacao.getItemLote().getNumero(),
                vencedorLicitacao.getItemLote().getFracionario(),
                vencedorLicitacao.getItemLote().getUnidadeMedida());

        return new OcorrenciaLicitacaoVencedor(
                ocorrenciaItemLote,
                vencedorLicitacao.getItemLote().getLote().getNome(),
                vencedorLicitacao.getLicitante(),
                ocorrenciaEntity,
                vencedorLicitacao.getLicitacao(),
                vencedorLicitacao.getValor(),
                vencedorLicitacao.getQuantidade(),
                vencedorLicitacao.getValorUnitario(),
                vencedorLicitacao.getMarcaModelo(),
                vencedorLicitacao.getEspecificacao(),
                vencedorLicitacao.getObservacao(),
                vencedorLicitacao.getDesconto()
        );
    }

    private void handleCaronasAssociadas(Licitacao licitacao) {
        List<Carona> caronas = this.caronaRepository.getCaronasByLicitacao(licitacao);
        if (caronas.size() > 0) {
            for (Carona carona : caronas) {
                carona.getDetentores().clear();
                this.caronaRepository.save(carona);
            }
        }
    }

    public void continuar(OcorrenciaLicitacaoDTO dto) throws AppException {
        Licitacao licitacao = licitacaoService.getById(dto.getIdLicitacao());
        licitacao.setNaturezaOcorrencia(TipoOcorrencia.CONTINUAR);
        licitacao.setStatusOcorrenciaAtual(StatusOcorrenciaLicitacao.CONTINUAR);
        licitacaoService.save(licitacao, licitacao.getFase(), getCurrentUser());

        OcorrenciaLicitacao ocorrenciaEntity = new OcorrenciaLicitacao();
        ocorrenciaEntity.setLicitacao(licitacao);
        ocorrenciaEntity.setMotivoOcorrencia(dto.getMotivoOcorrencia());
        ocorrenciaEntity.setTipoOcorrencia(StatusOcorrenciaLicitacao.CONTINUAR);
        ocorrenciaEntity.setNatureza(TipoOcorrencia.CONTINUAR);
        save(ocorrenciaEntity);

        dto.setIdOcorrencia(ocorrenciaEntity.getId());

        if (Objects.nonNull(dto.getArquivos())) {
            saveArquivos(dto.getArquivos(), ocorrenciaEntity);
        }
    }

    public void finalizar(OcorrenciaLicitacaoDTO dto) throws AppException {
        Licitacao licitacao = licitacaoService.getById(dto.getIdLicitacao());

        LocalDate dataAbertura = licitacao.getDataAbertura().toLocalDate();
        if (dto.getDataAviso() != null) {
            LocalDate dataAviso = dto.getDataAviso().toLocalDate();
            if (!dataAviso.isAfter(dataAbertura)) {
                throw new AppException("Data de Aviso inválida.", HttpStatus.UNPROCESSABLE_ENTITY);
            }
        }
        StatusOcorrenciaLicitacao tipoOcorrencia = dto.getTipoOcorrencia();

        licitacao.setNaturezaOcorrencia(TipoOcorrencia.FINALIZAR);
        licitacao.setStatusOcorrenciaAtual(tipoOcorrencia);

        licitacaoService.save(licitacao, licitacao.getFase(), getCurrentUser());

        OcorrenciaLicitacao ocorrenciaEntity = new OcorrenciaLicitacao();
        ocorrenciaEntity.setLicitacao(licitacao);
        ocorrenciaEntity.setMotivoOcorrencia(dto.getMotivoOcorrencia());
        ocorrenciaEntity.setTipoOcorrencia(tipoOcorrencia);
        ocorrenciaEntity.setNatureza(TipoOcorrencia.FINALIZAR);

        if (tipoOcorrencia.equals(StatusOcorrenciaLicitacao.ANULADA) || tipoOcorrencia.equals(StatusOcorrenciaLicitacao.REVOGADA)) {
            ocorrenciaEntity.setDataAviso(dto.getDataAviso());
        }
        if (tipoOcorrencia.equals(StatusOcorrenciaLicitacao.FRACASSADA)) {
            ocorrenciaEntity.setLicitantesLicitacao(dto.getLicitantes());
        }

        save(ocorrenciaEntity);

        dto.setIdOcorrencia(ocorrenciaEntity.getId());

        saveArquivos(dto.getArquivos(), ocorrenciaEntity);
    }

    @Override
    public List<OcorrenciaLicitacao> getAllByLicitacao(Long idLicitacao) {
        return repository.findAllByLicitacaoId(idLicitacao);
    }

    @Override
    public List<String> getPendenciasLicitacao(Long idLicitacao) {
        Licitacao licitacao = licitacaoService.getById(idLicitacao);
        List<Carona> caronas = caronaRepository.getCaronasByLicitacao(licitacao);
        List<String> result = new ArrayList<>();

        for (Carona carona : caronas) {
            StringBuilder titleBuilder = new StringBuilder();
            titleBuilder.append(carona.getEntidade().getNome()).append(" - ");
            titleBuilder.append("Carona: ").append(carona.getNumeroProcessoAdministrativo());
            result.add(titleBuilder.toString());
        }
        return result;
    }

    @Override
    void copiarParaLocalDefinitivo(OcorrenciaLicitacao entity, ArquivoDTO dto, ArquivoOcorrenciaLicitacao arquivoEntity) {
        this.arquivoOcorrenciaLicitacaoFileService.copiarParaLocalDefinitivo(entity.getLicitacao().getId(), entity.getId(), dto, arquivoEntity);
    }

    @Override
    public ArquivoOcorrenciaLicitacaoService getArquivoService() {
        return arquivoOcorrenciaLicitacaoService;
    }

    @Override
    public ArquivoOcorrenciaLicitacaoFileService getFileService() {
        return arquivoOcorrenciaLicitacaoFileService;
    }

    @Override
    public ArquivoOcorrenciaLicitacaoToDtoMapper getMapper() {
        return arquivoOcorrenciaLicitacaoToDtoMapper;
    }

    @Override
    protected ArquivoOcorrenciaLicitacao getNewArquivo() {
        return new ArquivoOcorrenciaLicitacao();
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() {
        AdvancedSearchRequest filtro = new AdvancedSearchRequest();
        this.inicializaFiltro(filtro);
        AdvancedSearchParameter arquivoObjetoParam = new AdvancedSearchParameter("objeto", SearchOperator.EQUAL_TO, Objeto.OCORRENCIA_LICITACAO.name());
        filtro.getAndParameters().add(arquivoObjetoParam);

        return obrigatoriedadeArquivoService.buscarAdvanced(filtro).getItems();
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoOcorrenciaLicitacao arquivoEntity, OcorrenciaLicitacao entity, ArquivoOcorrenciaLicitacaoDTO arquivoUpload) {
        arquivoEntity.setOcorrencia(entity);
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }
}
