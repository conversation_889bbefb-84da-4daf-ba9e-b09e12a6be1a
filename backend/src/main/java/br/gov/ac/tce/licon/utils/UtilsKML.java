package br.gov.ac.tce.licon.utils;

import br.gov.ac.tce.licon.exceptions.AppException;
import com.vividsolutions.jts.geom.Geometry;
import com.vividsolutions.jts.io.ParseException;
import com.vividsolutions.jts.io.WKTReader;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class UtilsKML {

    public static List<String> extractGeomsFromKML(InputStream inputStream) {
        try {
            List<String> geoms = new ArrayList<>();
            DocumentBuilder db = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            Document d = db.parse(inputStream);
            processGeometry(d, geoms);
            return geoms;
        } catch (Exception e) {
            throw new AppException("Erro ao processar arquivo de georreferenciamento.");
        }
    }

    private static boolean isTag(String tag, Node node) {
        return node.getNodeName().equalsIgnoreCase(tag);
    }

    private static void processGeometry(Node parent, List<String> geoms) {
        NodeList nodeList = parent.getChildNodes();
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node node = nodeList.item(i);
            if (isTag("LINESTRING", node)) {
                processLineString(node, geoms);
            } else if (isTag("POINT", node)) {
                processPoint(node, geoms);
            } else if (isTag("POLYGON", node)) {
                processPolygon(node, geoms);
            } else {
                processGeometry(node, geoms);
            }
        }
    }

    private static void processLineString(Node parent, List<String> geoms) {
        NodeList nodeList = parent.getChildNodes();
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node node = nodeList.item(i);
            if (isTag("COORDINATES", node)) {
                geoms.add(String.format("LINESTRING (%s)", processCoordinates(node.getTextContent().strip())));
            } else {
                processLineString(node, geoms);
            }
        }
    }

    private static void processPoint(Node parent, List<String> geoms) {
        NodeList nodeList = parent.getChildNodes();
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node node = nodeList.item(i);
            if (isTag("COORDINATES", node)) {
                geoms.add(String.format("POINT (%s)", processCoordinates(node.getTextContent().strip())));
            } else {
                processPoint(node, geoms);
            }
        }
    }

    private static void processPolygon(Node parent, List<String> geoms) {
        NodeList nodeList = parent.getChildNodes();
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node node = nodeList.item(i);
            if (isTag("COORDINATES", node)) {
                geoms.add(String.format("POLYGON ((%s))", processCoordinates(node.getTextContent().strip())));
            } else {
                processPolygon(node, geoms);
            }
        }
    }

    private static String processCoordinates(String coordinates) {
        String[] tokens = coordinates.split(" ");
        return Arrays.stream(tokens).map(token -> {
            String[] ponto = token.split(",");
            return String.format("%s %s", ponto[0], ponto[1]);
        }).collect(Collectors.joining(","));
    }

    public static Geometry extractGeomFromKML(InputStream inputStream) {
        Geometry geometry = null;
        List<String> geoms = UtilsKML.extractGeomsFromKML(inputStream);
        if (!geoms.isEmpty()) {
            WKTReader wktReader = new WKTReader();
            try {
                geometry = wktReader.read(geoms.get(0));
                geometry.setSRID(4674);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
        return geometry;
    }
}
