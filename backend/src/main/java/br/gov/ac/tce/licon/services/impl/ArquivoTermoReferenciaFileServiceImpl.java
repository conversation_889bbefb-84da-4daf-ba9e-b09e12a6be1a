package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.entities.Arquivo;
import br.gov.ac.tce.licon.entities.enums.TipoEntidade;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.services.ArquivoTermoReferenciaFileService;
import br.gov.ac.tce.licon.services.ArquivoTermoReferenciaService;
import com.j256.simplemagic.ContentType;
import org.springframework.stereotype.Service;

import javax.inject.Inject;

@Service
public class ArquivoTermoReferenciaFileServiceImpl extends AbstractFileServiceImpl implements ArquivoTermoReferenciaFileService {

    @Inject
    private ArquivoTermoReferenciaService arquivoTermoReferenciaService;

    @Override
    protected Arquivo lookupArquivoParaDownload(Long idArquivo) throws AppException {
        return arquivoTermoReferenciaService.getById(idArquivo);
    }

    @Override
    protected TipoEntidade getTipoEntidade() {
        return TipoEntidade.TERMO_REFERENCIA;
    }

    @Override
    protected ContentType[] tiposValidos() {
        return new ContentType[]{
                ContentType.ZIP,
                ContentType.MICROSOFT_WORD,
                ContentType.MICROSOFT_WORD_XML,
                ContentType.PDF
        };
    }
}
