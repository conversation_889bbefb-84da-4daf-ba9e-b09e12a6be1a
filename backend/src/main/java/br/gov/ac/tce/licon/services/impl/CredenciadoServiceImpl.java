package br.gov.ac.tce.licon.services.impl;

import br.gov.ac.tce.licon.dtos.ArquivoBinarioDTO;
import br.gov.ac.tce.licon.dtos.ArquivoDTO;
import br.gov.ac.tce.licon.dtos.mapper.ArquivoCredenciadoToDtoMapper;
import br.gov.ac.tce.licon.dtos.requests.ArquivoCredenciadoDTO;
import br.gov.ac.tce.licon.dtos.requests.ArquivoCredenciadoFiltroRequest;
import br.gov.ac.tce.licon.dtos.requests.CredenciadoDTO;
import br.gov.ac.tce.licon.dtos.requests.CredenciadoFiltroRequest;
import br.gov.ac.tce.licon.dtos.requests.advancedSearch.AdvancedSearchRequest;
import br.gov.ac.tce.licon.dtos.responses.BuscaResponse;
import br.gov.ac.tce.licon.entities.*;
import br.gov.ac.tce.licon.entities.enums.TipoArquivoCredenciado;
import br.gov.ac.tce.licon.exceptions.AppException;
import br.gov.ac.tce.licon.repositories.CredenciadoRepository;
import br.gov.ac.tce.licon.repositories.CredenciamentoRepository;
import br.gov.ac.tce.licon.services.ArquivoCredenciadoFileService;
import br.gov.ac.tce.licon.services.ArquivoCredenciadoService;
import br.gov.ac.tce.licon.services.CredenciadoService;
import br.gov.ac.tce.licon.services.ObrigatoriedadeArquivoService;
import br.gov.ac.tce.licon.services.specs.CredenciadoSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class CredenciadoServiceImpl extends AbstractUploadTipoServiceImpl<Credenciado, CredenciadoFiltroRequest, CredenciadoRepository, ArquivoCredenciadoFileService, ArquivoCredenciado, ArquivoCredenciadoFiltroRequest, ArquivoCredenciadoService, ArquivoCredenciadoDTO, ArquivoCredenciadoToDtoMapper, TipoArquivoCredenciado> implements CredenciadoService {

    @Inject
    private ArquivoCredenciadoService arquivoCredenciadoService;

    @Inject
    private ArquivoCredenciadoFileService arquivoCredenciadoFileService;

    @Autowired
    private ObrigatoriedadeArquivoService obrigatoriedadeArquivoService;

    @Autowired
    private ArquivoCredenciadoToDtoMapper arquivoCredenciadoToDtoMapper;

    @Autowired
    private CredenciadoRepository repository;

    @Autowired
    private CredenciamentoRepository credenciamentoRepository;

    @Override
    public CredenciadoRepository getRepository() {
        return repository;
    }

    @Override
    public BuscaResponse<Credenciado> buscarAdvanced(AdvancedSearchRequest filtro) {
        setDefaultFilterParams(filtro);
        BuscaResponse<Credenciado> buscaResponse = super.buscarAdvanced(filtro);
        buscaResponse.getItems().forEach((credenciado -> {
            Integer hasContratoAssociado = repository.hasContratoAssociado(credenciado.getId());
            credenciado.setHasContratoAssociado(hasContratoAssociado == 1);
        }));
        return buscaResponse;
    }

    @Override
    protected void setDefaultFilterParams(AdvancedSearchRequest filtro) {
    }

    @Override
    protected Specification<Credenciado> getSpecification(CredenciadoFiltroRequest filtro) {
        return new CredenciadoSpecification(filtro);
    }

    @Override
    protected void beforeSave(Credenciado entity) {
        if (entity.getId() == null) {
            entity.setDataCadastro(LocalDateTime.now());
        }
    }

    @Override
    public void saveCredenciado(CredenciadoDTO dto) throws AppException {
        Credenciado entity = dto.getCredenciado();
        this.beforeSave(entity);
        List<ArquivoCredenciadoDTO> arquivos = dto.getArquivos();
        validarArquivos(arquivos);
        Credenciado credenciado = save(entity);
        saveArquivos(arquivos, credenciado);
        credenciamentoRepository.updateLastModifiedTimestamp(credenciado.getCredenciamento().getId());
    }

    @Override
    public List<Credenciado> getAllByCredenciamento(Long idCredenciamento) {
        return repository.getCredenciadoByCredenciamentoId(idCredenciamento);
    }

    @Override
    public List<Long> getIdsCredenciados(Long idCredenciamento) {
        return repository.getIdsCredenciados(idCredenciamento);
    }

    @Override
    public List<LocalDate> getVigenciasCredenciados(Long idCredenciamento) {
        return repository.getVigenciasCredenciados(idCredenciamento);
    }

    @Override
    public void suspender(Long idCredenciado) {
        this.repository.suspender(idCredenciado);
    }

    @Override
    public List<Credenciado> findAllByCredenciamentoAndLicitante(Long idCredenciamento, Long idLicitante) {
        return repository.findAllByCredenciamentoIdAndLicitanteId(idCredenciamento, idLicitante);
    }

    @Override
    protected void preencherDadosEspecificos(ArquivoCredenciado arquivoEntity, Credenciado entity, ArquivoCredenciadoDTO arquivoUpload) {
        arquivoEntity.setCredenciado(entity);
        super.preencherDadosEspecificos(arquivoEntity, entity, arquivoUpload);
    }

    public ArquivoBinarioDTO download(ArquivoDTO arquivoDTO, Boolean countDownloads) throws AppException {
        ArquivoBinarioDTO download = arquivoCredenciadoFileService.download(arquivoDTO);
        if (arquivoDTO.ehArquivoDefinitivoValido() && Boolean.TRUE.equals(countDownloads)) {
            Long idArquivoCredenciado = Long.parseLong(arquivoDTO.getLookupId());
            repository.updateNumeroDownloadsCredenciado(idArquivoCredenciado);
        }
        return download;
    }

    @Override
    public void validarArquivos(List<ArquivoCredenciadoDTO> arquivos) {
        List<TipoArquivoCredenciado> tiposArquivosEnviados = extrairTiposArquivos(arquivos);
        this.verificarDescricaoTipoOutros(arquivos);
        this.verificarArquivosObrigatorios(tiposArquivosEnviados, this.getArquivosTiposObrigatorios());
    }

    @Override
    public List<ObrigatoriedadeArquivo> getArquivosTiposObrigatorios() {
        List<String> filtros = new ArrayList<>();
        filtros.add("CREDENCIADO");
        List<ObrigatoriedadeArquivo> obrigatoriedadeArquivos = this.obrigatoriedadeArquivoService.getArquivosObrigatorios("CREDENCIADO", filtros);
        return obrigatoriedadeArquivos.stream().filter(ObrigatoriedadeArquivo::getObrigatorio).collect(Collectors.toList());
    }

    @Override
    public ArquivoCredenciadoService getArquivoService() {
        return arquivoCredenciadoService;
    }

    @Override
    public ArquivoCredenciadoFileService getFileService() {
        return arquivoCredenciadoFileService;
    }

    @Override
    public ArquivoCredenciadoToDtoMapper getMapper() {
        return arquivoCredenciadoToDtoMapper;
    }

    @Override
    protected ArquivoCredenciado getNewArquivo() {
        return new ArquivoCredenciado();
    }

}
