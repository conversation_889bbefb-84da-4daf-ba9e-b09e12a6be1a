app: # App
  name: LICON-DEV
  pagination:
    limit:
      default: ${PAGINATION_LIMIT_DEFAULT:20}
      max: ${PAGINATION_LIMIT_MAX:100}
  files:
    repository-full-path: /usr/tmp/arquivos_licon
    temp-folder-path: arquivos_uploads_temp
    definitive-folder-path: arquivos_uploads
    licitacoes-folder-path: licitacoes
    caronas-folder-path: caronas
    contrato-folder-path: contrato
    aditivo-contrato-folder-path: aditivo_contrato
    dispensas-folder-path: dispensas
    inexigibilidades-folder-path: inexigibilidades
    comissoes-folder-path: comissoes
    termo-referencia-folder-path: termo_referencia
    tda-licitacao-folder-path: tda_licitacao
    tda-carona-folder-path: tda_carona
    tda-dispensa-folder-path: tda_dispensa
    tda-inexigibilidade-folder-path: tda_inexigibilidade
    alerta-analise-folder-path: alerta_analise
    alerta-mensagem-folder-path: alerta_mensagem
    editais-folder-path: editais
    obra-medicao-folder-path: obra-medicao
    diario-obra-folder-path: diario-obra
    boletim-obra-folder-path: boletim-obra
    empenho-contrato-folder-path: empenho-contrato
    geoobra-obra-folder-path: geoobra_obra
    credenciamentos-folder-path: credenciamentos
    credenciados-folder-path: credenciados
  group:
    admin:
      label: "ADMINISTRADOR"
  permissao:
    admin:
      label: "ADMIN"

server:
  port: 8080
  servlet:
    context-path: /licon-api
springdoc:
  swagger-ui:
    disable-swagger-default-url: true
    url: /licon-api/v3/api-docs.yaml
spring:
  servlet:
    multipart:
      max-request-size: 210MB
      max-file-size: 100MB
      enabled: true
  datasource:
    url: ******************************************************
    username: sa
    password: Oil2005iGIS
    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
  rabbitmq:
    password: "123456"
    port: "5672"
    host: localhost
    username: admin
    listener:
      simple:
        retry:
          enabled: true
          initial-interval: 3s
          max-attempts: 3
          multiplier: 2
    queue:
      name: mq.email.dev
    exchange:
      name: mq.exchange.dev.email
    routeKey: mq.route.dev.email
  jackson:
    date-format: yyyy-MM-dd'T'HH:mm:ss
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
  elasticsearch:
    host: localhost:9200
    user: elastic
    password: teste123
    index: material_index
    idxsinapi: sinapi_index
    idxsicro: sicro_index
    semanticapi: "http://localhost:5001"
  cjurapi:
    url: "http://localhost:8082/cjur-api"
  jpa:
    hibernate:
      ddl-auto: validate
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    properties:
      org:
        hibernate:
          envers:
            default_schema: AUDITORIA
            revision_field_name: ID_AUDT
      hibernate:
        dialect: org.hibernate.spatial.dialect.sqlserver.SqlServer2008SpatialDialect
        format_sql: true
        show_sql: false
        use_sql_comments: true
        default_schema: dbo
        listeners:
          envers:
            autoRegister: true
  geoobras:
    acompanhamento:
      path: data
    mascara: data/acreMascara.json
    minio:
      endpoint: localhost
      port: 9000
      secure: false
      access-key: geoobras
      secret-key: geoobras
      bucket: geoobras-publico
