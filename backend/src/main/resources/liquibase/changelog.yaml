databaseChangeLog:
  - changeSet:
      id: 20220717124016
      author: <PERSON>
      changes:
        sqlFile:
          path: scripts/20220717124016_DBO_ALTER_EFETIVO_RESPONSAVEL_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220717124017
      author: <PERSON>
      changes:
        sqlFile:
          path: scripts/20220717124017_AUDITORIA_ALTERA_COLUNAS_RESPONSAVEL_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220717124018
      author: Thiago Moura
      changes:
        sqlFile:
          path: scripts/20220717124018_AUDITORIA_ADD_COLUNAS_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220718082730
      author: Thiago Moura
      changes:
        sqlFile:
          path: scripts/20220718082730_DBO_ALTER_ID_INEXIGIBILIDADE_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220719121030
      author: <PERSON> Let<PERSON>a
      changes:
        sqlFile:
          path: scripts/20220719121030_DBO_ADD_COLUNA_MARCA_MODELO_ITEM_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220719121135
      author: Regina Leticia
      changes:
        sqlFile:
          path: scripts/20220719121135_AUDITORIA_ADD_COLUNA_MARCA_MODELO_ITEM_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220720083400
      author: Pedro Wanderley
      changes:
        sqlFile:
          path: scripts/20220720083400_MAPEAMENTO_ELEMENTO_DESPESA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220721163431
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          path: scripts/20220721163431_COLUNA_TIPO_DISPENSA_FUNDAMENTACAO_LEGAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220718132016
      author: Andreza Santana
      changes:
        sqlFile:
          path: scripts/20220718132016_AUDITORIA_ALTER_ORIGEM_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220718134746
      author: Andreza Santana
      changes:
        sqlFile:
          path: scripts/20220718134746_DBO_CARONA_ALTER_ORIGEM_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220719090012
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220719090012_DBO_ADD_UK_CPF_RESPONSAVEL_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220721143241
      author: Mateus Cunha
      changes:
        sqlFile:
          path: scripts/20220721143241_REMOVENDO_ID_MATERIAL_PEDIDO_CATALOGO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220727085826
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          path: scripts/20220727085826_COLUNA_FORMA_LICITACAO_NULL_E_UPDATE_ARQUIVO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220721231458
      author: Brunna Amorim
      changes:
        sqlFile:
          path: scripts/20220721231458_DBO_ADD_DATA_CADASTRO_RESCISAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220728202030
      author: Daniel Leite
      changes:
        sqlFile:
          path: scripts/20220728202030_ADICAO_PERMISSAO_ADITIVO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220810185205
      author: Brunna Amorim
      changes:
        sqlFile:
          path: scripts/20220810185105_ADD_REQMOD_LICITACAO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220815145543
      author: Andreza Santana
      changes:
        sqlFile:
          path: scripts/20220815145543_ADD_COLUMNS_CARONA_NOVA_FORMATACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220812121422
      author: João Paulo Dantas
      changes:
        sqlFile:
          path: scripts/20220812121422_INSERT_NOVA_UNIDADE_MEDIDA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220808212844
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220808212844_DBO_ADD_COLUMN_OUTRO_MOTIVO_RECISAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220808212845
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220808212845_AUDITORIA_ADD_COLUMN_OUTRO_MOTIVO_RECISAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220824230251
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220824230251_DBO_ARQUIVO_COMISSAO_ADD_COLUMN_TIPO_ARQUIVO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220824230252
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220824230252_DBO_COMISSAO_ADD_ENTIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220824230253
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220824230253_DBO_CRIA_VIEW_PESSOAS_COMISSAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220824230254
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220824230254_DBO_ADICIONA_FK_COMISSAO_USUARIO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220824230255
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220824230255_AUDITORIA_ADD_ID_ENTIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220825212106
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220825212106_ADICAO_PERMISSAO_COMISSAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220825224104
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220825224104_DBO_ADD_COLUMN_TIPO_ARQUIVO_LICON.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220829181159
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220829181159_DBO_UPDATE_TIPO_ARQUIVO_LICON.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220901122829
      author: Regina Letícia
      changes:
        sqlFile:
          path: scripts/20220901122829_DBO_ADD_TRES_CASAS_DECIMAIS_TERMO_REFERENCIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220901122958
      author: Regina Letícia
      changes:
        sqlFile:
          path: scripts/20220901122958_AUDITORIA_ADD_TRES_CASAS_DECIMAIS_TERMO_REFERENCIA_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220901123015
      author: Regina Letícia
      changes:
        sqlFile:
          path: scripts/20220901123015_DBO_AUMENTO_PRECISAO_TRES_CASAS_DECIMAIS_ITEM_LOTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220901123110
      author: Regina Letícia
      changes:
        sqlFile:
          path: scripts/20220901123110_AUDITORIA_AUMENTO_PRECISAO_TRES_CASAS_DECIMAIS_ITEM_LOTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220903194458
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220903194458_DBO_ALTER_COLUMN_TIPO_ARQUIVO_INATIVO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220901233928
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220901233928_DBO_CREATE_ITEM.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220901233929
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220901233929_DBO_INSERT_ITEM.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220901233930
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220901233930_AUDITORIA_CREATE_ITEM_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220901233931
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220901233931_AUDITORIA_INSERT_ITEM_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220901233932
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220901233932_AUDITORIA_CREATE_CONTRATO_LICITANTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220901233933
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220901233933_AUDITORIA_CREATE_ITEM_CONTRATO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220901233934
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220901233934_DBO_CREATE_CONTRATO_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220901233935
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220901233935_DBO_CREATE_ITEM_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220901233936
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220901233936_DBO_INSERT_CONTRATO_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220901233937
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220901233937_AUDITORIA_ADD_COLUMN_CONTRATO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220904141418
      author: Regina Letícia
      changes:
        sqlFile:
          path: scripts/20220904141418_DBO_AUMENTO_PRECISAO_TRES_CASAS_DECIMAIS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220904141559
      author: Regina Letícia
      changes:
        sqlFile:
          path: scripts/20220904141559_AUDITORIA_AUMENTO_PRECISAO_TRES_CASAS_DECIMAIS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220906115645
      author: João Paulo Dantas
      changes:
        sqlFile:
          path: scripts/20220906115645_ALTER_SECAO_ORDEM_NOT_NULL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220903091752
      author: Lucian Julio
      changes:
        sqlFile:
          path: scripts/20220903091752_DBO_CREATE_RESPONSAVEL_ENTIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220826170235
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          path: scripts/20220826170235_CREATE_TABLE_OBRIGATORIEDADE_ARQUIVO_LICITACAO_FASE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220905183552
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220905183552_AUDITORIA_ALTER_DISPENSA_LICITANTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220905183553
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220905183553_DBO_ALTER_DISPENSA_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220905183554
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220905183554_AUDITORIA_ALTER_ITEM_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220905183555
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220905183555_AUDITORIA_ALTER_VENCEDOR_LICITACAO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220905183556
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220905183556_AUDITORIA_ALTER_INEXIGIBILIDADE_LICITANTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220905183557
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220905183557_DBO_ALTER_INEXIGIBILIDADE_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220905183558
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220905183558_DBO_ALTER_ITEM.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220905183559
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220905183559_DBO_ALTER_PUBLICACACOES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220905183560
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220905183560_DBO_DROP_ITEM_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220909120246
      author: Lucian Julio
      changes:
        sqlFile:
          path: scripts/20220909120246_AUDITORIA_ALTER_SECAO_TIPO_PROCESSO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220912163033
      author: Lucian Julio
      changes:
        sqlFile:
          path: scripts/20220912163033_DBO_CREATE_RESPONSAVEL_ENTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220913195159
      author: João Paulo Dantas
      changes:
        sqlFile:
          path: scripts/20220913195159_ALTER_LENGTH_DESCRICAO_ALTERACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220920211804
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220920211804_DBO_ALTER_TABLE_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220921173706
      author: Lucian Julio
      changes:
        sqlFile:
          path: scripts/20220921173706_DBO_CREATE_FUNCTION_GET_FASES_PROCESSOS_LICITATORIOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220921173756
      author: Lucian Julio
      changes:
        sqlFile:
          path: scripts/20220921173756_DBO_CREATE_INDEXS_LICITACAO_CARONA_INEXIGIBILIDADE_DISPENSA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220921173834
      author: Lucian Julio
      changes:
        sqlFile:
          path: scripts/20220921173834_DBO_CREATE_ANALISE_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220920221600
      author: Eniedson Junior
      changes:
        sqlFile:
          path: scripts/20220920221600_CREATE_TABLE_ANALISE_AUTOMATICA_TIPO_PROCESSO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220921113440
      author: Eniedson Junior
      changes:
        sqlFile:
          path: scripts/20220921113440_CREATE_TABLE_ANALISE_AUTOMATICA_CATEGORIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220921234455
      author: Eniedson Junior
      changes:
        sqlFile:
          path: scripts/20220921234455_CREATE_TABLE_ANALISE_AUTOMATICA_ENTIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220922204851
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220922204851_DBO_ALTER_TABLE_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220922204852
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220922204852_AUDITORIA_ALTER_TABLE_INEXIGIBILIDADE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220920104544
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          path: scripts/20220920104544_ADD_COLUNA_SEI_DISPENSA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220926221710
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220926221710_DBO_ADD_COLUMN_DISPENSA_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220926221711
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220926221711_DBO_ALTER_DISPENSA_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220926221712
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20220926221712_AUDITORIA_ALTER_DISPENSA_LICITANTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220929103443
      author: Pedro Wanderley
      changes:
        sqlFile:
          path: scripts/20220929103443_DBO_ALTER_CARONA_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220930152107
      author: João Paulo Dantas
      changes:
        sqlFile:
          path: scripts/20220930152107_ALTER_TABLE_CONTRATO_GARANTIA_CONTRACTUAL_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220930152605
      author: João Paulo Dantas
      changes:
        sqlFile:
          path: scripts/20220930152605_ALTER_TABLE_EDIFICACAO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220930153449
      author: João Paulo Dantas
      changes:
        sqlFile:
          path: scripts/20220930153449_ALTER_TABLE_MATERIAL_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220930154810
      author: João Paulo Dantas
      changes:
        sqlFile:
          path: scripts/20220930154810_ALTER_TABLE_OBRA_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220929103525
      author: Pedro Wanderley
      changes:
        sqlFile:
          path: scripts/20220929103525_AUDITORIA_ALTER_CARONA_LICITANTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221007072234
      author: Eniedson Junior
      changes:
        sqlFile:
          path: scripts/20221007072234_ADD_PERMISSAO_TDA_GRUPO_AUDITOR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220921235020
      author: Brunna Amorim
      changes:
        sqlFile:
          path: scripts/20220921235020_DBO_ALTER_TABLE_ADITIVO_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221003193704
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20221003193704_DBO_ALTER_TABLE_DISPENSA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220929200450
      author: Regina Letícia
      changes:
        sqlFile:
          path: scripts/20220929200450_AUDITORIA_ADD_COLUMN_MODALIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220929200450
      author: Thiago Moura
      changes:
        sqlFile:
          path: scripts/20220929200450_DBO_NORMALIZE_INDEXS_LICITACAO_TERMO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220929200451
      author: Regina Letícia
      changes:
        sqlFile:
          path: scripts/20220929200451_DBO_ADD_COLUMN_MODALIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20220929200452
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20220929200452_DBO_INSERT_MODALIDADES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221004155405
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          path: scripts/20221004155405_TABLE_LICITACAO_TIPO_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221003211237
      author: Pedro Wanderley
      changes:
        sqlFile:
          path: scripts/20221003211237_DBO_UPDATE_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221003211800
      author: Daniel Leite
      changes:
        sqlFile:
          path: scripts/20221003211800_PERMISSOES_AUDITOR_CRIACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221003212400
      author: Daniel Leite
      changes:
        sqlFile:
          path: scripts/20221003212400_PERMISSOES_AUDITOR_MENU_CADASTROS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221003212600
      author: Daniel Leite
      changes:
        sqlFile:
          path: scripts/20221003212600_PERMISSOES_AUDITOR_MENU_TERMO_REFERENCIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221003212700
      author: Daniel Leite
      changes:
        sqlFile:
          path: scripts/20221003212700_PERMISSOES_AUDITOR_MENU_AVISOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221003212800
      author: Daniel Leite
      changes:
        sqlFile:
          path: scripts/20221003212800_PERMISSOES_AUDITOR_MENU_APOIO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221003212900
      author: Daniel Leite
      changes:
        sqlFile:
          path: scripts/20221003212900_PERMISSOES_AUDITOR_MENU_AUDITORIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221003213000
      author: Daniel Leite
      changes:
        sqlFile:
          path: scripts/20221003213000_PERMISSOES_AUDITOR_MENU_PROCESSOS_AUDITORIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221003213200
      author: Daniel Leite
      changes:
        sqlFile:
          path: scripts/20221003213200_PERMISSOES_AUDITOR_MENU_RELATORIO_ADMINISTRACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221003213300
      author: Daniel Leite
      changes:
        sqlFile:
          path: scripts/20221003213300_PERMISSOES_AUDITOR_MENU_SEGURANCA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221003213400
      author: Daniel Leite
      changes:
        sqlFile:
          path: scripts/20221003213400_PERMISSOES_AUDITOR_MENU_CATALOGO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221004214402
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20221004214402_DBO_ALTER_TABLE_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221005223120
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20221005223120_DBO_DROP_CREATE_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221005223121
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20221005223121_DBO_ALTER_TABLE_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221005223122
      author: Suelen Felix
      changes:
        sqlFile:
          path: scripts/20221005223122_DBO_ALTER_TABLE_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221006162854
      author: João Paulo Dantas
      changes:
        sqlFile:
          path: scripts/20221006162854_UPDATE_INEXIGIBILIDADE_LICITANTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221006190447
      author: Thiago Moura
      changes:
        sqlFile:
          path: scripts/20221006190447_AUDITORIA_ALTER_TABLE_CARONA_LICITANTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221006202751
      author: Lucian Julio
      changes:
        sqlFile:
          path: scripts/20221006202751_DBO_DROP_CREATE_ANALISE_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221010074748
      author: Lucian Julio
      changes:
        sqlFile:
          path: scripts/20221010074748_DBO_ALTER_ITEM_CHECKLIST.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221010122856
      author: Eniedson Junior
      changes:
        sqlFile:
          path: scripts/20221010122856_CREATE_FUNCTION_GET_CATEGORIA_NATUREZA_OBJETO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221010123724
      author: Eniedson Junior
      changes:
        sqlFile:
          path: scripts/20221010123724_CREATE_FUNCTION_VERIFICA_SIMILARES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221010123808
      author: Eniedson Junior
      changes:
        sqlFile:
          path: scripts/20221010123808_CREATE_TRIGGER_APLICACAO_ANALISE_AUTOMATICA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221011223329
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20221011223328_DBO_ALTER_TABLE_INEXIGIBILIDADE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221011223330
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20221011223330_DBO_ALTER_TABLE_INEXIGIBILIDADE_LICITANTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221011223331
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20221011223331_DBO_ALTER_TABLE_INEXIGIBILIDADE_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221011223333
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          path: scripts/20221011223331_DBO_ALTER_TABLE_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221013191736
      author: Eniedson Junior
      changes:
        sqlFile:
          path: scripts/20221013191736_ADD_PERMISSAO_LISTAR_TDA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221010210227
      author: Daniel Leite
      changes:
        sqlFile:
          path: scripts/20221010210227_INSERT_OBRIGATORIEDADE_ARQUIVO_ADITIVO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221014173131
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221014173131_DBO_ADD_COLUMN_LEI_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221014173152
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221014173152_AUDITORIA_ADD_COLUMN_LEI_LICITACAO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221019094530
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221019094530_DBO_ALTER_ANALISE_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221019202441
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221019202441_DBO_ALTER_LOTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221019211534
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221019211534_PERMISSOES_AUDITOR_CHECKLIST_PROCESSOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221023092600
      author: Daniel Leite
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221023092600_PERMISSOES_REQUISICAO_MODIFICACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 202210230102900
      author: Daniel Leite
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/202210230102900_ADICIONANDO_ATRIBUTO_MOD_AUDITORIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221025172622
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221025172622_DBO_CREATE_MODIFICACAO_LICITANTE_TABLE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221025235408
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221025235408_DBO.CREATE_LICITACAO_ORGAO_PARTICIPANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221025235409
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221025235409_AUDITORIA.CREATE_LICITACAO_ORGAO_PARTICIPANTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221025174001
      author: Victor Brandao
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221025174001_DBO_CREATE_VIEW_USUARIO_AUDITOR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221026200123
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221026200123_DBO_CREATE_TRIGGER_CHECKLIST_CHECK_DUPLICATES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221027205355
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221027205355_DBO_ALTER_VIEW_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221027205356
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221027205356_DBO_ALTER_INEXIGIBILIDADE_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221027205357
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221027205357_AUDITORIA_ALTER_DISPENSA_LICITANTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221027205358
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221027205358_AUDITORIA_ALTER_INEXIGIBILIDADE_LICITANTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 202210230102900
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221028190730_SET_LICITACAO_ID_MODALIDADE_NOVA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221101211144
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221101211144_DBO_UPDATE_SCHEMA_REQ_MODIFICACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221102001232
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221102001232_DBO_ALTER_COLUMNS_MODIFICACAO_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221103001518
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221103001518_DBO_ALTER_INEXIGIBILIDADE_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221103074517
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221103074517_DBO_ALTER_TABLES_REQ_MODIFICACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221031095329
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221031095329_CREATE_TABLE_PEDIDO_EDICAO_ITEM_CATALOGO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221031095330
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221031095330_CREATE_REVISAO_PEDIDO_ITEM_CATALOGO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221031095331
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221031095331_ADD_PERMISSAO_PEDIDO_EDICAO_ITEM_CATALOGO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221103074517
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221103133925_DBO_ALTER_TABLE_MODIFICACAO_ARQUIVO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221103074518
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221103074518_DBO_CREATE_VIEW_VW_VENCEDOR_LICITACAO_DISTINTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221103074519
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221103074519_DBO_ALTER_TABLE_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221108144718
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221108144718_DBO_ALTER_TABLE_CARONA_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221108144719
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221108144719_DBO_ALTER_TABLE_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221108144720
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221108144720_DBO_CREATE_VIEW_VW_ENTIDADE_INTERNA_EXTERNA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221108144721
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221108144721_DBO_CREATE_VIEW_VW_ULTIMA_ALTERACAO_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221110220229
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221110220229_DBO_CREATE_VIEW_VW_ULTIMA_ALTERACAO_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221110220230
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221110220230_DBO_CREATE_VIEW_VW_ULTIMA_ALTERACAO_DISPENSA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221110220231
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221110220231_DBO_CREATE_VIEW_VW_ULTIMA_ALTERACAO_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221110220232
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221110220232_DBO_CREATE_VIEW_VW_ULTIMA_ALTERACAO_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221109143348
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221109143348_DBO_ALTER_TABLE_MODIFICACAO_PUBLICACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221109162957
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221109162957_DBO_ALTER_TABLE_MODIFICACAO_VENCEDOR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221114215715
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221114215715_AUDITORIA_ALTER_TABLE_ADITIVO_CONTRATO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221114215716
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221114215716_DBO_ALTER_TABLE_ADITIVO_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221114215717
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221114215717_DBO_CREATE_VIEW_VW_ULTIMA_ALTERACAO_ADITIVO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221118112259
      author: Andreza Santana
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221118112259_DBO_MUNICIPIO_INSERT_DADOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221114151100
      author: Daniel Leite
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221117205900_DBO_ALTER_TABLE_ITEM_CHECKLIST.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221117205900
      author: Daniel Leite
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221117210800_CREATE_TRIGGER_ITEM_CHECKLIST_ORDEM.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221116142122
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221116142122_DBO_AUMENTO_PRECISAO_VALOR_GLOBAL_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221121130710
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221121130710_DBO_ALTER_TABLE_LICITACAO_DATE_COLUMNS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221122202444
      author: Eniedson Junior
      changes:
        - sqlFile:
            encoding: UTF-8
            path: scripts/20221122202444_DBO_ADD_STATUS_ADITIVO_CONTRATO.sql
            relativeToChangelogFile: true

  - changeSet:
      id: 20221124132620
      author: Eniedson Junior
      changes:
        - sqlFile:
            encoding: UTF-8
            path: scripts/20221124132620_CATALOGO_SET_ANOTADOR_MATERIAL_DETALHAMENTO.sql
            relativeToChangelogFile: true

  - changeSet:
      id: 20221124094759
      author: Lucian Julio
      changes:
        - sqlFile:
            encoding: UTF-8
            path: scripts/20221124094759_DBO_ALTER_VIEWS_LICITACAO.sql
            relativeToChangelogFile: true

  - changeSet:
      id: 20221125144940
      author: Júlia Oliveira
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221125144940_ADD_COLUMN_ORDEM_MODALIDADE_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221121214033
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221121214033_DBO_CREATE_TABLE_ADITIVO_MOTIVO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221121214034
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221121214034_DBO_INSERT_INTO_ADITIVO_MOTIVO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221121214035
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221121214035_DBO_CREATE_TABLE_ARQUIVO_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221121214036
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221121214036_DBO_INSERT_INTO_ARQUIVO_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221121214037
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221121214037_DBO_CREATE_TABLE_ARQUIVO_ADITIVO_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221121214038
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221121214038_DBO_INSERT_INTO_ARQUIVO_ADITIVO_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221121214039
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221121214039_DBO_COPY_EMPENHO_ATA_TO_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221121214040
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221121214040_DBO_CREATE_TABLE_EMPENHO_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221121214041
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221121214041_DBO_CREATE_TABLE_EMPENHO_CONTRATO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221121214042
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221121214042_DBO_INSERT_INTO_EMPENHO_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221121214043
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221121214043_AUDITORIA_CREATE_AND_COPY_AUDITORIA_ADITIVO_MOTIVO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221126215210
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221126215210_AUDITORIA_ALTER_TABLE_CARONA_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221126215211
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221126215211_DBO_ALTER_TABLE_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221129002939
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221129002939_DBO_MODIFICACAO_VENCEDOR_ADD_COLUMN_VALOR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221128210641
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221128210641_DBO_ALTER_TABLE_ARQ_ADITIVO_CONTRATO_AND_ARQ_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221129100743
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221129100743_UPDATE_DATA_CADASTRO_RESCISAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221130120840
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221130120840_DBO_ALTER_VIEW_ANALISE_PROCESSO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221130144324
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221130144324_DBO_CREATE_PROCEDURE_APPLY_PARAMETRO_ANALISE_AUTOMATICA_BY_ID.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221202112805
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221202112805_DBO_CREATE_PROCEDURE_APPLY_ANALISE_AUTOMATICA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221130144555
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221202112932_DBO_TRANSFORMA_PARAMETROS_ANALISE_AUTOMATICA_ANTIGOS.sql
          relativeToChangelogFile: true

  #  - changeSet:
  #      id: 20221205074123
  #      context: "!dev"
  #      author: Eniedson Junior
  #      changes:
  #        sqlFile:
  #          encoding: UTF-8
  #          path: scripts/20221205074123_DBO_CREATE_JOB_ANALISE_AUTOMATICA.sql
  #          relativeToChangelogFile: true

  - changeSet:
      id: 20221205094901
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221205094901_DBO_ALTER_TRIGGER_APLICACAO_ANALISE_AUTOMATICA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221201230839
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221201230839_DBO_CREATE_FUNCTION_REMOVE_ACENTUACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221201165146
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221201165146_DBO_ADD_COLUMNS_CONTRATO_PROCESSO_ENTIDADE_EXTERNA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221206102031
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221206102031_DBO_INSERT_NOVOS_TIPOS_ARQUIVOS_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221213103958
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221213103958_DBO_UPDATE_DATA_RESCISAO_CONTRATUAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221219135505
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221219135505_DBO_ALTER_ALERTA_ANALISE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221221120512
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221221120512_DBO_ADD_PERMISSIONS_ALERTA_ANALISE_AND_PESSOA_COMISSAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20221215080119
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20221215080119_DBO_CREATE_VIEW_HISTORICO_PROCESSOS_AUDITORIA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230116095211
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230116095211_PERMISSOES_AUDITOR_HISTORICO_ALTERACOES_PROCESSOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230202152934
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230202152934_DBO_CREATE_TABLES_NOTIFICACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230131144944
      author: Victor Brandao
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230131144944_ALTER_TABLE_ITEM_CHECKLIST_ORDEM.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230131145154
      author: Victor Brandao
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230131145154_ALTER_TRIGGER_ITEM_CHECKLIST_ORDEM.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230206105251
      author: Victor Brandao
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230206105251_ALTER_TABLE_LICITACAO_ADD_COLUNA_VALOR_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230206105401
      author: Victor Brandao
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230206105401_ALTER_ANALISE_PROCESSO_VIEW_ADD_VALOR_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230207233248
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230207233248_DBO_CREATE_TABLE_ARQUIVO_TERMO_REFERENCIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230210225927
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230210225927_DBO_ALTER_TABLE_TERMO_REFERENCIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230216225928
      author: Suelen Felix
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230216225928_AUDITORIA_ALTER_TABLE_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230216203207
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230216203207_ADD_COLUMN_SERVICO_MATERIAL_DETALHAMENTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230217130636
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230217130636_ALTER_TABLE_PEDIDO_ITEM_CATALOGO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230224121200
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230224121200_ALTER_TABLE_MATERIAL_DETALHAMENTO_SET_UNIDADE_MEDIDA_NULL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230217104555
      author: Felipe Oliveira
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230217104555_DELETE_DUPLICATED_PERMISSIONS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230216093536
      author: Lucian Julio
      context: "homolog or dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230216093536_DBO_INSERT_USUARIOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230214195618
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230214195618_DBO_CREATE_TABLES_MATRIZ_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230214195625
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230214195625_DBO_CREATE_TABLES_AUDITORIA_FUNCAO_RISCO_AND_CONDICAO_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230214195634
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230214195634_DBO_CREATE_PROCEDURE_APPLY_ANALISE_MATRIZ_DE_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230214195655
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230214195655_DBO_CREATE_TABLE_LIMIAR_VALOR_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230214195715
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230214195715_DBO_CREATE_TABLE_AUDITORIA_LIMIAR_VALOR_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230215083412
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230215083412_DBO_INSERT_PERMISSIONS_FUNCAO_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230215100240
      author: Victor Brandao
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230215100240_DBO_PERMISSOES_LIMIAR_VALOR_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230216150128
      author: Victor Brandao
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230216150128_DBO_INSERT_FUNCOES_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230223105704
      author: Victor Brandao
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230223105704_ALTER_TRIGGER_ITEM_CHECKLIST_ORDEM_UPDATE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230224085346
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230224085346_DBO_S_PERMISSAO_INSERT_PERMISSION_LISTAR_AUDITOR_ATRIBUIDOR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230227090738
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230227090738_DBO_ADD_PERMISSION_ANALISAR_ALERTA_DIRETOR_DAFO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230228174737
      author: Victor Brandao
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230228174737_ALTER_TABLE_REQUISICAO_MODIFICACAO_ADD_COLUNA_FASE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230228174821
      author: Victor Brandao
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230228174821_ALTER_PROCEDURE_ANALISE_MATRIZ_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230302212009
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230302212009_ADD_COLUMN_ID_REQUISICAO_MODIFICACAO_TERMO_REFERENCIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230307134450
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230307134450_DBO_CREATE_TABLES_MODIFICACAO_TERMO_REFERENCIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230307151022
      author: Felipe Oliveira
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230307151022_DELETE_PERMISSAO_CADASTRAR_CONSULTA_MATERIAIS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230308153355
      author: Felipe Oliveira
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230308153355_DELETE_PERMISSAO_CADASTRAR_CONSULTA_PROCESSOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230227115816
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230227115816_DBO_CREATE_TABLE_ALERTA_MENSAGEM.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230301105036
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230301105036_DBO_ALTER_TABLE_ARQUIVO_ALERTA_ANALISE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230303135909
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230303135909_DBO_CREATE_VIEW_ALERTA_ANALISE_ENTIDADE_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230307203840
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230307203840_DBO_CREATE_FUNCTION_GET_ALERTA_PASSOU_DAFO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230307210858
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230307210858_DBO_GET_ALERTA_PROCESSO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230307211009
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230307211009_ALTER_VIEW_ANALISE_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230308175053
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230308175053_DBO_ALTER_FUNCTION_GET_FASES_PROCESSOS_LICITATORIOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230309214315
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230309214315_DBO_CREATE_VIEW_USUARIO_JURISDICIONADO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230311151455
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230311151455_DBO_INSERT_OLD_VALUES_ALERTA_MENSAGEM.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230314100031
      author: Lucian Julio
      context: "homolog or dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230314100031_DBO_INSERT_USUARIO_DAFO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230314180436
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230314180436_DBO_ALTER_TABLE_MODIFICACAO_ITEM_LOTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230312090004
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230312090004_DBO_ALTER_TABLE_TERMO-REFERENCIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230321133733
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230321133733_UPDATE_COLUMN_MODELO_TERMO_REFERENCIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230324200936
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230324200936_CORRECAO_TABELAS_AUDITORIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230322000552
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230322000552_DBO_ALTER_TABLE_ITEM.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230322000553
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230322000553_AUDITORIA_ALTER_TABLE_ITEM_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230322000554
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230322000554_DBO_ALTER_TABLE_VENCEDOR_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230322000555
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230322000555_AUDITORIA_ALTER_TABLE_VENCEDOR_LICITACAO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230322000556
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230322000556_DBO_ALTER_TABLE_CARONA_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230322000557
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230322000557_AUDITORIA_ALTER_TABLE_CARONA_LICITANTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230322000558
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230322000558_DBO_ALTER_TABLE_DISPENSA_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230322000559
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230322000559_AUDITORIA_ALTER_TABLE_DISPENSA_LICITANTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230322000560
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230322000560_DBO_ALTER_TABLE_INEXIGIBILIDADE_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230322000561
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230322000561_AUDITORIA_ALTER_TABLE_INEXIGIBILIDADE_LICITANTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230329155927
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230329155927_ADD_COLUMNS_VALORES_ORIGINAIS_REQ_MODIFICACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230329103226
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230329103226_DBO_UPDATE_OBRIGATORIEDADE_ARQUIVO_LICITACAO_FASE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230329175435
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230329175435_DBO_ALTER_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230330232007
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230330232007_CORRECAO_FASES_TABELA_REQUISICAO_MODIFICACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230412163940
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230412163940_DBO_CREATE_TABLE_ARQUIVO_REQUISICAO_MODIFICACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230404111024
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230404111024_ADD_COLUMNS_MODIFICACA_VENCEDORES_ITENS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230403205416
      author: Mateus Cunha
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230403205416_ROBOS_DDL_INICIAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230403205550
      author: Mateus Cunha
      context: "homolog or dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230403205550_INIT_DADOS_ROBOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230411171422
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230411171422_CREATE_VIEW_EDITAIS_LICITACAO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231204212516
      author: Mateus Cunha
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231204212516_ROBOS_AJUSTE_DDL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230417225155
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230417225155_ADD_NOVOS_ATRIBUTOS_FUNDAMENTACAO_LEGAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230412211300
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230412211300_ROBOS_CREATE_VIEW_MONITORAMENTO_ATOS_DIARIO_OFICIAL_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230417124225
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230417124225_DBO_ADD_PERMISSAO_MONITORAMENTO_ATOS_DIARIO_OFICIAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230413100847
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230413100847_ROBOS_CREATE_TABLE_ARQUIVO_EDITAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230413102345
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230413102345_ALTER_TABLE_ROBOS_SENTENCA_EDITAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230413103022
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230413103022_ALTER_TABLE_ROBOS_EDITAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230413110023
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230413110023_DBO_ADD_PERMISSAO_EDITAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230423214449
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230423214449_AJUSTE_CONTRATOS_RESCINDIDOS_NAO_PERMITEM_ADITIVOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230424110751
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230424110751_ALTER_VIEW_EDITAIS_LICITACAO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230425112038
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230425112038_ALTER_VIEW_EDITAIS_LICITACAO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230424115432
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230419220032_DBO_ALTER_TABLE_ITEM_CONTRATO_REMOVE_DEFAULT_CONSTRAINT.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230419220032
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230424115432_ALTER_COLUMN_QUANTIDADE_TYPE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230426203327
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230426203327_DBO_DELETE_PERMISSAO_CONFIGURACAO_SERVIDOR_EMAIL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230426205810
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230426205810_DBO_DROP_CONFIGURACAO_SERVIDOR_EMAIL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230426205811
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230426205811_AUDITORIA_DROP_CONFIGURACAO_SERVIDOR_EMAIL_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230503235622
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230503235622_INSERT_VALORES_INEXIGIBILIDADE_FUNDAMENTACAO_LEGAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230504180739
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230504180739_INSERT_VALORES_TIPO_DISPENSA_FUNDAMENTACAO_LEGAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230509195301
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230509195301_DBO_ALTER_TABLE_CONTRATO_ADD_CONTRAINT_ENT_EXT.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230510171521
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230510171521_DBO_UPDATE_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230524113932
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230524113932_DBO_ADD_FK_ENTIDADE_EXTERNA_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230509172040
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230509172040_DBO_ALTER_TABLE_ADD_NUM_EMPENHO_STATUS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230509172103
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230509172103_DBO_CREATE_TABLE_OBRA_MEDICAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230516213301
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230516213301_DBO_CREATE_TABLE_ARQUIVO_OBRA_MEDICAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230522184649
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230522184649_DBO_ADICAO_PERMISSOES_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230525165507
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230525165507_VIEWS_E_INSERTS_MATRIZ_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230524200103
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230524200103_PROCEDURE_APPLY_FUNCAO_GENERICA_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230524200104
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230524200104_PROCEDURE_APPLY_ANALISA_PROCESSO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230524200105
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230524200105_ALTER_ANALISE_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230515211356
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230515211356_DBO_ALTER_VIEW_AND_PROCEDURE_ANALISE_PROCESSO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230524165220
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230524165220_DBO_UPDATE_TDAS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230525213855
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230525213855_DBO_ALTER_ITEM.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230525213856
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230525213856_AUDITORIA_ALTER_ITEM_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230531194907
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230531194907_DBO_DELETE_PERMISSIONS_LIMIAR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230531194921
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230531194921_DBO_ADD_PERMISSIONS_CONFIGURACOES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230531194933
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230531194933_DBO_UPDATE_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230531194941
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230531194941_DBO_UPDATE_DISPENSA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230531194948
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230531194948_DBO_UPDATE_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230531194956
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230531194956_DBO_UPDATE_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230531195559
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230531195725_DBO_CREATE_TABLE_LIMIAR_DIAS_ARQUIVAMENTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230531195605
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230531195805_DBO_ALTER_VIEW_AND_FUNCTION_ANALISE_PROCESSOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230602233154
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230602233154_DBO_ALTER_ITEM_LOTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230602233155
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230602233155_AUDITORIA_ALTER_ITEM_LOTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230605085553
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230605085553_DBO_ALTER_FUNCTION_FASES_PROCESSOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230529113501
      author: Eniedson Júnior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230529113501_SETUP_SCHEMA_NOVO_CATALOGO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230608134201
      author: Eniedson Júnior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230608134201_CATALOGO_RESET_PERMISSOES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230607195025
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230607195025_ASSOCIA_PERMISSOES_JURISDICIONADO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230609135128
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230609135128_ASSOCIA_GRUPOS_USUARIOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612113430
      author: Mateus Cunha
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612113430_ROBOS_DOE_AJUSTE_METADADOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612085359
      author: Regina Letícia
      context: "dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612085359_INSERT_SECOES_TERMO_REFERENCIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612221800
      author: Eniedson Junior
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612221800_CATALOGO_INSERT_CARACTERISTICAS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612221801
      author: Eniedson Junior
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612221801_CATALOGO_INSERT_CARACTERISTICAS_VALORES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612221802
      author: Eniedson Junior
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612221802_CATALOGO_INSERT_SECOES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612221803
      author: Eniedson Junior
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612221803_CATALOGO_INSERT_UNIDADES_MEDIDA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612221804
      author: Eniedson Junior
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612221804_CATALOGO_INSERT_DIVISOES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612221805
      author: Eniedson Junior
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612221805_CATALOGO_INSERT_GRUPOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612221806
      author: Eniedson Junior
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612221806_CATALOGO_INSERT_CLASSES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612221807
      author: Eniedson Junior
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612221807_CATALOGO_INSERT_SUBCLASSES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612221808
      author: Eniedson Junior
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612221808_CATALOGO_INSERT_PDM.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612221809
      author: Eniedson Junior
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612221809_CATALOGO_INSERT_PDM_CARACTERISTICA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612221810
      author: Eniedson Junior
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612221810_CATALOGO_INSERT_PDM_UNIDADE_MEDIDA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612221811
      author: Eniedson Junior
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612221811_CATALOGO_INSERT_MATERIAIS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612221812
      author: Eniedson Junior
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612221812_CATALOGO_INSERT_MATERIAL_CARACTERISTICA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 0230607233245
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230607233245_DBO_ALTER_ITEM_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230607233246
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230607233246_AUDITORIA_ALTER_ITEM_CONTRATO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230607233247
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230607233247_DBO_ALTER_CONTRATO_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230607233248
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230607233248_AUDITORIA_ALTER_CONTRATO_LICITANTE_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230607090004
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230607090004_DBO_CREATE_TABLE_RISCO_ENTIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230609135057
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230609135057_DBO_ADD_PERMISSAO_RISCO_ENTIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612105112
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612105112_PROCEDURE_APPLY_ANALISA_PROCESSO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230614095930
      author: Mateus Cunha
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230614095930_ROBOS_AJUSTE_TAM_COLUNAS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230531201212
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230531201212_DBO_ALTER_TABLE_OBRA_MEDICAO_ADD_NUM_EMPENHO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230601084846
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230601084846_DBO_ALTER_TABLE_ARQUIVO_OBRA_MEDICAO_ADD_COLUMN.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612194808
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612194808_DBO_AUDITORIA_CREATE_TABLE_LIMIAR_DIAS_OBRA_MEDICAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230612210430
      author: Pedro Wanderley
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230612210430_DBO_CREATE_PROCEDURE_VERIFICA_OCORRENCIA_OBRA_PARALISADA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230613145500
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230613145500_ADICIONA_WRITE_PERMISSION_AUDITOR_ATRIBUIDOR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230613151057
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230613151057_ASSOCIA_PERMISSOES_JURISDICIONADO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230613153505
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230613153505_ASSOCIA_PERMISSOES_AUDITOR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230614112455
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230614112455_ASSOCIA_PERMISSAO_JURISDICIONADO_CONSULTA_MATERIAIS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230616090303
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230616090303_DBO_CREATE_TABLE_EMAIL_LOG.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230616090323
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230616090323_DBO_CREATE_TABLE_DESTINATARIO_EMAIL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230619090120
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230619090120_DBO_REMOVE_PERMISSOES_NAO_UTILIZADAS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230620090120
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.0.0

  - changeSet:
      id: 20230718100630
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230718100630_DBO_ALTER_TABLE_CLASSIFICACAO_ADMINISTRATIVA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230718100630_ROLLBACK_DBO_ALTER_TABLE_CLASSIFICACAO_ADMINISTRATIVA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230718100648
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230718100648_DBO_ALTER_TABLE_ENTE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230718100648_ROLLBACK_DBO_ALTER_TABLE_ENTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230718100924
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230718100924_DBO_ALTER_TABLE_PODER.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230718100924_ROLLBACK_DBO_ALTER_TABLE_PODER.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230718102903
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230718102903_DBO_DELETE_VINCULOS_DUPLICADOS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230718102903_ROLLBACK_DBO_DELETE_VINCULOS_DUPLICADOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230718103114
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230718103114_DBO_ALTER_VINCULO_ADD_CONSTRAINT_UNIQUE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230718103114_ROLLBACK_DBO_ALTER_VINCULO_ADD_CONSTRAINT_UNIQUE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230719091601
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230719091601_DBO_ALTER_TABLE_TIPO_DE_RESPONSAVEL.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230719091601_ROLLBACK_DBO_ALTER_TABLE_TIPO_DE_RESPONSAVEL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230719180348
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230719180348_ALTER_DESCRICAO_AUDITORIA_FUNCAO_RISCO_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230719180348_ROLLBACK_ALTER_DESCRICAO_AUDITORIA_FUNCAO_RISCO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230719180606
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230719180606_ALTER_DESCRICAO_DBO_FUNCAO_RISCO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230719180606_ROLLBACK_ALTER_DESCRICAO_DBO_FUNCAO_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230726072227
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.0.1

  - changeSet:
      id: 20230705082900
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230705082900_ROBOS_CREATE_TABLE_MATCH_DOE_LICON.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230705082900_ROLLBACK_ROBOS_CREATE_TABLE_MATCH_DOE_LICON.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230705083354
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230705083354_ROBOS_CREATE_MAPEAMENTO_ATOS_LICITACOES_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230705083354_ROLLBACK_ROBOS_CREATE_MAPEAMENTO_ATOS_LICITACOES_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230705083521
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230705083521_ADD_PERMISSAO_MAPEAMENTOS_ATOS_LICITACOES_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230705083521_ROLLBACK_ADD_PERMISSAO_MAPEAMENTOS_ATOS_LICITACOES_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230705083548
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230705083548_ADD_PERMISSAO_ATO_DIARIO_OFICIAL_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230705083548_ROLLBACK_ADD_PERMISSAO_ATO_DIARIO_OFICIAL_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230710131012
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.1.0

  - changeSet:
      id: 20230815165902
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.1.1

  - changeSet:
      id: 20230630173629
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230630173629_DBO_ADD_COLUMN_ID_REQUISICAO_MODIFICACAO_OBRA_MEDICAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230630173629_ROLLBACK_DBO_ADD_COLUMN_ID_REQUISICAO_MODIFICACAO_OBRA_MEDICAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230726135550
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230726135550_DBO_INSERT_PERMISSAO_EMPENHO_JURISDICIONADO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230726135550_ROLLBACK_DBO_INSERT_PERMISSAO_EMPENHO_JURISDICIONADO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230801182228
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.2.0

  - changeSet:
      id: 20230824205802
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.2.1

  - changeSet:
      id: 20230801105227
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230801105227_ALTER_VIEW_DBO_ANALISE_PROCESSO_LICON_VIEW_ADD_MODALIDADE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230801105227_ROLLBACK_DBO_ANALISE_PROCESSO_LICON_VIEW_ADD_MODALIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230816095955
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.3.0

  - changeSet:
      id: 20230815131147
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230815131147_DBO_INSERT_PERMISSAO_ALTERACAO_CONTRATUAL_JURISDICIONADO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230815131147_ROLLBACK_DBO_INSERT_PERMISSAO_ALTERACAO_CONTRATUAL_JURISDICIONADO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 2023080113552
      author: Suelen Felix
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/2023080113552_DBO_CREATE_FUNCTION_GET_NOME_MODALIDADE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/2023080113552_ROLLBACK_DBO_CREATE_FUNCTION_GET_NOME_MODALIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 2023080213551
      author: Suelen Felix
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/2023080213551_DBO_SELECT_TIPO_PROCESSO_IN_ALERTA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/2023080213551_ROLLBACK_SELECT_TIPO_PROCESO_IN_ALERTA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230722110646
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230722110646_DBO_ALTER_ALERTA_MENSAGEM_ADD_COLUMN_PASSOU_ATRIBUIDOR.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230722110646_ROLLBACK_DBO_ALTER_ALERTA_MENSAGEM_ADD_COLUMN_PASSOU_ATRIBUIDOR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230722111249
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230722111249_AUDITORIA_ALTER_ALERTA_MENSAGEM_ADD_COLUMN_PASSOU_ATRIBUIDOR.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230722111249_ROLLBACK_AUDITORIA_ALTER_ALERTA_MENSAGEM_ADD_COLUMN_PASSOU_ATRIBUIDOR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230731132201
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230731132201_DBO_ALTER_PROCEDURE_GET_FASES_PROCESSOS_LICITATORIOS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230731132201_ROLLBACK_DBO_ALTER_PROCEDURE_GET_FASES_PROCESSOS_LICITATORIOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230816192259
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230816192259_DBO_CREATE_TABLE_ARQUIVO_EMPENHO_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192259_ROLLBACK_DBO_CREATE_TABLE_ARQUIVO_EMPENHO_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230816192320
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230816192320_DBO_REINSERT_DATA_EMPENHO_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230816194336
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230816194336_DBO_RENAME_DEPRECATED_TABLES.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816194336_ROLLBACK_DBO_RENAME_DEPRECATED_TABLES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230825151557
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.4.0

  - changeSet:
      id: 20230904211149
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.4.1

  - changeSet:
      id: 20230719152918
      author: Regina Letícia
      context: "dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230719152918_INSERT_RESPONSAVEL_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230719153048_ROLLBACK_INSERT_RESPONSAVEL_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230725094857
      author: Regina Letícia
      context: "dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230725094857_INSERT_ENTIDADE_EXTERNA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230725094857_ROLLBACK_INSERT_ENTIDADE_EXTERNA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230807104851
      author: Regina Letícia
      context: "dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230807104851_INSERT_PROCESSO_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230807104851_ROLLBACK_INSERT_PROCESSO_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230807104852
      author: Regina Letícia
      context: "dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230807104852_INSERT_PROCESSO_CARONA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230807104852_ROLLBACK_INSERT_PROCESSO_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230807104853
      author: Regina Letícia
      context: "dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230807104853_INSERT_PROCESSO_DISPENSA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230807104853_ROLLBACK_INSERT_PROCESSO_DISPENSA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230807104854
      author: Regina Letícia
      context: "dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230807104854_INSERT_PROCESSO_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230807104854_ROLLBACK_INSERT_PROCESSO_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230811104854
      author: Regina Letícia
      context: "dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230811104854_INSERT_TERMO_PROCESSOS_LICITATORIOS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230811104854_ROLLBACK_INSERT_TERMO_PROCESSOS_LICITATORIOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230829172502
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230829172502_DBO_ALTER_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230829172502_ROLLBACK_DBO_ALTER_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230829182337
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230829182337_DBO_ALTER_ALERTA_ANALISE_ENTIDADE_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230829182337_ROLLBACK_DBO_ALTER_ALERTA_ANALISE_ENTIDADE_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230904162450
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230904162450_CREATE_INDEXS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230904162450_ROLLBACK_CREATE_INDEXS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230915131031
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.5.0

  - changeSet:
      id: 20230817133129
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230817133129_DBO_CREATE_TIPO_PROCESSO_AND_FUNCAO_RISCO_TIPO_PROCESSO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230817133129_ROLLBACK_DBO_CREATE_TIPO_PROCESSO_AND_FUNCAO_RISCO_TIPO_PROCESSO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230816122255
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230816122255_ALTER_NAME_ANALISE_MATRIZ_RISCO_VIEW_AND_TABLE_FUNCAO_RISCO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816122255_ROLLBACK_ALTER_NAME_ANALISE_MATRIZ_RISCO_VIEW_AND_TABLE_FUNCAO_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230816145158
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230816145158_DBO_ALTER_PROCEDURE_APPLY_ANALISA_PROCESSO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816145158_ROLLBACK_DBO_ALTER_PROCEDURE_APPLY_ANALISA_PROCESSO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230913132229
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230913132229_DBO_REINSERT_FUNCOES_RISCO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230913132229_ROLLBACK_DBO_REINSERT_FUNCOES_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230915163149
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230915163149_ALTER_VIEW_ANALISE_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230915190122
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230915190122_DBO_INSERT_PERMISSIONS_TIPO_PROCESSO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230915190122_ROLLBACK_DBO_INSERT_PERMISSIONS_TIPO_PROCESSO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230822165154
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230822165154_DBO_ALTER_TABLES_TDA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230822165154_ROLLBACK_DBO_ALTER_TABLES_TDA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230822165222
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230822165222_DBO_ALTER_TABLES_AUDITORIA_TDA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230822165222_ROLLBACK_DBO_ALTER_TABLES_AUDITORIA_TDA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20230927200944
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.5.1

  - changeSet:
      id: 20231004170236
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.5.2

  - changeSet:
      id: 20230922101828
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230922101828_ALTER_TABLE_ARQUIVO_TERMO_REFERENCIA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230922101828_ROLLBACK_ALTER_TABLE_ARQUIVO_TERMO_REFERENCIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231024162655
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.6.0

  - changeSet:
      id: 20231002091809
      author: Ana Beatriz
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231002091809_DBO_ALTER_TABLE_ITEM_CHECKLIST.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231002091809_ROLLBACK_DBO_ALTER_TABLE_ITEM_CHECKLIST.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231106084829
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231106084829_ALTER_FUNCTION_GET_CATEGORIA_NATUREZA_OBJETO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231106084829_ROLLBACK_ALTER_FUNCTION_GET_CATEGORIA_NATUREZA_OBJETO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231026150707
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231026150707_ALTER_TERMO_REFERENCIA_ADD_ITENS_LOTES.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231026150707_ROLLBACK_ALTER_TERMO_REFERENCIA_ADD_ITENS_LOTES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231114092222
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.7.0

  - changeSet:
      id: 20230602080831
      author: Ana Beatriz
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230602080831_CREATE_VIEW_FORNECEDORES_CONTRATADOS.sql
          relativeToChangelogFile: true
        rollback:
          sqlFile:
            encoding: UTF-8
            path: rollback_scripts/20230602080831_ROLLBACK_CREATE_VIEW_FORNECEDORES_CONTRATADOS.sql
            relativeToChangelogFile: true

  - changeSet:
      id: 20230704110813
      author: Ana Beatriz
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230704110813_CREATE_VIEW_ALERTAS_AUDITORIA_VIEW.sql
          relativeToChangelogFile: true
        rollback:
          sqlFile:
            encoding: UTF-8
            path: rollback_scripts/20230704110813_ROLLBACK_CREATE_VIEW_ALERTAS_AUDITORIA_VIEW.sql
            relativeToChangelogFile: true

  - changeSet:
      id: 20230912084534
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230912084534_CREATE_VIEW_ADITIVOS.sql
          relativeToChangelogFile: true
        rollback:
          sqlFile:
            encoding: UTF-8
            path: rollback_scripts/20230912084534_ROLLBACK_CREATE_VIEW_ADITIVOS.sql
            relativeToChangelogFile: true

  - changeSet:
      id: 20231023231314
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231023231314_ALTER_VIEW_FORNECEDORES_CONTRATADOS.sql
          relativeToChangelogFile: true
        rollback:
          sqlFile:
            encoding: UTF-8
            path: rollback_scripts/20231023231314_ROLLBACK_ALTER_VIEW_FORNECEDORES_CONTRATADOS.sql
            relativeToChangelogFile: true

  - changeSet:
      id: 20230928193656
      author: Alexandre Medeiros
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20230928193656_ALTER_ANALISE_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230928193656_ROLLBACK_ALTER_ANALISE_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231123182955
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.8.0

  - changeSet:
      id: 20231205211442
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.9.0

  - changeSet:
      id: 20231128221127
      author: Regina Leticia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231128221127_ADD_PERMISSAO_JURISDICIONADO_CADASTRAR_OCORRENCIA_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231128221127_ROLLBACK_ADD_PERMISSAO_JURISDICIONADO_CADASTRAR_OCORRENCIA_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231121113613
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231121113613_DBO_UPDATE_OBRIGATORIEDADE_ARQUIVO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231121113613_ROLLBACK_DBO_UPDATE_OBRIGATORIEDADE_ARQUIVO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231127102514
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231127102514_DBO_CREATE_TABLE_CONFIGURACAO_ARQUIVO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231127102514_ROLLBACK_DBO_CREATE_TABLE_CONFIGURACAO_ARQUIVO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231127102523
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231127102523_DBO_CREATE_TABLE_FILTRO_ARQUIVO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231127102523_ROLLBACK_DBO_CREATE_TABLE_FILTRO_ARQUIVO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231127112632
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231127112632_DBO_INSERTS_CONFIGURACAO_ARQUIVOS_LICITACAO_8666.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231204144557
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231204144557_DBO_INSERTS_CONFIGURACAO_ARQUIVOS_LICITACAO_14133.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231204211633
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231204211633_DBO_INSERTS_CONFIGURACAO_ARQUIVOS_LICITACAO_OUTRA_LEI.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231207141843
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231207141843_DBO_UPDATE_PROCESSOS_MIGRADOS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231207141843_ROLLBACK_DBO_UPDATE_PROCESSOS_MIGRADOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231214161437
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231214161437_DBO_UPDATE_OBRIGATORIEDADE_ARQUIVO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231214161437_ROLLBACK_DBO_UPDATE_OBRIGATORIEDADE_ARQUIVO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231214165655
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231214165655_DBO_ALTER_TABLE_TERMO_REFERENCIA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231214165655_ROLLBACK_DBO_ALTER_TABLE_TERMO_REFERENCIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231215001258
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.10.0

  - changeSet:
      id: 20231204144504
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231204144504_DELETE_PERMISSAO_ADM_AUDITOR.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231204144504_ROLLBACK_PERMISSAO_ADM_AUDITOR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231130200028
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231130200028_DBO_COMISSAO_ADD_COLUMN_TIPO_CONJUNTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231130200028_ROLLBACK_DBO_COMISSAO_ADD_COLUMN_TIPO_CONJUNTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231221110048
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231221110048_DBO_CREATE_TABLE_OCORRENCIA_LICITACAO_LICITANTE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231221110048_ROLLBACK_DBO_CREATE_TABLE_OCORRENCIA_LICITACAO_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231227150934
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231227150934_DBO_INSERT_ARQUIVOS_OBRIGATORIOS_FINALIZAR.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231227150934_ROLBACK_DBO_INSERT_ARQUIVOS_OBRIGATORIOS_FINALIZAR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231229104920
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231229104920_DBO_ADD_COLUMN_TIPO_ARQUIVOS_OCORRENCIA_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231229104920_ROLLBACK_DBO_ADD_COLUMN_TIPO_ARQUIVOS_OCORRENCIA_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240123102937
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.11.0

  - changeSet:
      id: 20240125133643
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240125133643_NOVOS_VALORES_TIPO_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240125133643_ROLLBACK_NOVOS_VALORES_TIPO_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231101134414
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231101134414_DBO_REINSERT_OLD_VALUES_ALERTA_MENSAGEM.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231101134836
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231101134836_ALTER_DBO_ALERTA_ANALISE_ENTIDADE_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231101134836_ROLLBACK_ALTER_DBO_ALERTA_ANALISE_ENTIDADE_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231101141904
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231101141904_DBO_ALTER_PROCEDURES_AND_FUNCTIONS_AUDITORIA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231101141904_ROLLBACK_DBO_ALTER_PROCEDURES_AND_FUNCTIONS_AUDITORIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231107052850
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231107052850_DBO_MOVE_COLUMN_STATUS_ARQUIVAMENTO_PROCESSOS.sql
          relativeToChangelogFile: true
        rollback:
          sqlFile:
            encoding: UTF-8
            path: rollback_scripts/20231107052850_ROLLBACK_DBO_MOVE_COLUMN_STATUS_ARQUIVAMENTO_PROCESSOS.sql
            relativeToChangelogFile: true

  - changeSet:
      id: 20240205110528
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240205110528_DBO_UPDATE_LEI_LICITACAO.sql
          relativeToChangelogFile: true
        rollback:
          sqlFile:
            encoding: UTF-8
            path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
            relativeToChangelogFile: true

  - changeSet:
      id: 20231220090653
      author: Bruno Rodrigues
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231220090653_DBO_UPDATE_LEGISLACAO_MAIOR_RETORNO_ECONOMICO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231220090653_ROLLBACK_DBO_UPDATE_LEGISLACAO_MAIOR_RETORNO_ECONOMICO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240207081608
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.12.0

  - changeSet:
      id: 20240207191546
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240207191546_CATALOGO_ALTER_CARACTERISTICAS_VALORES.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240207191546_ROLLBACK_CATALOGO_ALTER_CARACTERISTICAS_VALORES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240207191729
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.12.1

  - changeSet:
      id: 20231207220709
      author: Alexandre Medeiros
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231207220709_REMOVENDO_MODALIDADE_LEI14133.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231207220709_ROLLBACK_REMOVENDO_MODALIDADE_LEI14133.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231207221602
      author: Alexandre Medeiros
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231207221602_GARANTE_MODALIDADES_LEI14133.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240205181051
      author: Eniedson Junior
      context: "staging or prod"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240205181051_DBO_CREATE_VIEW_AUTORIDADES_CONTRATANTES_PROD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240205181051_ROLLBACK_DBO_CREATE_VIEW_AUTORIDADES_CONTRATANTES_PROD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240205181052
      author: Eniedson Junior
      context: "homolog or dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240205181052_DBO_CREATE_VIEW_AUTORIDADES_CONTRATANTES_LOCAL.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240205181052_ROLLBACK_DBO_CREATE_VIEW_AUTORIDADES_CONTRATANTES_LOCAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240208160848
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240208160848_DBO_ADD_COLUMN_ID_AUTORIDADE_CONTRATANTE_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240205181052_ROLLBACK_DBO_CREATE_VIEW_AUTORIDADES_CONTRATANTES_LOCAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240208120330
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240208120330_DBO_ALTER_FUNCTION_GET_FASES_PROCESSOS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240208120330_ROLLBACK_DBO_ALTER_FUNCTION_GET_FASES_PROCESSOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240215172114
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240215172114_DBO_REMOVE_ARQUIVO_LCIITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240215172114_ROLLBACK_DBO_REMOVE_ARQUIVO_LCIITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240221132852
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240221132852_DBO_REMOVE_OLD_FILES_DESCRICAO_NECESSIDADE_CONTRATACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240220113829
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240220113829_DBO_UPDATE_ARQUIVOS_ADITIVOS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240220113829_ROLLBACK_DBO_UPDATE_ARQUIVOS_ADITIVOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240220121851
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240220121851_DBO_UPDATE_ARQUIVOS_EMPENHO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240220121851_ROLLBACK_DBO_UPDATE_ARQUIVOS_EMPENHO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20231212074003
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20231212074003_DBO_CREATE_TABLE_ITEM_LOTE_FRACASSADO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20231212074003_ROLLBACK_DBO_CREATE_TABLE_ITEM_LOTE_FRACASSADO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240202172447
      author: Mirelle Maria
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240202172447_DBO_INSERT_ITEM_LOTE_TESTES.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240202172447_ROLLBACK_DBO_INSERT_ITEM_LOTE_TESTES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240229101648
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.13.0

  - changeSet:
      id: 20240131174114
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240131174114_ADD_COLUMN_FUND_LEGAL_CARONA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240131174114_ROLLBACK_ADD_COLUMN_FUND_LEGAL_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240221115050
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240221115050_DBO_UPDATE_ARQUIVOS_COMISSAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240221115050_ROLLBACK_DBO_UPDATE_ARQUIVOS_COMISSAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240221205322
      author: Regina Letícia
      context: "dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240221205322_DBO_UPDATE_COLUMN_REGISTER_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240223094135
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240223094135_DBO_INSERT_ARQUIVOS_OBRIGATORIOS_SUSPENDER.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240223094135_ROLBACK_DBO_INSERT_ARQUIVOS_OBRIGATORIOS_SUSPENDER.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240216171844
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240216171844_DBO_ALTER_TABLE_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240216171844_ROLLBACK_DBO_ALTER_TABLE_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240227161117
      author: Regina Letícia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240227161117_DBO_INSERT_ARQUIVOS_OBRIGATORIOS_PRORROGAR.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240227161117_ROLLBACK_DBO_INSERT_ARQUIVOS_OBRIGATORIOS_PRORROGAR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240305165740
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240305165740_ADD_ARQUIVOS_OBRIGATORIO_REABRIR_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240305165740_ROLLBACK_ADD_ARQUIVOS_OBRIGATORIO_REABRIR_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240304203531
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240304203531_UPDATE_LEI_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240304203531_ROLLBACK_UPDATE_LEI_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240223100132
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240223100132_ADD_COLUMN_LEI_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240223100132_ROLLBACK_ADD_COLUMN_LEI_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240228205139
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240228205139_ADD_COLUMN_LEI_DISPENSA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240228205139_ROLLBACK_ADD_COLUMN_LEI_DISPENSA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240319104521
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.14.0

  - changeSet:
      id: 20240307124620
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240307124620_ALTER_SCHEMA_CATALOGO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240307124620_ROLLBACK_ALTER_SCHEMA_CATALOGO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240314180412
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240314180412_DBO_ITEM_LOTE_ADD_UNIDADE_MEDIDA_COLUMN.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240314180412_ROLLBACK_DBO_ITEM_LOTE_ADD_UNIDADE_MEDIDA_COLUMN.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240319220435
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.14.1

  - changeSet:
      id: 20240221161832
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240221161832_DBO_CONTRATO_ENTIDADE_EXTERNA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240221161832_ROLLBACK_DBO_CONTRATO_ENTIDADE_EXTERNA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240315170900
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240315170900_DBO_UPDATE_OBRIGATORIEDADE_ARQUIVO_DISPENSA_8666.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240315170900_ROLLBACK_DBO_UPDATE_OBRIGATORIEDADE_ARQUIVO_DISPENSA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240318154447
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240318154447_DBO_INSERT_OBRIGATORIEDADE_ARQUIVO_DISPENSA_8666.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240318155100
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240318155100_DBO_INSERT_OBRIGATORIEDADE_ARQUIVO_DISPENSA_14133.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240318155308
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240318155308_DBO_INSERT_OBRIGATORIEDADE_ARQUIVO_DISPENSA_OUTRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240320145747
      author: Everton Kauan
      context: "dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240320145747_DBO_INSERT_ITEM_LOTE_TESTES.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240320145747_ROLLBACK_DBO_INSERT_ITEM_LOTE_TESTES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240125144454
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240125144454_DBO_ADD_COLUMN_REQUISICAO_MODIFICACAO_ANTIGA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240125144454_ROLLBACK_DBO_ADD_COLUMN_REQUISICAO_MODIFICACAO_ANTIGA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240325095523
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.15.0

  - changeSet:
      id: 20240319074642
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240319074642_UPDATE_INSERT_OBRIGATORIEDADE_CONFIGURACAO_FILTRO_ARQUIVO_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240319074642_ROLLBACK_UPDATE_INSERT_CONFIGURACAO_FILTRO_ORBIGATORIEDADE_ARQUIVO_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240327231546
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240327231546_DBO_ALTER_VIEW_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240327231546_ROLLBACK_DBO_ALTER_VIEW_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240403142241
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240403142241_CREATE_TABLE_NATUREZA_OBJETO_CARONA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240403142241_ROLLBACK_CREATE_TABLE_NATUREZA_OBJETO_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240322111852
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240322111852_DBO_UPDATE_ARQUIVOS_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240322111852_ROLLBACK_DBO_UPDATE_ARQUIVOS_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240403110338
      author: Iasmim Torres
      context: "staging or prod"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240403110338_DBO_INSERT_GRUPO_DAFO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240403110338_ROLLBACK_DBO_INSERT_GRUPO_DAFO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240405112925
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.16.0

  - changeSet:
      id: 20240404231029
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240404231029_DBO_ADD_ARQUIVOS_OBRIGATORIO_AVISO_REABERTURA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240404231029_ROLLBACK_DBO_ADD_ARQUIVOS_OBRIGATORIO_AVISO_REABERTURA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240409122808
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240409122808_DBO_ADD_UPDATE_ID_REQUISICAO_MODIFICACAO_PROCESSOS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240326135429
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240326135429_DBO_ITEM_LOTE_ADICIONA_FRACIONARIO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240326135429_ROLLBACK_DBO_ITEM_LOTE_ADICIONA_FRACIONARIO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240405164355
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240405164355_DBO_UPDATE_CRITERIOS_JULGAMENTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240405164355_ROLLBACK_DBO_UPDATE_CRITERIOS_JULGAMENTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240321113803
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240321113803_DBO_ALTER_TABLE_VENCEDOR_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240321113803_ROLLBACK_DBO_ALTER_TABLE_VENCEDOR_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240321113810
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240321113810_DBO_ALTER_TABLE_ITEM_LOTE_FRACASSADO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240321113810_ROLLBACK_DBO_ALTER_TABLE_ITEM_LOTE_FRACASSADO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240321113817
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240321113817_DBO_ALTER_TABLES_PROCESSOS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240321113817_ROLLBACK_DBO_ALTER_TABLES_PROCESSOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240321113827
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240321113827_DBO_CREATE_TABLE_ITEM_DESERTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240321113827_ROLLBACK_DBO_CREATE_TABLE_ITEM_DESERTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240321113838
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240321113838_DBO_ALTER_TABLE_DISPENSA_LICITANTE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240321113838_ROLLBACK_DBO_ALTER_TABLE_DISPENSA_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240321204728
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240321204728_DBO_ALTER_TABLE_INEXIGIBILIDADE_LICITANTE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240321204728_ROLLBACK_DBO_ALTER_TABLE_INEXIGIBILIDADE_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240321213011
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240321213011_DBO_ALTER_TABLE_CARONA_LICITANTE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240321213011_ROLLBACK_DBO_ALTER_TABLE_CARONA_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240410191204
      author: Lucian Julio
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240410191204_DBO_UPDATE_TERMO_REFERENCIA_TESTES.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240411121222
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.17.0

  - changeSet:
      id: 20240411080145
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240411080145_ALTER_VIEW_CARONA_MATRIZ_RISCO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240411080145_ROLLBACK_ALTER_VIEW_CARONA_MATRIZ_RISCO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240412142212
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240412142212_DBO_UPDATE_CRITERIOS_JULGAMENTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240412142212_ROLLBACK_DBO_UPDATE_CRITERIOS_JULGAMENTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240402163816
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240402163816_ADD_COLUMN_LEI_CARONA_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240402163816_ROLLBACK_ADD_COLUMN_LEI_CARONA_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240402164103
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240402164103_DBO_INSERTS_CONFIGURACAO_ARQUIVOS_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240402164103_ROLLBACK_DBO_INSERTS_CONFIGURACAO_ARQUIVOS_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240403123640
      author: Vanderson dos Santos Araújo
      context: "homolog or dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240403123640_UPDATE_LEI_TESTE_PROCESSOS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240403123640_ROLLBACK_UPDATE_LEI_TESTE_PROCESSOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240328113509
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240328113509_DBO_ALTER_TABLE_CONTRATO_LICITANTE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240328113509_ROLLBACK_DBO_ALTER_TABLE_CONTRATO_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240328113510
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240328113510_DBO_ALTER_TABLE_ITEM_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240328113510_ROLLBACK_DBO_ALTER_TABLE_ITEM_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240412193553
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240412193553_DBO_UPDATE_CONTRATO_LICITANTE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240412204817
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240412204817_ALTER_FORNECEDORES_CONTRATADOS_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240415152928
      author: Lucian Julio
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240415152928_DBO_UPDATE_PROCESSOS_TESTES.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240415235930
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.18.0

  - changeSet:
      id: 20240416184855
      author: Vanderson dos Santos Araújo
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240416184855_UPDATE_ARQUIVO_CONTRATO_ORDEM.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240416211333
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.18.1

  - changeSet:
      id: 20240417111546
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240417111546_CREATE_BOARD_LICITACAO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240417111546_ROLLBACK_CREATE_BOARD_LICITACAO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240417115157
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240417115157_CREATE_INDEX_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240417115157_ROLLBACK_CREATE_INDEX_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240423230315
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.19.0

  - changeSet:
      id: 20240404171100
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240404171100_DBO_ADD_TIPOS_OBRIGATORIEDADE_ARQUIVOS.sql
          relativeToChangelogFile: true
        rollback:
          sqlFile:
            encoding: UTF-8
            path: rollback_scripts/20240404171100_ROLLBACK_DBO_ADD_TIPOS_OBRIGATORIEDADE_ARQUIVOS.sql
            relativeToChangelogFile: true

  - changeSet:
      id: 20240423131759
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240423131759_DBO_UPDATE_OBRIGATORIEDADE_ARQUIVOS.sql
          relativeToChangelogFile: true
        rollback:
          sqlFile:
            encoding: UTF-8
            path: rollback_scripts/20240423131759_ROLLBACK_DBO_UPDATE_OBRIGATORIEDADE_ARQUIVOS.sql
            relativeToChangelogFile: true

  - changeSet:
      id: 20240426132341
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240426132341_DBO_INSERT_ARQUIVO_OCORRENCIA_LICITACAO_CONTINUAR.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240426132341_ROLLBACK_DBO_INSERT_ARQUIVO_OCORRENCIA_LICITACAO_CONTINUAR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240428124927
      author: Eniedson Junior
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240428124927_DBO_ALTER_OCORRENCIA_DATA_AVISO_ALLOWS_NULL.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240428124927_ROLLBACK_DBO_ALTER_OCORRENCIA_DATA_AVISO_ALLOWS_NULL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240411090158
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240411090158_UPDATE_VALORES_FUNDAMENTACAO_LEGAL_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240411090158_ROLLBACK_UPDATE_VALORES_FUNDAMENTACAO_LEGAL_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240410100522
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240422102421_DBO_DELETE_FUNDAMENTACOES_LEGAIS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240422102421_ROLLBACK_DBO_DELETE_FUNDAMENTACOES_LEGAIS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240410100801
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240410100801_DBO_INSERT_FUNDAMENTACOES_LEGAIS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240410100801_ROLLBACK_DBO_INSERT_FUNDAMENTACOES_LEGAIS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240411230424
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240411230424_UPDATE_DBO_FUNDAMENTACOES_LEGAIS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240411230424_ROLLBACK_UPDATE_DBO_FUNDAMENTACOES_LEGAIS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240415220722
      author: Iasmim Torres
      context: "homolog or dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240415220722_DBO_INSERT_FUNDAMENTACAO_OUTRA_TESTES.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240415220722_ROLLBACK_DBO_INSERT_FUNDAMENTACAO_OUTRA_TESTES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240429150736
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240429150736_ALTER_BOARD_LICITACAO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240429150736_ROLLBACK_ALTER_BOARD_LICITACAO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240503203253
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.20.0

  - changeSet:
      id: 20240513102146
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240513102146_INSERT_PARA_RETIFICACAO_EDITAL_ARQUIVOS_OBRIGATORIOS_SUSPENDER.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240513102146_ROLLBACK_INSERT_PARA_RETIFICACAO_EDITAL_ARQUIVOS_OBRIGATORIOS_SUSPENDER.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240517103820
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.21.0

  - changeSet:
      id: 20240509172927
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240509172927_GEOOBRAS_CREATE_SCHEMA_GEOOBRAS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240509172927_ROLLBACK_GEOOBRAS_CREATE_SCHEMA_GEOOBRAS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240509175232
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240509175232_GEOOBRAS_CREATE_TABLE_TIPO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240509175232_ROLLBACK_GEOOBRAS_CREATE_TABLE_TIPO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240509214440
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240509214440_AUDITORIA_CREATE_TABLE_TIPO_OBRA_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240509214440_ROLLBACK_AUDITORIA_CREATE_TABLE_TIPO_OBRA_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240509224321
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240509224321_DBO_ADD_PERMISSAO_TIPO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240509224321_ROLLBACK_DBO_ADD_PERMISSAO_TIPO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240510210653
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240510210653_DBO_CREATE_TABLE_OCORRENCIA_ITEM_LOTE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240510210653_ROLLBACK_DBO_CREATE_TABLE_OCORRENCIA_ITEM_LOTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240312152436
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240312152436_DBO_CREATE_TABLE_OCORRENCIA_LICITACAO_ITEM_LOTE_FRACASSADO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240312152436_ROLLBACK_DBO_CREATE_TABLE_OCORRENCIA_LICITACAO_ITEM_LOTE_FRACAS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240324114653
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240324114653_DBO_CREATE_TABLE_OCORRENCIA_LICITACAO_VENCEDOR.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240324114653_ROLLBACK_DBO_CREATE_TABLE_OCORRENCIA_LICITACAO_VENCEDOR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240425102407
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240425102407_DBO_CREATE_OCORRENCIA_LICITACAO_ITEM_LOTE_DESERTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240425102407_ROLLBACK_DBO_CREATE_OCORRENCIA_LICITACAO_ITEM_LOTE_DESERT.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240506073849
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240506073849_CATALOGO_ALTER_ALL_UPDATE_CONSTRAINT_UNIQUE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240506073849_ROLLBACK_CATALOGO_ALTER_ALL_UPDATE_CONSTRAINT_UNIQUE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240514153316
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240514153316_UPDATE_DADOS_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240520185406
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240520185406_CREATE_INDEX_LICITACAO_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240520185406_ROLLBACK_CREATE_INDEX_LICITACAO_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240520185430
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240520185430_CREATE_INDEX_CARONA_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240520185430_ROLLBACK_CREATE_INDEX_CARONA_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240520185445
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240520185445_CREATE_INDEX_DISPENSA_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240520185445_ROLLBACK_CREATE_INDEX_DISPENSA_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240520185456
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240520185456_CREATE_INDEX_INEXIGIBILIDADE_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240520185456_ROLLBACK_CREATE_INDEX_INEXIGIBILIDADE_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240520185507
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240520185507_ALTER_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240520185507_ROLLBACK_ALTER_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240515102117
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240515102117_UPDATE_DADOS_FALTANTES_EM_LICITANTE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240515102117_ROLLBACK_UPDATE_DADOS_FALTANTES_EM_LICITANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240509110543
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240509110543_UPDATE_VALOR_ADJUDICADO_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240509110543_ROLLBACK_UPDATE_VALOR_ADJUDICADO_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240509110241
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240509110241_CREATE_LICITACAO_SRP_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240509110241_ROLLBACK_CREATE_LICITACAO_SRP_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240520143219
      author: Lucian Julio
      context: "homolog or dev"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240520143219_DBO_INSERT_PROCESSO_LICITACAO_TEST_CARONA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240515105233
      author: Vanderson dos Santos Araújo
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240515105233_UPDATE_PROCESSOS_TESTES.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240510141624
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240510141624_DBO_SUBSTITUI_ARQUIVOS_CARONA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240510141624_ROLLBACK_DBO_SUBSTITUI_ARQUIVOS_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240510141625
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240510141625_DBO_ADICIONA_ARQUIVOS_CARONA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240510141625_ROLLBACK_DBO_ADICIONA_ARQUIVOS_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240510141626
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240510141626_DBO_ADICIONA_CONFIGURACOES_E_FILTROS_ARQUIVOS_CARONA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240510141626_ROLLBACK_DBO_ADICIONA_CONFIGURACOES_E_FILTROS_ARQUIVOS_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240510141627
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240510141627_DBO_ATUALIZA_TABELA_ARQUIVO_CARONA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240510141627_ROLLBACK_DBO_ATUALIZA_TABELA_ARQUIVO_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240526103659
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.22.0

  - changeSet:
      id: 20240522144952
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240522144952_CREATE_INDEX_GERENCIAMENTO_TERMO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240522144952_ROLLBACK_CREATE_INDEX_GERENCIAMENTO_TERMO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240522145011
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240522145011_CREATE_GERENCIAMENTO_TERMO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240522145011_ROLLBACK_CREATE_GERENCIAMENTO_TERMO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240531140336
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240531140336_DBO_AJUSTES_ARQUIVOS_CARONA_ANTIGA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240531140336_ROLLBACK_DBO_AJUSTES_ARQUIVOS_CARONA_ANTIGA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240528164725
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240528164725_DBO_ADD_COLUMN_ID_RESPONSAVEL_PROCESSOS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240528164725_ROLLBACK_DBO_ADD_COLUMN_ID_RESPONSAVEL_PROCESSOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240527143836
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240527143836_CREATE_CONTRATO_LIST_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240527143836_ROLLBACK_CREATE_CONTRATO_LIST_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240610110241
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.23.0

  - changeSet:
      id: 20240611192505
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240611192505_DBO_CREATE_TABLES_RELATORIO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240611192505_ROLLBACK_DBO_CREATE_TABLES_RELATORIO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240521114333
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240521114333_DBO_CREATE_TABLE_CONFIGURACOES_EMAIL.sql
          relativeToChangelogFile: true
        rollback:
          sqlFile:
            encoding: UTF-8
            path: rollback_scripts/20240521114333_ROLLBACK_DBO_CREATE_TABLE_CONFIGURACOES_EMAIL.sql
            relativeToChangelogFile: true

  - changeSet:
      id: 20240610081958
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240610081958_DBO_CREATE_PROCESSO_LICITACAO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240610081958_ROLLBACK_DBO_CREATE_PROCESSO_LICITACAO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240606135013
      author: Erick Farias
      context: "dev or homolog"
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240606135013_DBO_UPDATE_CONTRATO_LICITACAO_TESTES.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240618141250
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240618141250_ADICIONA_COLUNAS_VIGENCIAS_CONTRATO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240618141250_ROLLBACK_ADICIONA_COLUNAS_VIGENCIAS_CONTRATO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240606133635
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240606133635_ADD_TIPO_ALTERACAO_ADITIVO_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240606133635_ROLLBACK_ADD_TIPO_ALTERACAO_ADITIVO_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240611120009
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240611120009_CREATE_PROC_ADITIVOS_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240611120024
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240611120024_CREATE_PROC_UPDATE_ADITIVOS_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240610112927
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240610112927_DBO_ALTER_VIEW_VW_BOARD_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240610112927_ROLLBACK_DBO_ALTER_VIEW_VW_BOARD_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240610112928
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240610112928_DBO_ALTER_VIEW_ANALISE_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240610112928_ROLLBACK_DBO_ALTER_VIEW_ANALISE_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240620161109
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240620161109_UPDATE_ARQUIVO_MAPA_LANCES_ARQ_OBG_CARONA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240620161109_ROLLBACK_UPDATE_ARQUIVO_MAPA_LANCES_ARQ_OBG_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240621110825
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240621110825_DBO_ALTER_ALERTA_ANALISE_ENTIDADE_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240621110825_DBO_ROLLBACK_ALTER_ALERTA_ANALISE_ENTIDADE_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240628202043
      author: Pedro Manoel
      changes:
        - tagDatabase:
            tag: 2.24.0

  - changeSet:
      id: 20240614152940
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240614152940_DBO_INSERT_MODALIDADES_LEI_OUTRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240614152940_ROLLBACK_DBO_INSERT_MODALIDADES_LEI_OUTRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240614152954
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240614152954_DBO_INSERT_FUND_LEGAL_LEI_OUTRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240614152954_ROLLBACK_DBO_INSERT_FUND_LEGAL_LEI_OUTRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240516102526
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240516102526_GEOOBRAS_CREATE_TABLE_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240516102526_ROLLBACK_GEOOBRAS_CREATE_TABLE_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240516150128
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240516150128_AUDITORIA_CREATE_TABLE_OBRA_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240516150128_ROLLBACK_AUDITORIA_CREATE_TABLE_OBRA_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240522085227
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240522085227_GEOOBRAS_CREATE_TABLE_CONVENIO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240522085227_ROLLBACK_GEOOBRAS_CREATE_TABLE_CONVENIO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240522090717
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240522090717_AUDITORIA_CREATE_TABLE_CONVENIO_OBRA_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240522090717_ROLLBACK_AUDITORIA_CREATE_TABLE_CONVENIO_OBRA_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240527092702
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240527092702_GEOOBRAS_CREATE_TABLE_ARQUIVO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240527092702_ROLLBACK_GEOOBRAS_CREATE_TABLE_ARQUIVO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240531080231
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240531080231_DBO_ADD_PERMISSAO_GEOOBRA_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240531080231_ROLLBACK_DBO_ADD_PERMISSAO_GEOOBRA_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240611042744
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240611042744_DBO_ALTER_TABLE_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240611042744_ROLLBACK_DBO_ALTER_TABLE_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240611042744
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240611042744_DBO_ALTER_TABLE_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240611042744_ROLLBACK_DBO_ALTER_TABLE_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240619130910
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240619130910_INSERT_TIPO_ARQUIVO_OCORRENCIA_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240619130910_ROLLBACK_INSERT_TIPO_ARQUIVO_OCORRENCIA_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240704134641
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240704134641_DBO_ADICIONA_CNPJ_CONTRATADO_CONTRATO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240704134641_DBO_ROLLBACK_ADICIONA_CNPJ_CONTRATADO_CONTRATO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240610163151
      author: Regina Leticia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240610163151_ALTER_BOARD_LICITACAO_VIEW_ADD_TERMO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240610163151_ROLLBACK_ALTER_BOARD_LICITACAO_VIEW_ADD_TERMO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240618140152
      author: Regina Leticia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240618140152_ALTER_TERMO_ADD_COLUMN_BLOCKED.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240618140152_ROLLBACK_ALTER_TERMO_ADD_COLUMN_BLOCKED.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240618140253
      author: Regina Leticia
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240618140253_ALTER_BOARD_TERMO_VIEW_ADD_COLUMN_BLOCKED.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240618140253_ROLLBACK_ALTER_BOARD_TERMO_VIEW_ADD_COLUMN_BLOCKED.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240708164208
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240708164208_DBO_ALTER_TABLE_ITEM_DESERTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240708164208_ROLLBACK_DBO_ALTER_TABLE_ITEM_DESERTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240708164243
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240708164243_DBO_ALTER_TABLE_ITEM_LOTE_FRACASSADO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240708164243_ROLLBACK_DBO_ALTER_TABLE_ITEM_LOTE_FRACASSADO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240709144125
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240709144125_GEOOBRAS_ALTER_TABLE_ITEM_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240709144125_ROLLBACK_GEOOBRAS_ALTER_TABLE_ITEM_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240709150107
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240709150107_GEOOBRAS_CREATE_TABLE_ITEM_OBRA_NON_MATCHING.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240709150107_ROLLBACK_GEOOBRAS_CREATE_TABLE_ITEM_OBRA_NON_MATCHING.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240709203759
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240709203759_DBO_ALTER_TABLE_GEOOBRAS_ADD_DATA_DESEJAVEL.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240709203759_ROLLBACK_DBO_ALTER_TABLE_GEOOBRAS_ADD_DATA_DESEJAVEL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240715165813
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240715165813_GEOOBRAS_ALTER_TABLE_RELATORIO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240715165813_ROLLBACK_GEOOBRAS_ALTER_TABLE_RELATORIO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240716150316
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240716150316_GEOOBRAS_ALTER_TABLE_ITEM_OBRA_NON_MATCHING.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240716150316_ROLLBACK_GEOOBRAS_ALTER_TABLE_ITEM_OBRA_NON_MATCHING.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240527133139
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240527133139_DBO_CREATE_USUARIO_DIRETOR_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240527133139_ROLLBACK_DBO_CREATE_USUARIO_DIRETOR_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240702155214
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240702155214_DBO_PROC_UPDATE_TIPO_ALTERACAO_DADOS_MIGRADOS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240702155214_ROLLBACK_DBO_PROC_UPDATE_TIPO_ALTERACAO_DADOS_MIGRADOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240702164016
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240702164016_DBO_ALTER_CONTRATO_ADD_ADITIVADOS_SUPRIMIDOS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240702164016_ROLLBACK_DBO_ALTER_CONTRATO_ADD_ADITIVADOS_SUPRIMIDOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240704075030
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240704075030_DBO_PROC_UPDATE_ADITIVADOS_SUPRIMIDOS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240704075030_ROLLBACK_DBO_PROC_UPDATE_ADITIVADOS_SUPRIMIDOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240704075433
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240704075433_DBO_UPDATE_CONTRATO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240704075433_ROLLBACK_DBO_UPDATE_CONTRATO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240708162154
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240708162154_DBO_ADD_COLUNA_DATA_VIGENTE_ATUAL_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240708162154_ROLLBACK_DBO_ADD_COLUNA_DATA_VIGENTE_ATUAL_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240708164000
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240708164000_PROC_UPDATE_DATA_FINAL_VIGENTE_DADOS_MIGRADOS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240708164000_ROLLBACK_PROC_UPDATE_DATA_FINAL_VIGENTE_DADOS_MIGRADOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240708165648
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240708165648_DBO_ADICIONA_COLUNA_DATA_VIGENTE_CONTRATO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240708165648_ROLLBACK_DBO_ADICIONA_COLUNA_DATA_VIGENTE_CONTRATO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240719173355
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240719173355_GEOOBRAS_ALTER_TABLE_RELATORIO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240719173355_ROLLBACK_GEOOBRAS_ALTER_TABLE_RELATORIO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240722162944
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.25.0

  - changeSet:
      id: 20240704145223
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240704145223_DBO_CREATE_TABLE_CREDENCIAMENTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240704145223_ROLLBACK_DBO_CREATE_TABLE_CREDENCIAMENTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240708103558
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240708103558_AUDITORIA_CREATE_TABLE_CREDENCIAMENTO_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240708103558_ROLLBACK_AUDITORIA_CREATE_TABLE_CREDENCIAMENTO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240708104908
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240708104908_DBO_ADD_PERMISSAO_CREDENCIAMENTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240708104908_ROLLBACK_DBO_ADD_PERMISSAO_CREDENCIAMENTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240708105150
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240708105150_DBO_CREATE_VIEW_VW_ULTIMA_ALTERACAO_CREDENCIAMENTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240708105150_ROLLBACK_DBO_CREATE_VIEW_VW_ULTIMA_ALTERACAO_CREDENCIAMENTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240530123059
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240530123059_GEOOBRAS_CREATE_TABLE_MEDICAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240530123059_ROLLBACK_GEOOBRAS_CREATE_TABLE_MEDICAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240530123321
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240530123321_AUDITORIA_CREATE_TABLE_MEDICAO_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240530123321_ROLLBACK_AUDITORIA_CREATE_TABLE_MEDICAO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240530123512
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240530123512_GEOOBRAS_CREATE_TABLE_ARQUIVO_MEDICAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240530123512_ROLLBACK_GEOOBRAS_CREATE_TABLE_ARQUIVO_MEDICAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240530123840
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240530123840_GEOOBRAS_CREATE_VIEW_ACOMPANHAMENTO_OBRA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240530123840_ROLLBACK_GEOOBRAS_CREATE_VIEW_ACOMPANHAMENTO_OBRA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240607223136
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240607223136_DBO_ALTER_TABLE_ENTE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240607223136_ROLLBACK_DBO_ALTER_TABLE_ENTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240607223137
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240607223137_GEOOBRAS_CREATE_TABLE_CONFIGURACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240607223137_ROLLBACK_GEOOBRAS_CREATE_TABLE_CONFIGURACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240607223138
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240607223138_GEOOBRAS_CREATE_VIEW_VW_ENTE_BUFFERED.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240607223138_ROLLBACK_GEOOBRAS_VIEW_VW_ENTE_BUFFERED.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240608122012
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240608122012_GEOOBRAS_INSERT_INTO_OBRIGATORIEDADE_ARQUIVO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240608122012_ROLLBACK_GEOOBRAS_INSERT_INTO_OBRIGATORIEDADE_ARQUIVO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240621160900
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240621160900_GEOOBRAS_ALTER_TABLE_ARQUIVO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240621160900_ROLLBACK_GEOOBRAS_ALTER_TABLE_ARQUIVO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240611164713
      author: Rayane Silva
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240611164713_CREATE_CARONA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240611164713_ROLLBACK_CREATE_CARONA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240612115039
      author: Rayane Silva
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240612115039_CREATE_INDEX_CARONA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240612115039_ROLLBACK_CREATE_INDEX_CARONA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240621142152
      author: Rayane Silva
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240621142152_DBO_CREATE_INEXIGIBILIDADE_ORGAO_PARTICIPANTE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240621142152_ROLLBACK_DBO_CREATE_INEXIGIBILIDADE_ORGAO_PARTICIPANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240620110757
      author: Rayane Silva
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240620110757_AUDITORIA_CREATE_DISPENSA_ORGAO_PARTICIPANTE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240620110757_ROLLBACK_AUDITORIA_CREATE_DISPENSA_ORGAO_PARTICIPANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240620121927
      author: Rayane Silva
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240620121927_DBO_CREATE_DISPENSA_ORGAO_PARTICIPANTE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240620121927_ROLLBACK_DBO_CREATE_DISPENSA_ORGAO_PARTICIPANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240621141650
      author: Rayane Silva
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240621141650_AUDITORIA_CREATE_INEXIGIBILIDADE_ORGAO_PARTICIPANTE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240621141650_ROLLBACK_AUDITORIA_CREATE_INEXIGIBILIDADE_ORGAO_PARTICIPANTE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240625174320
      author: Rayane Silva
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240625174320_ALTER_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240625174320_ROLLBACK_ALTER_PROCESSO_LICON_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240614151352
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240614151352_ALTER_TABLE_EMPENHO_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240614151352_ROLLBACK_ALTER_TABLE_EMPENHO_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240711163514
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240711163514_ADD_COLUMN_FASE_ARQUIVO_OCORRENCIA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240711163514_ROLLBACK_ADD_COLUMN_FASE_ARQUIVO_OCORRENCIA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240726133555
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240726133555_DBO_UPDATE_PROCESS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240726133555_ROLLBACK_DBO_UPDATE_PROCESS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240726151543
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240726151543_UPDATE_ARQUIVO_MAPA_COMPARATIVO_PRECO_ARQ_OBG_CRED.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240726151543_ROLLBACK_UPDATE_ARQUIVO_MAPA_COMPARATIVO_PRECO_ARQ_OBG_CRED.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240801092100
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240801092100_DBO_UPDATE_PROCESS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240801092100_ROLLBACK_DBO_UPDATE_PROCESS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240715095744
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240715095744_DBO_CREATE_TABLE_CREDENCIADO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240715095744_ROLLBACK_DBO_CREATE_TABLE_CREDENCIADO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240715100054
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240715100054_AUDITORIA_CREATE_TABLE_CREDENCIADO_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240715100054_ROLLBACK_AUDITORIA_CREATE_TABLE_CREDENCIADO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240716142949
      author: Eliane Tamara
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240716142949_DBO_ADD_PERMISSAO_CREDENCIADO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240716142949_ROLLBACK_DBO_ADD_PERMISSAO_CREDENCIADO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240611142122
      author: Guilherme Aureliano
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240611142122_DBO_ADICIONA_ATRIBUTO_TENTATIVAS_MATCHING_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240611142122_ROLLBACK_20240611142122_DBO_ADICIONA_ATRIBUTO_TENTATIVAS_MATCHING_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240724192124
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240724192124_GEOOBRAS_CREATE_VIEW_MAPA_OBRA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240724192124_ROLLBACK_GEOOBRAS_CREATE_VIEW_MAPA_OBRA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240716093544
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240716093544_ALTER_PROCESSO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240716093544_ROLLBACK_ALTER_PROCESSO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240726211013
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240726211013_DB0_ALTER_TABLE_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240726211013_ROLLBACK_DB0_ALTER_TABLE_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240726211250
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240726211250_DBO_ALTER_TABLE_ITEM_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240726211250_ROLLBACK_DBO_ALTER_TABLE_ITEM_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240726211455
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240726211455_DBO_INSERT_CONFIGURACAO_ARQUIVO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240726211455_ROLLBACK_DBO_INSERT_CONFIGURACAO_ARQUIVO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240801145139
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240801145139_DBO_ALTER_ALERTA_ANALISE_ADD_REJEICAO_INSPETOR.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240801145139_ROLLBACK_DBO_ALTER_ALERTA_ANALISE_ADD_REJEICAO_INSPETOR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240731133556
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240731133556_ALTER_TABLE_SICRO_SINAPI.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240731133556_ROLLBACK_ALTER_TABLE_SICRO_SINAPI.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240807164002
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240807164002_DBO_ALTER_TABLE_CREDENCIADO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240807164002_ROLLBACK_DBO_ALTER_TABLE_CREDENCIADO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240807183826
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.26.0

  - changeSet:
      id: 20240726142858
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240726142858_DBO_ALTER_TABLE_OBRA_ATRIBUTO_CREDENCIAMENTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240726142858_ROLLBACK_DBO_ALTER_TABLE_OBRA_ATRIBUTO_CREDENCIAMENTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240619085119
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240619085119_GEOOBRAS_INSERT_TIPO_DIMENSAO_COLUMN_IN_TIPO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240619085119_ROLLBACK_GEOOBRAS_INSERT_TIPO_DIMENSAO_COLUMN_IN_TIPO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240620082947
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240620082947_AUDITORIA_INSERT_TIPO_DIMENSAO_COLUMN_IN_TIPO_OBRA_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240620082947_ROLLBACK_AUDITORIA_INSERT_TIPO_DIMENSAO_COLUMN_IN_TIPO_OBRA_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240620101216
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240620101216_GEOOBRAS_CREATE_TABLE_RECURSO_PROPRIO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240620101216_ROLLBACK_GEOOBRAS_CREATE_TABLE_RECURSO_PROPRIO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240620103758
      author: Pedro Manoel
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240620103758_AUDITORIA_CREATE_TABLE_RECURSO_PROPRIO_OBRA_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240620103758_ROLLBACK_AUDITORIA_CREATE_TABLE_RECURSO_PROPRIO_OBRA_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240809140513
      author: Eliane
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240809140513_DBO_CREATE_CONTRATOS_CREDENCIADO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240809140513_ROLLBACK_DBO_CREATE_CONTRATOS_CREDENCIADO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240812090939
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240812090939_DBO_ALTER_BOARD_LICITACAO_VIEW_ADD_ULTIMA_ALTERACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240812090939_ROLLBACK_DBO_ALTER_BOARD_LICITACAO_VIEW_ADD_ULTIMA_ALTERACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240808141547
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240808141547_DBO_UPDATE_ARQUIVO_IMPUGNACAO_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240808141547_ROLLBACK_DBO_UPDATE_ARQUIVO_IMPUGNACAO_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240808151507
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240808151507_DBO_UPDATE_ARQUIVOS_CARONA_AUTORIDADE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240808151507_ROLLBACK_DBO_UPDATE_ARQUIVOS_CARONA_AUTORIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240821100155
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.27.0

  - changeSet:
      id: 20240812091404
      author: Rayane Silva
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240812091404_CREATE_CARONA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240812091404_ROLLBACK_CREATE_CARONA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240812095516
      author: Rayane Silva
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240812095516_CREATE_INDEX_CARONA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240812095516_ROLLBACK_CREATE_INDEX_CARONA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240812222219
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240812222219_GEOOBRAS_CREATE_CONTRATO_OBRA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240812222219_ROLLBACK_GEOOBRAS_CREATE_CONTRATO_OBRA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240710140231
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240710140231_ADD_DISPENSA_INEXIGIBILIDADE_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240710140231_ROLLBACK_ADD_DISPENSA_INEXIGIBILIDADE_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240809140513
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240820155228_DBO_ALTER_TABLE_OBRA_FK_DISPENSA_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240820155228_ROLLBACK_DBO_ALTER_TABLE_OBRA_FK_DISPENSA_INEXIGIBILIDADE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240820153853
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240820153853_DBO_ALTER_OBRA_FK_CREDENCIAMENTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240820153853_ROLLBACK_DBO_ALTER_OBRA_FK_CREDENCIAMENTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240821102630
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240821102630_DBO_UPDATE_ARQUIVO_AUTORIDADE_CARONA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240821102630_ROLLBACK_DBO_UPDATE_ARQUIVO_AUTORIDADE_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240711132237
      author: Guilherme Aureliano
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240711132237_DBO_ALTER_TABLE_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240711132237_ROLLBACK_DBO_ALTER_TABLE_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240716083643
      author: Guilherme Aureliano
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240716083643_AUDITORIA_LICITACAO_ADD_COLUMN_EDITAL.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240716083643_ROLLBACK_AUDITORIA_LICITACAO_ADD_COLUMN_EDITAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240815094146
      author: Guilherme Aureliano
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240815094146_ALTER_TABLE_AUDITORIA_LICITACAO_ADD_COLUMN_TENTATIVAS_MATCH.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240815094146_ROLLBACK_ALTER_TABLE_AUDITORIA_LICITACAO_ADD_COLUMN_TENTATIVAS_MATCH.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240821085315
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240821085315_DBO_ADD_UPDATED_AT_USER_UPDATED_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240821085315_ROLLBACK_DBO_ADD_UPDATED_AT_USER_UPDATED_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240821093225
      author: Eliane
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240821093225_DBO_AJUSTE_ARQUIVOS_CREDENCIAMENTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240821093225_ROLLBACK_DBO_AJUSTE_ARQUIVOS_CREDENCIAMENTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240821130647
      author: Eliane
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240821130647_DBO_ADD_ARQUIVOS_CREDENCIADO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240821130647_ROLLBACK_DBO_ADD_ARQUIVOS_CREDENCIADO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240822081507
      author: Eliane
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240822081507_DBO_ADD_ARQUIVOS_CONTRATO_CRED.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240822081507_ROLLBACK_DBO_ADD_ARQUIVOS_CONTRATO_CRED.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240823155941
      author: Eliane
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240823155941_DBO_CONFIGURACAO_ARQUIVOS_CONTRATO_CRED_14133.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240823155941_ROLLBACK_DBO_CONFIGURACAO_ARQUIVOS_CONTRATO_CRED_14133.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240823155956
      author: Eliane
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240823155956_DBO_CONFIGURACAO_ARQUIVOS_CONTRATO_CRED_OUTRA_LEI.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240823155956_ROLLBACK_DBO_CONFIGURACAO_ARQUIVOS_CONTRATO_CRED_OUTRA_LEI.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240731101650
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240731101650_DBO_CREATE_GRUPO_AUDITOR_DEMAIS_INSPETORIAS_COM_PERMISSOES.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240731101650_ROLLBACK_DBO_CREATE_GRUPO_AUDITOR_DEMAIS_INSPETORIAS_COM_PERMISSOES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240828103553
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240828103553_DBO_ADD_USUARIO_DIVULGACAO_LICITACAO_E_BOARD_LIC.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240828103553_ROLLBACK_DBO_ADD_USUARIO_DIVULGACAO_LICITACAO_E_BOARD_LIC.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240819152806
      author: Eliane
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240819152806_DBO_ALTER_CARONA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240819152806_ROLLBACK_DBO_ALTER_CARONA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240819154623
      author: Eliane
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240819154623_DBO_FIX_NOMENCLATURA_ARQUIVO_CREDENCIAMENTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240819154623_ROLLBACK_DBO_FIX_NOMENCLATURA_ARQUIVO_CREDENCIAMENTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240820172651
      author: Eliane
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240820172651_ADD_DESC_COMPLEMENTAR_ITENS_PROCESSO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240820172651_ROLLBACK_ADD_DESC_COMPLEMENTAR_ITENS_PROCESSO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240903113221
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.28.0

  - changeSet:
      id: 20240819171038
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240819171038_DBO_ALTER_TABLE_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240819171038_ROLLBACK_DBO_ALTER_TABLE_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240829173149
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240829173149_GEOOBRAS_ALTER_TABLE_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240829173149_ROLLBACK_GEOOBRAS_ALTER_TABLE_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240829173150
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240829173150_AUDITORIA_ALTER_TABLE_GEOOBRAS_OBRA_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240829173150_ROLLBACK_AUDITORIA_ALTER_TABLE_GEOOBRAS_OBRA_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240906140207
      author: Vinicius Ian
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240906140207_DBO_ALTER_TABLE_ITEM_CHECKLIST.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240906140207_ROLLBACK_DBO_ALTER_TABLE_ITEM_CHECKLIST.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240813002225
      author: Eniedson Junior
      runInTransaction: false
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240813002225_ROBOS_CREATE_INDEX_AND_AUX_FUNCTION.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240813002225_ROLLBACK_ROBOS_CREATE_INDEX_AND_AUX_FUNCTION.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240814221819
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240814221819_GEOOBRAS_INSERT_INTO_OBRIGATORIEDADE_ARQUIVO_OBRA_INTERRUPCAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240814221819_ROLLBACK_GEOOBRAS_INSERT_INTO_OBRIGATORIEDADE_ARQUIVO_OBRA_INTERRUPCAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240815201500
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240815201500_GEOOBRAS_ALTER_TABLE_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240815201500_ROLLBACK_GEOOBRAS_ALTER_TABLE_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240815201501
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240815201501_AUDITORIA_ALTER_TABLE_OBRA_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240815201501_ROLLBACK_AUDITORIA_ALTER_TABLE_OBRA_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240827210628
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240827210628_GEOOBRAS_ALTER_TABLE_MEDICAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240827210628_ROLLBACK_GEOOBRAS_ALTER_TABLE_MEDICAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240827211215
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240827211215_AUDITORIA_ALTER_TABLE_GEOOBRAS_MEDICAO_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240827211215_ROLLBACK_AUDITORIA_ALTER_TABLE_GEOOBRAS_MEDICAO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240827211216
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240827211216_GEOOBRAS_ALTER_ACOMPANHAMENTO_OBRA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240827211216_ROLLBACK_GEOOBRAS_ALTER_ACOMPANHAMENTO_OBRA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240913204345
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240913204345_GEOOBRAS_ALTER_CONTRATO_OBRA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240913204345_ROLLBACK_GEOOBRAS_ALTER_CONTRATO_OBRA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240916141847
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240916141847_DBO_ADD_PERMISSAO_GEOOBRA_ACOMPANHAMENTO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240916141847_ROLLBACK_DBO_ADD_PERMISSAO_GEOOBRA_ACOMPANHAMENTO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240916201925
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240916201925_GEOOBRAS_ALTER_CONTRATO_OBRA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240916201925_ROLLBACK_GEOOBRAS_ALTER_CONTRATO_OBRA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240916211031
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.29.0

  - changeSet:
      id: 20240904222100
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240904222100_GEOOBRAS_ALTER_TABLE_RECURSO_PROPRIO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240904222100_ROLLBACK_GEOOBRAS_ALTER_TABLE_RECURSO_PROPRIO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240904222101
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240904222101_AUDITORIA_ALTER_TABLE_RECURSO_PROPRIO_OBRA_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240904222101_ROLLBACK_AUDITORIA_ALTER_TABLE_RECURSO_PROPRIO_OBRA_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240913113722
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240913113722_ALTER_VIEW_PROCESSO_LICON_VIEW_LICITACAO_TIPO_LEILAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240913113722_ROLLBACK_ALTER_VIEW_PROCESSO_LICON_VIEW_LICITACAO_TIPO_LEILAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240916125949
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240916125949_ALTER_VIEW_LICITACAO_SRP_VIEW_LEILAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240916125949_ROLLBACK_ALTER_VIEW_LICITACAO_SRP_VIEW_LEILAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240902113210
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240902113210_DBO_ADD_DESC_COMPLEMENTAR_ITEM_CONTRATO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240902113210_ROLLBACK_DBO_ADD_DESC_COMPLEMENTAR_ITEM_CONTRATO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240926233945
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240926233945_ALTER_TABLE_CARONA_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240926233945_ROLLBACK_ALTER_TABLE_CARONA_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240926231304
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.30.0

  - changeSet:
      id: 20240917142731
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240917142731_DBO_ALTER_TABLE_CARONA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240917142731_ROLLBACK_DBO_ALTER_TABLE_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240917093956
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240917093956_DBO_ADD_ARQ_OBR_OCORRENCIA_LICITACAO_PRORROGAR.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240917093956_ROLLBACK_DBO_ADD_ARQ_OBR_OCORRENCIA_LICITACAO_PRORROGAR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240927122657
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.31.0

  - changeSet:
      id: 20240901162744
      author: Rayane Silva
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240901162744_DBO_INSERT_MODALIDADES_CONCORRENCIA_ELETRONICA_E_PRESENCIAL.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240901162744_ROLLBACK_DBO_INSERT_MODALIDADES_CONCORRENCIA_ELETRONICA_E_PRESENCIAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240901164556
      author: Rayane Silva
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240901164556_GARANTE_MODALIDADES_NOVAS_LEI14133.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240901164556_ROLLBACK_GARANTE_MODALIDADES_NOVAS_LEI14133.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240902084338
      author: Rayane Silva
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240902084338_DBO_INSERT_MODALIDADES_CONCORRENCIA_ELETRONICA_E_PRESENCIAL_LEI_OUTRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240902084338_ROLLBACK_DBO_INSERT_MODALIDADES_CONCORRENCIA_ELETRONICA_E_PRESENCIAL_LEI_OUTRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240926130000
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240926130000_ALTER_VIEW_PROCESSO_LICON_VIEW_LICITACAO_VALOR_ADJUDICADO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240926130000_ROLLBACK_ALTER_VIEW_PROCESSO_LICON_VIEW_LICITACAO_VALOR_ADJUDICADO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240919130043
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240919130043_DBO_ALTER_TIPO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240919130043_ROLLBACK_DBO_ALTER_TIPO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241008122412
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.32.0

  - changeSet:
      id: 20240903085118
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240903085118_DBO_CREATE_TDA_CREDENCIAMENTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240903085118_ROLLBACK_DBO_CREATE_TDA_CREDENCIAMENTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240903092358
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240903092358_DBO_ALTER_PROCEDURE_ALERTA_DF.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240903092358_ROLLBACK_DBO_ALTER_PROCEDURE_ALERTA_DF.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240903093509
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240903093509_DBO_ALTER_PROCEDURE_ALERTA_PROC.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240903093509_ROLLBACK_DBO_ALTER_PROCEDURE_ALERTA_PROC.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240903093858
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240903093858_DBO_ALTER_PROCEDURE_PROCESSOS_LIC.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240903093858_ROLLBACK_DBO_ALTER_PROCEDURE_PROCESSOS_LIC.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240904163134
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240904163134_DBO_CREATE_TDA_CREDENCIAMENTO_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240904163134_ROLLBACK_DBO_TDA_CREDENCIAMENTO_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240904090534
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240904090534_DBO_ALTER_VIEW_ANALISE_PROCESSO.sql
          relativeToChangelogFile: true
        rollback:
          sqlFile:
            encoding: UTF-8
            path: rollback_scripts/20240904090534_ROLLBACK_DBO_ALTER_VIEW_ANALISE_PROCESSO.sql
            relativeToChangelogFile: true

  - changeSet:
      id: 20240909142201
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240909142201_AUDITORIA_ADD_COLUMNS_CREDENCIAMENTO.sql
          relativeToChangelogFile: true
        rollback:
          sqlFile:
            encoding: UTF-8
            path: rollback_scripts/20240909142201_ROLLBACK_AUDITORIA_ADD_COLUMNS_CREDENCIAMENTO.sql
            relativeToChangelogFile: true

  - changeSet:
      id: 20240910141245
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240910141245_CREATE_TABLE_CHECKLIST_CRED.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240910141245_ROLLBACK_CREATE_TABLE_CHECKLIST_CRED.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240912152905
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240912152905_ALTER_VIEW_ANALISE_ALERTA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240912152905_ROLLBACK_ALTER_VIEW_ANALISE_ALERTA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240912153425
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240912153425_DBO_ADD_COLUMNS_ANALISE_ALERTA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240912153425_ROLLBACK_DBO_ADD_COLUMNS_ANALISE_ALERTA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240910091808
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240910091808_DBO_ALTER_ANALISE_PROCESSO_VIEW_ADD_TERMO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240910091808_ROLLBACK_DBO_ALTER_ANALISE_PROCESSO_VIEW_ADD_TERMO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241007145301
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241007145301_ALTER_VIEW_GERENCIAMENTO_TERMO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241007145301_ROLLBACK_ALTER_VIEW_GERENCIAMENTO_TERMO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241008174605
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241008174605_DBO_ALTER_TABLE_FUNDAMENTACAO_LEGAL_OUTRO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241008174605_ROLLBACK_DBO_ALTER_TABLE_FUNDAMENTACAO_LEGAL_OUTRO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240923180125
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240923180125_DBO_INSERT_PERMISSION_PAINEL_ATOS_LICITACOES.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240923180125_ROLLBACK_DBO_INSERT_PERMISSION_PAINEL_ATOS_LICITACOES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240925012125
      author: Everton Kauan
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240925012125_ROBOS_CREATE_VIEW_PAINEL_ATOS_LICITACOES_MATEIRO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240925012125_ROLLBACK_ROBOS_CREATE_VIEW_PAINEL_ATOS_LICITACOES_MATEIRO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241007101459
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241007101459_DBO_ALTER_VIEW_PROCESSO_LICON_VIEW_NUMERO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241007101459_ROLLBACK_DBO_ALTER_VIEW_PROCESSO_LICON_VIEW_NUMERO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20240930114109
      author: Lívia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240930114109_DBO_CRIA_GRUPO_AUDITORES_ATRIBUIDORES.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240930114109_ROLLBACK_DBO_CRIA_GRUPO_AUDITORES_ATRIBUIDORES.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241001112336
      author: Lívia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241001112336_DBO_ALTER_AUDITOR_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241001112336_ROLLBACK_DBO_ALTER_AUDITOR_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241003114958
      author: Lívia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241003114958_DBO_ATUALIZA_ITENS_BD_COM_COL_ATRIBUIDOR.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241003114958_ROLLBACK_DBO_ATUALIZA_ITENS_BD_COM_COL_ATRIBUIDOR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241004090156
      author: Lívia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241004090156_RENOMEIA_COLUNAS_MENSAGEM_INSPETOR.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241004090156_ROLLBACK_RENOMEIA_COLUNAS_MENSAGEM_INSPETOR.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241018091008
      author: Lívia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241018091008_DBO_UPDATE_COMISSAO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241018091008_ROLLBACK_DBO_UPDATE_COMISSAO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241021084115
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241021084115_CHANGE_MOTIVO_ARQUIVO_OBRIGATORIO_REABRIR_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241021084115_ROLLBACK_CHANGE_MOTIVO_ARQUIVO_OBRIGATORIO_REABRIR_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241016124924
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241016124924_DBO_ALTER_TABLES_PROCESSOS_ADD_UPDATED_AT_AUDIT_COLUMN.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241016124924_ROLLBACK_DBO_ALTER_TABLES_PROCESSOS_ADD_UPDATED_AT_AUDIT_COLUMN.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241017103418
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241017103418_DBO_ALTER_ANALISE_PROCESSO_VIEW_SORT_BY_UPDATED_AT_AUDIT.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241017103418_ROLLBACK_DBO_ALTER_ANALISE_PROCESSO_VIEW_SORT_BY_UPDATED_AT_AUDIT.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241024230849
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241024230849_UPDATE_STATUS_ALERTA_ANALISE.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241024230849_ROLLBACK_UPDATE_STATUS_ALERTA_ANALISE.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241024220630
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.33.0

  - changeSet:
      id: 20240826134910
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20240826134910_DBO_ADICIONA_COLUNA_PARTICIPACAO_EXCLUSIVA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20240826134910_DBO_ROLLBACK_ADICIONA_COLUNA_PARTICIPACAO_EXCLUSIVA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241007105827
      author: Lívia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241007105827_DBO_ALTER_CONTRATO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241007105827_ROLLBACK_DBO_ALTER_CONTRATO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241025083909
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241025083909_ALTER_PROC_GET_PROCESSOS_LICITATORIOS.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241025083909_ROLLBACK_ALTER_PROC_GET_PROCESSOS_LICITATORIOS.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241026212828
      author: Mirelle Maria
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241026212828_DBO_LICITANTE_ADICIONA_COLUNA_INTERCIONAL.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241026212828_ROLLBACK_DBO_LICITANTE_ADICIONA_COLUNA_INTERCIONAL.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241021095239
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241021095239_DBO_ALTER_TABLES_PROCESSOS_ADD_COLUMN_UPDATED_AT.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241021095239_ROLLBACK_DBO_ALTER_TABLES_PROCESSOS_ADD_COLUMN_UPDATED_AT.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241021130814
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241021130814_DBO_ALTER_VIEW_CARONA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241021130814_ROLLBACK_DBO_ALTER_VIEW_CARONA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241105084114
      author: Lucian Julio
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241105084114_ALTER_VIEW_ENTIDADE_INTERNA_EXTERNA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241105084114_ROLLBACK_ALTER_VIEW_ENTIDADE_INTERNA_EXTERNA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241025101811
      author: Erick Farias
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241025101811_DBO_ALTER_PROCESSO_LICON_VIEW_MODALIDADE_LEILAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241025101811_ROLLBACK_DBO_ALTER_PROCESSO_LICON_VIEW_MODALIDADE_LEILAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241021110314
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241021110314_DBO_UPDATE_ARQ_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241021110314_ROLLBACK_DBO_UPDATE_ARQ_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241022170247
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241022170247_DBO_UPDATE_ARQ_DISPENSA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241022220031
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241022220031_DBO_UPDATE_ARQ_INEXIGI.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241023093323
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241023093323_DBO_UPDATE_ARQ_CREDENCIAMENTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241027192735
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241027192735_DBO_UPDATE_ARQ_CARONA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241028150719
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241028150719_DBO_UPDATE_ARQ_CARONA_ORGAO_N_INT.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20230816192320_ROLLBACK_EMPTY.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241101144850
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241101144850_DBO_ALTER_TIPO_ARQUIVO_CARONA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241101144850_ROLLBACK_DBO_ALTER_TIPO_ARQUIVO_CARONA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241029222626
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241029222626_ALTER_TABLE_GEOOBRAS_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241029222626_ROLLBACK_ALTER_TABLE_GEOOBRAS_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241029222627
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241029222627_ALTER_TABLE_GEOOBRAS_OBRA_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241029222627_ROLLBACK_ALTER_TABLE_GEOOBRAS_OBRA_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241029222628
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241029222628_ALTER_VIEW_MAPA_OBRA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241029222628_ROLLBACK_ALTER_VIEW_MAPA_OBRA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241113083809
      author: Lucian Julio
      changes:
        - tagDatabase:
            tag: 2.34.0

  - changeSet:
      id: 20241001155240
      author: Lucas Khalil
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241001155240_ROBOS_ALTERAR_ATO_DIARIO_OFICIAL_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241001155240_ROLLBACK_ROBOS_ALTERAR_ATO_DIARIO_OFICIAL_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241104204017
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241104204017_GEOOBRAS_CREATE_TABLE_DIARIO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241104204017_ROLLBACK_GEOOBRAS_CREATE_TABLE_DIARIO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241104205526
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241104205526_AUDITORIA_CREATE_TABLE_DIARIO_OBRA_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241104205526_ROLLBACK_AUDITORIA_CREATE_TABLE_DIARIO_OBRA_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241104205635
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241104205635_GEOOBRAS_CREATE_TABLE_ARQUIVO_DIARIO_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241104205635_ROLLBACK_CREATE_TABLE_ARQUIVO_DIARIO_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241107150233
      author: Iasmim Torres
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241107150233_DBO_UPDATE_ARQUIVOS_CONTRATO_CREDENCIAMENTO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241107150233_ROLLBACK_DBO_UPDATE_ARQUIVOS_CONTRATO_CREDENCIAMENTO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241015095249
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241015095249_DBO_CREATE_TABLE_ANULACAO_REVOGACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241015095249_ROLLBACK_DBO_CREATE_TABLE_ANULACAO_REVOGACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241015104143
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241015104143_DBO_ALTER_PROCESSO_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241015104143_ROLLBACK_DBO_ALTER_PROCESSO_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241023083112
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241023083112_DBO_ALTER_CARONA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241023083112_ROLLBACK_DBO_ALTER_CARONA_VIEW.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241024111120
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241024111120_CREATE_ARQUIVOS_ANULACAO_REVOGACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241024111120_ROLLBACK_CREATE_ARQUIVOS_ANULACAO_REVOGACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241113140713
      author: Arthur Coelho
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241113140713_DBO_ALTER_VIEW_ALERTAS_AUDITORIA_VIEW.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241113140713_ROLLBACK_DBO_ALTER_VIEW_ALERTAS_AUDITORIA_VIEW.sql
          relativeToChangelogFile: true
          
  - changeSet:
      id: 20241114224403
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241114224403_GEOOBRAS_CREATE_TABLE_BOLETIM_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241114224403_ROLLBACK_GEOOBRAS_CREATE_TABLE_BOLETIM_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241114224405
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241114224405_AUDITORIA_CREATE_TABLE_BOLETIM_OBRA_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241114224405_ROLLBACK_AUDITORIA_CREATE_TABLE_BOLETIM_OBRA_AUD.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241114224410
      author: Samuel Vasconcelos
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241114224410_GEOOBRAS_CREATE_TABLE_ARQUIVO_BOLETIM_OBRA.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241114224410_ROLLBACK_CREATE_TABLE_ARQUIVO_BOLETIM_OBRA.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20241126101616
      author: Livia Aniely
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241126101616_DBO_DELETE_TABLE_MOD_LIC.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20250611093522
      author: Alexandre Ugalde
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241101123000_DBO_CREATE_TABLE_MODALIDADE_LICITACAO.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241101123000_ROLLBACK_DBO_CREATE_TABLE_MODALIDADE_LICITACAO.sql
          relativeToChangelogFile: true

  - changeSet:
      id: 20250611094150
      author: Alexandre Ugalde
      changes:
        sqlFile:
          encoding: UTF-8
          path: scripts/20241101123100_AUDITORIA_CREATE_TABLE_MODALIDADE_LICITACAO_AUD.sql
          relativeToChangelogFile: true
      rollback:
        sqlFile:
          encoding: UTF-8
          path: rollback_scripts/20241101123100_ROLLBACK_AUDITORIA_CREATE_TABLE_MODALIDADE_LICITACAO_AUD.sql
          relativeToChangelogFile: true
