#!/bin/bash
if [ -z ${REACT_APP_API_URL+x} ]; then 
  echo "Environment variable REACT_APP_API_URL not found."
  exit 1
elif [ -z ${RELATORIOS_FE_URL+x} ]; then 
  echo "Environment variable RELATORIOS_FE_URL not found."
  exit 1
elif [ -z ${WMS_API_URL+x} ]; then 
  echo "Environment variable WMS_API_URL not found."
  exit 1
elif [ -z ${WMS_LAYER+x} ]; then 
  echo "Environment variable WMS_LAYER not found."
  exit 1
elif [ -z ${SPRING_DATASOURCE_URL+x} ]; then 
  echo "Environment variable SPRING_DATASOURCE_URL not found."
  exit 1
elif [ -z ${SPRING_DATASOURCE_USERNAME+x} ]; then 
  echo "Environment variable SPRING_DATASOURCE_USERNAME not found."
  exit 1
elif [ -z ${SPRING_DATASOURCE_PASSWORD+x} ]; then 
  echo "Environment variable SPRING_DATASOURCE_PASSWORD not found."
  exit 1
elif [ -z ${APP_FILES_REPOSITORY_FULL_PATH+x} ]; then 
  echo "Environment variable APP_FILES_REPOSITORY_FULL_PATH not found."
  exit 1
elif [ -z ${SPRING_RABBITMQ_PASSWORD+x} ]; then 
  echo "Environment variable SPRING_RABBITMQ_PASSWORD not found."
  exit 1
elif [ -z ${SPRING_RABBITMQ_USERNAME+x} ]; then 
  echo "Environment variable SPRING_RABBITMQ_USERNAME not found."
  exit 1
elif [ -z ${SPRING_RABBITMQ_HOST+x} ]; then 
  echo "Environment variable SPRING_RABBITMQ_HOST not found."
  exit 1
elif [ -z ${SPRING_ELASTICSEARCH_HOST+x} ]; then 
  echo "Environment variable SPRING_ELASTICSEARCH_HOST not found."
  exit 1
elif [ -z ${SPRING_ELASTICSEARCH_USER+x} ]; then 
  echo "Environment variable SPRING_ELASTICSEARCH_USER not found."
  exit 1
elif [ -z ${SPRING_ELASTICSEARCH_PASSWORD+x} ]; then 
  echo "Environment variable SPRING_ELASTICSEARCH_PASSWORD not found."
  exit 1
elif [ -z ${SPRING_ELASTICSEARCH_INDEX+x} ]; then 
  echo "Environment variable SPRING_ELASTICSEARCH_INDEX not found."
  exit 1 
elif [ -z ${SPRING_ELASTICSEARCH_SEMANTICAPI+x} ]; then 
  echo "Environment variable SPRING_ELASTICSEARCH_SEMANTICAPI not found."
  exit 1
elif [ -z ${SPRING_ELASTICSEARCH_IDXSINAPI+x} ]; then 
  echo "Environment variable SPRING_ELASTICSEARCH_IDXSINAPI not found."
  exit 1
elif [ -z ${SPRING_ELASTICSEARCH_IDXSICRO+x} ]; then 
  echo "Environment variable SPRING_ELASTICSEARCH_IDXSICRO not found."
  exit 1
elif [ -z ${SPRING_GEOOBRAS_ACOMPANHAMENTO_PATH+x} ]; then 
  echo "Environment variable SPRING_GEOOBRAS_ACOMPANHAMENTO_PATH not found."
  exit 1
elif [ -z ${SPRING_GEOOBRAS_MASCARA+x} ]; then 
  echo "Environment variable SPRING_GEOOBRAS_MASCARA not found."
  exit 1
elif [ -z ${SPRING_GEOOBRAS_MINIO_ENDPOINT+x} ]; then 
  echo "Environment variable SPRING_GEOOBRAS_MINIO_ENDPOINT not found."
  exit 1
elif [ -z ${SPRING_GEOOBRAS_MINIO_PORT+x} ]; then 
  echo "Environment variable SPRING_GEOOBRAS_MINIO_PORT not found."
  exit 1
elif [ -z ${SPRING_GEOOBRAS_MINIO_ACCESS_KEY+x} ]; then 
  echo "Environment variable SPRING_GEOOBRAS_MINIO_ACCESS_KEY not found."
  exit 1
elif [ -z ${SPRING_GEOOBRAS_MINIO_SECRET_KEY+x} ]; then 
  echo "Environment variable SPRING_GEOOBRAS_MINIO_SECRET_KEY not found."
  exit 1
elif [ -z ${SPRING_GEOOBRAS_MINIO_BUCKET+x} ]; then 
  echo "Environment variable SPRING_GEOOBRAS_MINIO_BUCKET not found."
  exit 1
elif [ -z ${SPRING_CJURAPI_URL+x} ]; then 
  echo "Environment variable SPRING_CJURAPI_URL not found."
  exit 1
fi

target_env="homolog"
if [ "$TARGET_ENV" == "staging" ]; then
  target_env="staging"
elif [ "$TARGET_ENV" == "production" ]; then
  target_env="production"
fi

if [ "$TARGET_ENV" == "staging" ] && [ "$CI_COMMIT_BRANCH" == "main" ]; then
  target_branch="staging"
  source_branch="main"

  echo "git config http.sslVerify false"
  git config http.sslVerify false

  echo "bash /git-set-url-credentials.sh"
  bash /git-set-url-credentials.sh

  echo "git fetch origin ${source_branch}"
  git fetch origin ${source_branch}

  echo "git fetch origin ${target_branch}"
  git fetch origin ${target_branch}

  commits_diffs=$(git diff --name-only origin/${source_branch} origin/${target_branch} 2>&1)
  if [[ -z "${commits_diffs// }" ]]; then
    echo "No commits since last deploy."
    echo "Deploy is not necessary."
    exit 0
  fi

  echo "git checkout -q ${source_branch}"
  git checkout -q ${source_branch}

  echo "git pull -q origin ${source_branch}"
  git pull -q origin ${source_branch}

  echo "git checkout -q ${target_branch}"
  git checkout -q ${target_branch}

  echo "git pull -q origin ${target_branch}"
  git pull -q origin ${target_branch}

  echo "git merge -q --no-edit origin/${source_branch}"
  git merge -q --no-edit origin/${source_branch}

  echo "git config --global --add safe.directory /novo-licon/frontend/src/frontend-components"
  git config --global --add safe.directory /novo-licon/frontend/src/frontend-components

  echo "git submodule update --init --recursive --force"
  git submodule update --init --recursive --force

  echo "git push origin ${target_branch}"
  git push origin ${target_branch}
fi

touch ./frontend/${target_env}.env
echo REACT_APP_API_URL=$REACT_APP_API_URL > ./frontend/${target_env}.env
echo RELATORIOS_FE_URL=$RELATORIOS_FE_URL >> ./frontend/${target_env}.env
echo WMS_API_URL=$WMS_API_URL >> ./frontend/${target_env}.env
echo WMS_LAYER=$WMS_LAYER >> ./frontend/${target_env}.env
touch ./frontend/.env
echo REACT_APP_API_URL=$REACT_APP_API_URL > ./frontend/.env
echo RELATORIOS_FE_URL=$RELATORIOS_FE_URL >> ./frontend/.env
echo WMS_API_URL=$WMS_API_URL >> ./frontend/.env
echo WMS_LAYER=$WMS_LAYER >> ./frontend/.env

touch ./backend/${target_env}.env
echo SPRING_DATASOURCE_URL=$SPRING_DATASOURCE_URL > ./backend/${target_env}.env
echo SPRING_DATASOURCE_USERNAME=$SPRING_DATASOURCE_USERNAME >> ./backend/${target_env}.env
echo SPRING_DATASOURCE_PASSWORD=$SPRING_DATASOURCE_PASSWORD >> ./backend/${target_env}.env
echo APP_FILES_REPOSITORY_FULL_PATH=$APP_FILES_REPOSITORY_FULL_PATH >> ./backend/${target_env}.env
echo SPRING_RABBITMQ_PASSWORD=$SPRING_RABBITMQ_PASSWORD >> ./backend/${target_env}.env
echo SPRING_RABBITMQ_USERNAME=$SPRING_RABBITMQ_USERNAME >> ./backend/${target_env}.env
echo SPRING_RABBITMQ_HOST=$SPRING_RABBITMQ_HOST >> ./backend/${target_env}.env
echo SPRING_ELASTICSEARCH_HOST=$SPRING_ELASTICSEARCH_HOST >> ./backend/${target_env}.env
echo SPRING_ELASTICSEARCH_USER=$SPRING_ELASTICSEARCH_USER >> ./backend/${target_env}.env
echo SPRING_ELASTICSEARCH_PASSWORD=$SPRING_ELASTICSEARCH_PASSWORD >> ./backend/${target_env}.env
echo SPRING_ELASTICSEARCH_INDEX=$SPRING_ELASTICSEARCH_INDEX >> ./backend/${target_env}.env
echo SPRING_ELASTICSEARCH_SEMANTICAPI=$SPRING_ELASTICSEARCH_SEMANTICAPI >> ./backend/${target_env}.env
echo SPRING_ELASTICSEARCH_IDXSINAPI=$SPRING_ELASTICSEARCH_IDXSINAPI >> ./backend/${target_env}.env
echo SPRING_ELASTICSEARCH_IDXSICRO=$SPRING_ELASTICSEARCH_IDXSICRO >> ./backend/${target_env}.env
echo SPRING_GEOOBRAS_ACOMPANHAMENTO_PATH=$SPRING_GEOOBRAS_ACOMPANHAMENTO_PATH >> ./backend/${target_env}.env
echo SPRING_GEOOBRAS_MASCARA=$SPRING_GEOOBRAS_MASCARA >> ./backend/${target_env}.env
echo SPRING_GEOOBRAS_MINIO_ENDPOINT=$SPRING_GEOOBRAS_MINIO_ENDPOINT >> ./backend/${target_env}.env
echo SPRING_GEOOBRAS_MINIO_PORT=$SPRING_GEOOBRAS_MINIO_PORT >> ./backend/${target_env}.env
echo SPRING_GEOOBRAS_MINIO_ACCESS_KEY=$SPRING_GEOOBRAS_MINIO_ACCESS_KEY >> ./backend/${target_env}.env
echo SPRING_GEOOBRAS_MINIO_SECRET_KEY=$SPRING_GEOOBRAS_MINIO_SECRET_KEY >> ./backend/${target_env}.env
echo SPRING_GEOOBRAS_MINIO_BUCKET=$SPRING_GEOOBRAS_MINIO_BUCKET >> ./backend/${target_env}.env
echo SPRING_CJURAPI_URL=$SPRING_CJURAPI_URL >> ./backend/${target_env}.env

echo "touch deploy_can_proceed"
touch deploy_can_proceed
